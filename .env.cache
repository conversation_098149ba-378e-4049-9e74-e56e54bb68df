# Environment configuration for LLM Cache testing
# Copy from .env and add Redis configuration

# Database Configuration
DATABASE_URL=**************************************************************************/postgres

# BGE Embedding Server
BGE_SERVER_URL=http://************:8080
BGE_TIMEOUT=30
USE_DIRECT_EMBEDDING=false

# LLM Configuration (use your existing values)
LLM_PROVIDER=openrouter
LLM_CHOICE=anthropic/claude-3.5-sonnet
LLM_API_KEY=your_api_key_here
LLM_BASE_URL=https://openrouter.ai/api/v1

# Redis Cache Configuration
REDIS_URL=redis://localhost:6379/0

# Cache Performance Settings
CACHE_PERFORMANCE_MONITORING=true
CACHE_DEFAULT_TTL_HOURS=24
CACHE_MAX_MEMORY_ENTRIES=10000

# MCP Server Configuration
HOST=0.0.0.0
PORT=8050
TRANSPORT=sse

# User ID for testing
MCP_USER_ID=cache_test_user