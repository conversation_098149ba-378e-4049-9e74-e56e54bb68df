# Docker Compose configuration for LLM Cache testing
# Extends the main development setup with Redis for cache performance testing

version: '3.8'

services:
  # Extend existing spark-memory service with cache configuration
  spark-mcp-dev:
    extends:
      file: docker-compose.dev.yml
      service: spark-mcp-dev
    environment:
      # Add Redis configuration
      - REDIS_URL=redis://redis:6379/0
      # Enable cache performance monitoring
      - CACHE_PERFORMANCE_MONITORING=true
      # Cache configuration
      - CACHE_DEFAULT_TTL_HOURS=24
      - CACHE_MAX_MEMORY_ENTRIES=10000
    depends_on:
      - redis
      - postgres-dev
    networks:
      - spark-dev-network

  # Redis for LLM response caching
  redis:
    image: redis:7-alpine
    container_name: spark-redis
    ports:
      - "6379:6379"
    command: redis-server --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - spark-dev-network

  # Extend PostgreSQL service
  postgres-dev:
    extends:
      file: docker-compose.dev.yml
      service: postgres-dev
    networks:
      - spark-dev-network

  # Extend pgAdmin service
  pgadmin:
    extends:
      file: docker-compose.dev.yml
      service: pgadmin
    networks:
      - spark-dev-network

volumes:
  redis_data:
    driver: local
  postgres_dev_data:
    name: spark_postgres_dev_data
  pgadmin_data:
    name: spark_pgadmin_data

networks:
  spark-dev-network:
    driver: bridge
    name: spark-dev-network