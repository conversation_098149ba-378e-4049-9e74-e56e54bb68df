FROM python:3.12-slim

WORKDIR /app

# Install test dependencies
RUN apt-get update && \
    apt-get install -y \
    curl \
    git \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install uv
RUN pip install uv

# Copy requirements
COPY pyproject.toml .
COPY uv.lock* .

# Install dependencies including test requirements
RUN python -m venv .venv
RUN uv pip install -e . && \
    uv pip install \
    pytest \
    pytest-asyncio \
    pytest-cov \
    pytest-xdist \
    pytest-timeout \
    httpx \
    faker

# Set Python path
ENV PYTHONPATH=/app:$PYTHONPATH

# Default test command
CMD ["pytest", "-v"]
