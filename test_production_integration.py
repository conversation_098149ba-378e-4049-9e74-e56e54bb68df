#!/usr/bin/env python3
"""
Production integration test for the Spark Memory MCP server.

This test validates that the production container is working correctly
and all core functionality is operational.
"""

import asyncio
import json
import sys
import os
import time
import httpx
from typing import Dict, Any

# Add src directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))


class ProductionIntegrationTest:
    """Production integration test suite."""
    
    def __init__(self):
        self.base_url = "http://localhost:8050"
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def test_server_health(self):
        """Test that the server is healthy and responding."""
        print("Testing server health...")
        
        try:
            response = await self.client.get(f"{self.base_url}/sse")
            print(f"✅ Server responding: HTTP {response.status_code}")
            return response.status_code == 200
        except Exception as e:
            print(f"❌ Server health check failed: {e}")
            return False
    
    async def test_bge_connectivity(self):
        """Test BGE embedding server connectivity."""
        print("Testing BGE connectivity...")
        
        try:
            # Test BGE server directly
            bge_response = await self.client.get("http://************:8080/health")
            if bge_response.status_code == 200:
                print("✅ BGE server is accessible")
                return True
            else:
                print(f"❌ BGE server returned: {bge_response.status_code}")
                return False
        except Exception as e:
            print(f"❌ BGE connectivity failed: {e}")
            return False
    
    async def test_database_connectivity(self):
        """Test database connectivity by checking container logs."""
        print("Testing database connectivity...")
        
        try:
            # Check container logs for database initialization
            import subprocess
            result = subprocess.run(
                ["docker", "logs", "spark-mcp-server", "--tail", "20"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            logs = result.stdout + result.stderr
            
            if "Database service initialized successfully" in logs:
                print("✅ Database connectivity confirmed")
                return True
            else:
                print("❌ Database initialization not found in logs")
                print(f"Recent logs: {logs[-500:]}")
                return False
                
        except Exception as e:
            print(f"❌ Database connectivity test failed: {e}")
            return False
    
    async def test_memory_pipeline_components(self):
        """Test that memory pipeline components are initialized."""
        print("Testing memory pipeline components...")
        
        try:
            import subprocess
            result = subprocess.run(
                ["docker", "logs", "spark-mcp-server", "--tail", "50"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            logs = result.stdout + result.stderr
            
            required_components = [
                "Initialized memory database",
                "Initialized LLM client",
                "Initialized rolling summary manager",
                "Initialized performance monitor",
                "Initialized MemoryExtractionModule",
                "Initialized MemoryUpdateModule",
                "Initialized two-phase memory pipeline"
            ]
            
            missing_components = []
            for component in required_components:
                if component not in logs:
                    missing_components.append(component)
            
            if not missing_components:
                print("✅ All memory pipeline components initialized")
                return True
            else:
                print(f"❌ Missing components: {missing_components}")
                return False
                
        except Exception as e:
            print(f"❌ Memory pipeline test failed: {e}")
            return False
    
    async def test_service_readiness(self):
        """Test that the service is marked as fully ready."""
        print("Testing service readiness...")
        
        try:
            import subprocess
            result = subprocess.run(
                ["docker", "logs", "spark-mcp-server", "--tail", "30"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            logs = result.stdout + result.stderr
            
            readiness_indicators = [
                "Service marked as fully available",
                "MCP protocol initialization complete",
                "service now fully available"
            ]
            
            for indicator in readiness_indicators:
                if indicator in logs:
                    print("✅ Service is fully ready")
                    return True
            
            print("❌ Service readiness not confirmed")
            print(f"Recent logs: {logs[-300:]}")
            return False
                
        except Exception as e:
            print(f"❌ Service readiness test failed: {e}")
            return False
    
    async def test_container_health_check(self):
        """Test that Docker health check is passing."""
        print("Testing container health check...")
        
        try:
            import subprocess
            result = subprocess.run(
                ["docker", "ps", "--filter", "name=spark-mcp-server", "--format", "table {{.Names}}\t{{.Status}}"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            output = result.stdout
            
            if "healthy" in output:
                print("✅ Container health check is passing")
                return True
            elif "unhealthy" in output:
                print("❌ Container health check is failing")
                return False
            else:
                print(f"⚠️ Container health status unclear: {output}")
                return False
                
        except Exception as e:
            print(f"❌ Container health check test failed: {e}")
            return False
    
    async def run_all_tests(self):
        """Run all production integration tests."""
        tests = [
            ("Server Health", self.test_server_health),
            ("BGE Connectivity", self.test_bge_connectivity),
            ("Database Connectivity", self.test_database_connectivity),
            ("Memory Pipeline Components", self.test_memory_pipeline_components),
            ("Service Readiness", self.test_service_readiness),
            ("Container Health Check", self.test_container_health_check),
        ]
        
        results = []
        for test_name, test_func in tests:
            try:
                print(f"\n{'='*50}")
                print(f"Running {test_name}...")
                print(f"{'='*50}")
                
                success = await test_func()
                results.append((test_name, success))
                
                status = "PASSED" if success else "FAILED"
                emoji = "✅" if success else "❌"
                print(f"{emoji} {test_name}: {status}")
                
            except Exception as e:
                print(f"❌ {test_name} failed with exception: {e}")
                import traceback
                traceback.print_exc()
                results.append((test_name, False))
        
        return results
    
    async def cleanup(self):
        """Cleanup resources."""
        await self.client.aclose()


async def main():
    """Main test runner."""
    print("🧪 Running Production Integration Tests")
    print("=" * 80)
    
    test_suite = ProductionIntegrationTest()
    
    try:
        results = await test_suite.run_all_tests()
        
        # Summary
        print("\n" + "=" * 80)
        print("PRODUCTION INTEGRATION TEST SUMMARY")
        print("=" * 80)
        
        passed = 0
        failed = 0
        
        for test_name, result in results:
            status = "PASSED" if result else "FAILED"
            emoji = "✅" if result else "❌"
            print(f"{emoji} {test_name}: {status}")
            
            if result:
                passed += 1
            else:
                failed += 1
        
        print(f"\nTotal Results: {passed} PASSED, {failed} FAILED")
        print(f"Success Rate: {passed}/{passed + failed} ({100 * passed / (passed + failed):.1f}%)")
        
        if failed == 0:
            print("\n🎉 ALL PRODUCTION TESTS PASSED!")
            print("✅ Production container is working correctly!")
            print("✅ All core components are operational!")
            print("✅ Service is ready for production use!")
            return 0
        else:
            print(f"\n❌ {failed} PRODUCTION TEST(S) FAILED!")
            print("Please check the error details above and fix the issues.")
            return 1
            
    finally:
        await test_suite.cleanup()


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
