---
type: "agent_requested"
description: "Project Overview"
---
# Spark Memory MCP Server - Claude Code Development Guide

## Project Overview

This is a **Spark Memory MCP Server** project that implements intelligent memory management based on Mem0's two-phase architecture. It's designed for a two-person team running locally/remotely, focusing on reliability over enterprise features.

**Key Technologies:**
- **Memory Pipeline**: Two-phase (extraction → update) based on Mem0 research
- **Embeddings**: BGE server (BAAI/bge-base-en-v1.5) with CLS pooling
- **Storage**: PostgreSQL with pgvector extension
- **Interface**: MCP (Model Context Protocol) for AI integration
- **External Services**: Supabase for production storage

## Critical Environment Details

### Server Location
- **Project Path**: `/home/<USER>/dev/tools/mcp-mem0`
- **Ubuntu Server IP**: `************`
- **BGE Server**: Running in separate container at `http://************:8080`
- **Supabase**: External instance at `*************:8000`

### Important URLs to Update
When working with this project, ensure these URLs are correct:
```yaml
# In docker-compose.dev.yml and .env files:
BGE_SERVER_URL: http://************:8080  # NOT host.docker.internal
DATABASE_URL: **************************************************************************/postgres
```

## Development Environment Setup

### Prerequisites
1. Docker and Docker Compose installed
2. Access to the Ubuntu server at ************
3. BGE embedding server running at port 8080
4. Git Bash or WSL for running shell scripts on Windows

### Initial Setup
```bash
# 1. Navigate to project directory
cd /home/<USER>/dev/tools/mcp-mem0

# 2. Make scripts executable
chmod +x setup-dev.sh dev.sh

# 3. Create .env from example
cp .env.example .env

# 4. Update .env with correct values:
# - Set BGE_SERVER_URL=http://************:8080
# - Add your LLM API keys
# - Verify DATABASE_URL points to correct Supabase instance

# 5. Run setup
./setup-dev.sh
```

### Starting Development Server
```bash
# Start with hot reload (recommended)
./dev.sh start

# Or start detached
./dev.sh start-d

# View logs
./dev.sh logs
```

## Project Structure

```
/home/<USER>/dev/tools/mcp-mem0/
├── src/                    # Source code (hot-reloaded)
│   ├── main_new.py        # Main MCP server entry
│   ├── main_dev.py        # Development wrapper for hot reload
│   ├── memory_database.py # Database operations
│   ├── memory_extraction.py # Phase 1: Extract memories
│   ├── memory_update.py   # Phase 2: Update/merge memories
│   ├── bge_embedding_client.py # BGE server client
│   └── supabase_mcp_integration.py # Supabase MCP
├── tests/
│   ├── phase_1/           # Phase 1 test suite
│   ├── phase_2/           # Phase 2 test suite
│   ├── phase_3/           # Phase 3 test suite
│   └── phase_4/           # Phase 4 test suite
├── docker-compose.yml     # Production config
├── docker-compose.dev.yml # Development config (USE THIS)
├── Dockerfile             # Production image
├── Dockerfile.dev         # Development image
└── dev.sh                 # Development helper script
```

## Development Workflow

### 1. Making Code Changes
- Edit files in `src/` directory
- Changes auto-reload without container rebuild
- Check logs: `./dev.sh logs`

### 2. Running Tests
```bash
# Test current phase implementation
./dev.sh test-phase 1  # For Phase 1
./dev.sh test-phase 2  # For Phase 2
# etc.

# Run all tests
./dev.sh test

# Run specific test types
./dev.sh test-unit
./dev.sh test-integration
```

### 3. Database Management
```bash
# Access PostgreSQL shell
./dev.sh db-shell

# Common queries:
SELECT COUNT(*) FROM memories;
SELECT * FROM memories WHERE user_id = 'test' LIMIT 10;
\d memories  # Show table structure

# Access pgAdmin UI
# http://localhost:5050
# Login: <EMAIL> / admin
```

### 4. Performance Monitoring
```bash
# Check performance stats
curl http://localhost:8050/tools/get_performance_stats

# Health check
curl http://localhost:8050/health
```

## Implementation Phases

### Current Phase Status
- [ ] **Phase 1**: Critical Fixes (Semantic Search, Latency, Conflicts)
- [ ] **Phase 2**: Memory Intelligence (Evolution, Graph, Context)
- [ ] **Phase 3**: User Experience (Search, Management, Dashboard)
- [ ] **Phase 4**: Polish & Reliability (Error Handling, Optimization)

### Phase 1 Tasks (Current Focus)
1. **Fix Semantic Search**
   - Files: `src/bge_embedding_client.py`, `src/memory_database.py`
   - Test: `./dev.sh test-phase 1`
   
2. **Improve P95 Latency**
   - Add database indexes
   - Implement connection pooling
   - Target: <1 second
   
3. **Basic Conflict Resolution**
   - File: `src/memory_update.py`
   - Similarity threshold: 0.7

## Common Issues & Solutions

### BGE Server Connection
```bash
# If BGE server not accessible:
# 1. Verify it's running on Ubuntu server
docker ps | grep bge

# 2. Test connectivity
curl http://************:8080/health

# 3. Update docker-compose.dev.yml if needed
BGE_SERVER_URL: http://************:8080
```

### Hot Reload Not Working
```bash
# 1. Check volume mounts
docker-compose -f docker-compose.dev.yml config

# 2. Restart development server
./dev.sh restart

# 3. Check file permissions
ls -la src/
```

### Database Issues
```bash
# Reset development database
docker-compose -f docker-compose.dev.yml down -v
./setup-dev.sh
```

## Code Quality Standards

### Before Committing
```bash
# Format code
./dev.sh format

# Lint code
./dev.sh lint

# Run tests
./dev.sh test
```

### Python Style
- Use type hints
- Add docstrings for all functions
- Follow PEP 8
- Keep functions under 50 lines

## MCP Tool Endpoints

For testing MCP tools directly:
```bash
# Add memory
curl -X POST http://localhost:8050/tools/add_memories \
  -H "Content-Type: application/json" \
  -d '{"text": "Test memory", "user_id": "test"}'

# Search memory
curl -X POST http://localhost:8050/tools/search_memory \
  -H "Content-Type: application/json" \
  -d '{"query": "test", "user_id": "test"}'

# Get performance stats
curl -X POST http://localhost:8050/tools/get_performance_stats
```

## Important Notes

1. **Never use `host.docker.internal`** - Always use actual IP addresses
2. **BGE Server**: Must be running at `http://************:8080`
3. **Hot Reload**: Only works for Python files in `src/`
4. **Database**: Development uses local PostgreSQL, production uses Supabase
5. **Logs**: Check `./logs/` directory for detailed logs

## Quick Reference

```bash
# Most common commands
./dev.sh start          # Start development
./dev.sh logs          # View logs
./dev.sh test-phase 1  # Test current phase
./dev.sh shell         # Debug in container
./dev.sh stop          # Stop everything

# Performance check
curl http://localhost:8050/tools/get_performance_stats | jq
```

## Contact & Support

- Project location: Ubuntu server at ************
- BGE embedding server: Port 8080
- Development PostgreSQL: Port 5433
- pgAdmin: Port 5050
- MCP Server: Port 8050

---

**Remember**: This is a LOCAL TEAM PROJECT - avoid over-engineering. Focus on reliability and simplicity over enterprise features. You are equipped with powerful local RAG knowledge base with spark-rag mcp server where you can access all the knowledge sources and code examples."
