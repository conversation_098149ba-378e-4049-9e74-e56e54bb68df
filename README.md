# Spark - Self-Evolved Memory MCP Server

**Spark** is a Model Context Protocol (MCP) server that provides AI agents with persistent, semantic memory capabilities using a research-based two-phase architecture. Built for reliability and simplicity, designed as a second brain for development teams.

## 🧠 Features

- **Two-Phase Memory Pipeline**: Evidence-based extraction and update system from Mem0 research
- **Multi-User Memory Isolation**: Separate memory spaces for different users, projects, or contexts
- **BGE Semantic Search**: BAAI/bge-base-en-v1.5 embeddings with PostgreSQL+pgvector
- **Rolling Conversation Context**: Intelligent conversation summarization for better memory extraction
- **Performance Monitoring**: LOCOMO benchmark tracking with comprehensive metrics
- **Flexible User Management**: Support for user-based, project-based, and hierarchical memory organization

## 🚀 Quick Start

### Prerequisites

- Python 3.12+
- PostgreSQL with pgvector extension (via Supabase)
- BGE Embedding Server (running on port 8080)
- LLM Provider (OpenAI, OpenRouter, Ollama, or Mock)

### Installation

#### Development Setup (Recommended)
1. **Clone and setup**:
```bash
git clone <repository-url>
cd mcp-mem0
chmod +x setup-dev.sh dev.sh
```

2. **Configure environment**:
```bash
cp .env.example .env
# Edit .env with your settings (see Configuration section)
```

3. **Run development setup**:
```bash
./setup-dev.sh
```

4. **Start development server**:
```bash
# Start with hot reload
./dev.sh start

# View logs
./dev.sh logs
```

5. **Test the installation**:
```bash
./dev.sh test-phase 1
```

#### Production Setup
1. **Install dependencies**:
```bash
pip install -e .
```

2. **Start the server**:
```bash
cd src && python main_new.py
```

3. **Test with Docker**:
```bash
docker-compose up --build
```

### Configuration

Key environment variables in `.env`:

```bash
# ==========================================
# MCP Server Configuration
# ==========================================
TRANSPORT=sse                       # MCP transport: sse or stdio
HOST=0.0.0.0                       # Server host binding
PORT=8050                          # Server port

# ==========================================
# LLM Configuration
# ==========================================
LLM_PROVIDER=openrouter            # LLM provider: openai, openrouter, ollama, mock
LLM_BASE_URL=https://openrouter.ai/api/v1
LLM_API_KEY=your-api-key-here
LLM_CHOICE=google/gemini-2.5-flash-lite

# ==========================================
# BGE Embedding Configuration
# ==========================================
BGE_SERVER_URL=http://localhost:8080   # BGE embedding server endpoint

# ==========================================
# Database Configuration
# ==========================================
DATABASE_URL=postgresql://user:pass@host:port/database

# ==========================================
# User ID Configuration
# ==========================================
MCP_USER_ID=user                   # Default user ID for memory operations
# Examples:
# MCP_USER_ID=aung-dev              # Personal development
# MCP_USER_ID=project:web-app       # Project-based memory
# MCP_USER_ID=team:backend          # Team-shared memory
```

### MCP Client Configuration

Add the Spark Memory server to your Claude Code MCP configuration:

#### Basic Configuration
```json
{
  "spark-memory": {
    "command": "npx",
    "args": [
      "-y",
      "mcp-remote",
      "http://************:8050/sse/",
      "--allow-http"
    ],
    "env": {
      "MCP_USER_ID": "aung-dev"
    }
  }
}
```

#### Project-Based Configuration
```json
{
  "spark-memory": {
    "command": "npx",
    "args": [
      "-y",
      "mcp-remote",
      "http://your-server-ip:8050/sse/",
      "--allow-http"
    ],
    "env": {
      "MCP_USER_ID": "user:aung-dev:project:mcp-mem0"
    }
  }
}
```

#### Multiple Context Configuration
```json
{
  "spark-memory-dev": {
    "command": "npx",
    "args": [
      "-y",
      "mcp-remote",
      "http://your-server-ip:8050/sse/",
      "--allow-http"
    ],
    "env": {
      "MCP_USER_ID": "aung-dev:development"
    }
  },
  "spark-memory-prod": {
    "command": "npx", 
    "args": [
      "-y",
      "mcp-remote",
      "http://your-server-ip:8050/sse/",
      "--allow-http"
    ],
    "env": {
      "MCP_USER_ID": "aung-dev:production"
    }
  }
}
```

### User ID Patterns

The system supports flexible user ID patterns for different use cases:

| Pattern | Example | Use Case |
|---------|---------|----------|
| **Simple User** | `aung-dev` | Personal development work |
| **Project-Based** | `project:web-app-redesign` | Project-specific memories |
| **User + Project** | `user:aung-dev:project:api-v2` | Personal notes within projects |
| **Team-Based** | `team:backend-developers` | Shared team knowledge |
| **Hierarchical** | `team:backend:project:api:feature:auth` | Complex organizational structure |
| **Session-Based** | `session:20241124-143022` | Temporary isolated contexts |

## 📖 Documentation

- **[INITIAL.md](INITIAL.md)** - Project overview and development context
- **[docs/USER_MANUAL.md](docs/USER_MANUAL.md)** - User guide and evolution system
- **[docs/TECHNICAL_SPECIFICATION.md](docs/TECHNICAL_SPECIFICATION.md)** - Complete technical documentation
- **[docs/API_REFERENCE.md](docs/API_REFERENCE.md)** - MCP tools and web API reference

## 🔧 Usage

### MCP Tools Available

The server provides 6 MCP tools for memory operations:

#### 1. Add Memories
```json
{
  "name": "add_memories",
  "arguments": {
    "text": "Our team uses PostgreSQL 15 with pgvector for semantic search",
    "user_id": "aung-dev"
  }
}
```

#### 2. Search Memories
```json
{
  "name": "search_memory",
  "arguments": {
    "query": "database configuration",
    "user_id": "aung-dev",
    "limit": 5
  }
}
```

#### 3. List All Memories
```json
{
  "name": "list_memories",
  "arguments": {
    "user_id": "aung-dev",
    "limit": 50
  }
}
```

#### 4. Delete All Memories
```json
{
  "name": "delete_all_memories",
  "arguments": {
    "user_id": "aung-dev"
  }
}
```

#### 5. Get Performance Stats
```json
{
  "name": "get_performance_stats",
  "arguments": {}
}
```

#### 6. Get Conversation Summary
```json
{
  "name": "get_conversation_summary",
  "arguments": {
    "user_id": "aung-dev"
  }
}
```

### Claude Code Integration

When using with Claude Code, the `user_id` parameter is optional if you've configured `MCP_USER_ID` in your MCP server configuration:

```bash
# Claude Code usage examples
/memories add "Team decided to use React Query for state management"
/memories search "state management decisions"
/memories list
```

### Conflict Resolution

1. Access web dashboard at `http://your-ip:8080`
2. Review pending conflicts
3. Choose resolution: Keep A, Keep B, Merge, or Mark as Related
4. System automatically updates memory store

## 🏗️ Architecture

```
MCP Client ◄──► Spark Memory Server ◄──► BGE Embedding Server
    │                    │                        │
    │                    ▼                        │
    │         Memory Extraction Module            │
    │                    │                        │
    │                    ▼                        │
    │          Memory Update Module ◄─────────────┘
    │                    │
    │                    ▼
    │              Memory Database
    │             (PostgreSQL+pgvector)
    │                    │
    │                    ▼
    │           Rolling Summary Module
    │                    │
    │                    ▼
    └──────► Performance Monitor (LOCOMO)
```

### Two-Phase Memory Pipeline

The system implements an evidence-based architecture from Mem0 research:

#### Phase 1: Memory Extraction
- Processes conversation messages with context
- Uses LLM to extract meaningful memories
- Incorporates rolling conversation summaries
- Considers recent message history

#### Phase 2: Memory Updates  
- Makes intelligent operation decisions (ADD/UPDATE/DELETE/NOOP)
- Performs vector similarity search using BGE embeddings
- Resolves conflicts with 0.7 similarity threshold
- Updates existing memories or creates new ones

### User Isolation & Multi-Tenancy

- **Database Level**: All queries filtered by `user_id`
- **Memory Spaces**: Complete isolation between users
- **Conversation Context**: Separate rolling summaries per user
- **Performance Tracking**: Per-user usage statistics

## 🧪 Testing

### Development Testing
```bash
# Run phase-specific tests
./dev.sh test-phase 1    # Critical fixes
./dev.sh test-phase 2    # Memory intelligence
./dev.sh test-phase 3    # User experience
./dev.sh test-phase 4    # Polish & reliability

# Run all tests
./dev.sh test

# Run specific test types
./dev.sh test-unit
./dev.sh test-integration

# Test individual components
./dev.sh shell
# Inside container:
python -c "from bge_embedding_client import BGEEmbeddingClient; import asyncio; asyncio.run(BGEEmbeddingClient().health_check())"
```

### Production Testing
```bash
# Run comprehensive two-phase pipeline test
python tests/test_two_phase_pipeline.py

# Test server startup
cd src && timeout 10s python main_new.py

# Run with Docker
docker build -t spark-mcp . && docker run -p 8050:8050 --env-file .env spark-mcp
```

## 📁 Project Structure

```
mcp-mem0/
├── README.md                         # This file
├── CLAUDE.md                         # Development guide and principles
├── DEVELOPMENT.md                    # Development setup and workflows
├── .env.example                      # Environment template
├── Dockerfile                        # Production container
├── Dockerfile.dev                    # Development container with hot reload
├── docker-compose.yml                # Production Docker compose
├── docker-compose.dev.yml            # Development Docker compose
├── dev.sh                           # Development helper script
├── setup-dev.sh                     # Development environment setup
├── src/                             # Source code (hot-reloaded in dev)
│   ├── main_new.py                   # Production MCP server entry point
│   ├── main_dev.py                   # Development wrapper with hot reload
│   ├── config.py                     # Configuration management
│   ├── exceptions.py                 # Custom exception classes
│   ├── memory_handlers.py            # Memory operation handlers
│   ├── server_readiness.py           # Server health and readiness checks
│   ├── bge_embedding_client.py       # BGE embedding service client
│   ├── memory_database.py            # PostgreSQL+pgvector database layer
│   ├── memory_extraction.py          # Phase 1: Memory extraction module
│   ├── memory_update.py              # Phase 2: Memory update module
│   ├── rolling_summary.py            # Conversation context management
│   ├── performance_monitor.py        # Performance monitoring and metrics
│   ├── supabase_mcp_integration.py   # Supabase MCP integration
│   ├── cache_performance_monitor.py  # Cache performance tracking
│   ├── llm_cache_service.py          # LLM response caching
│   ├── enhanced_database_service.py  # Enhanced database operations
│   └── utils.py                      # Utility functions
├── tests/                           # Test suite (organized by phase)
│   ├── phase_1/                     # Phase 1 critical fixes tests
│   ├── integration/                 # Integration tests
│   ├── unit/                        # Unit tests
│   ├── mocks/                       # Mock services for testing
│   └── test_*.py                    # Individual test files
├── scripts/                         # Utility and setup scripts
│   ├── init_db.sql                  # Database initialization
│   ├── setup_enhanced_db.sh         # Enhanced database setup
│   └── migrate_database.py          # Database migration tools
├── docs/                            # Documentation
│   ├── DOCKER_SETUP.md             # Docker setup guide
│   └── e2e-testing-report.md        # End-to-end testing report
├── docker/                          # Docker configurations
│   └── configs/                     # Specialized Docker configs
│       ├── docker-compose.cache.yml # Cache testing configuration
│       └── Dockerfile.test          # Testing container
├── archive/                         # Archived/unused files
│   ├── debug_scripts/               # Archived debug tools
│   └── unused_modules/              # Archived unused modules
├── logs/                            # Application logs
└── public/                          # Public assets
    └── Mem0AndMCP.png              # Project diagram
```

## 🔐 Security

- **User Isolation**: Complete data separation by user_id
- **Input Validation**: All memory content sanitized before storage
- **Secure Connections**: HTTPS/TLS support for remote connections
- **Environment Variables**: Sensitive data stored in environment, not code

## 🚨 Troubleshooting

### Common Issues

**BGE Embedding Server Not Responding?**
- Verify BGE server is running on configured port (default: 8080)
- Check `BGE_SERVER_URL` environment variable
- Test connectivity: `curl http://localhost:8080/health`

**Memory Operations Failing?**
- Check database connectivity via `DATABASE_URL`
- Verify Supabase MCP server access
- Review server logs for detailed error messages

**User Isolation Not Working?**
- Verify `MCP_USER_ID` configuration in MCP client
- Check that `user_id` parameter is being passed correctly
- Confirm database queries include proper user filtering

**Performance Issues?**
- Monitor LOCOMO benchmarks via `get_performance_stats` tool
- Check BGE embedding server performance
- Review database query performance

**LLM Provider Errors?**
- Verify `LLM_API_KEY` and `LLM_PROVIDER` settings
- Check API rate limits and quotas
- Test with `mock` provider for debugging

### Debug Commands

```bash
# Test BGE connectivity
curl http://localhost:8080/health

# Check server health  
curl http://localhost:8050/health

# View comprehensive test results
python test_two_phase_pipeline.py

# Check database connectivity
python -c "from supabase_integration import *; test_connection()"
```

## 📊 Monitoring

The system provides comprehensive performance metrics via LOCOMO benchmarks:

### Performance Targets
- **P50 Latency**: < 708ms (target)
- **P95 Latency**: < 1440ms (target)  
- **Token Usage**: ~7K tokens per conversation (target)

### Available Metrics
- Memory pipeline performance (extraction + update phases)
- BGE embedding response times
- Database query performance
- Per-user usage statistics
- System health indicators

Access via `get_performance_stats` MCP tool for real-time monitoring.

## 🤝 Contributing

This is an internal tool for a 2-person development team. For technical questions or issues:

1. Check existing documentation in `/docs/`
2. Review logs in `/logs/` directory
3. Test with isolation using `/tests/` suite
4. Update documentation for any changes

## 📄 License

Internal use only - proprietary software for team development workflow.

---

**Built with**: Python, FastAPI, PostgreSQL, RabbitMQ, BGE Embeddings, MCP SDK
**Optimized for**: NVIDIA RTX4060 Ti 16GB, Local infrastructure, 2-person team workflow