#!/usr/bin/env python3
"""
Production MCP Server Test
Tests the deployed production container with actual Supabase
"""

import asyncio
import json
import sys
import os
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_container_imports():
    """Test that we can import all required modules"""
    print("📦 Testing production container imports...")
    try:
        # Test we can access the production container environment
        result = os.popen("docker exec spark-mcp-server python -c 'import sys; print(sys.version)'").read().strip()
        if result:
            print(f"✅ Container Python: {result}")
            return True
        else:
            print("❌ Cannot access container Python")
            return False
    except Exception as e:
        print(f"❌ Container test failed: {e}")
        return False

async def test_bge_connectivity_from_container():
    """Test BGE connectivity from inside the container"""
    print("🧠 Testing BGE connectivity from container...")
    try:
        cmd = """docker exec spark-mcp-server python -c "
import httpx
import asyncio
async def test():
    async with httpx.AsyncClient() as client:
        response = await client.get('http://************:8080/health', timeout=10.0)
        print(f'BGE Status: {response.status_code}')
        if response.status_code == 200:
            data = response.json()
            print(f'Model: {data.get(\"model\", \"Unknown\")}')
            print(f'Dimensions: {data.get(\"dimensions\", \"Unknown\")}')
        return response.status_code == 200
result = asyncio.run(test())
print(f'Success: {result}')
"""
        
        result = os.popen(cmd).read().strip()
        if "Success: True" in result:
            print("✅ BGE server accessible from container")
            return True
        else:
            print(f"❌ BGE connectivity issue: {result}")
            return False
    except Exception as e:
        print(f"❌ BGE test failed: {e}")
        return False

async def test_supabase_connectivity():
    """Test Supabase connectivity using MCP tools"""
    print("🗄️ Testing Supabase connectivity via MCP...")
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        # Import and test Supabase MCP integration
        from supabase_mcp_integration import RealSupabaseMCP
        
        mcp_client = RealSupabaseMCP()
        
        # Test connection
        result = await mcp_client.test_connection()
        print(f"✅ Supabase MCP connection: {result}")
        
        # Test listing existing memories
        memories = await mcp_client.list_memories("production-test", limit=5)
        print(f"✅ Found {len(memories)} existing memories")
        
        return True
    except Exception as e:
        print(f"❌ Supabase connectivity failed: {e}")
        return False

async def test_production_memory_pipeline():
    """Test the complete memory pipeline in production"""
    print("🔄 Testing production memory pipeline...")
    try:
        # Test via container execution
        test_script = '''
import asyncio
import sys
sys.path.insert(0, "/app/src")

from memory_extraction import MemoryExtractionModule
from memory_update import MemoryUpdateModule
from supabase_mcp_integration import RealSupabaseMCP
from simple_llm_client import get_spark_memory_client

async def test():
    try:
        # Initialize components
        llm_client = get_spark_memory_client()
        mcp_client = RealSupabaseMCP()
        
        extraction = MemoryExtractionModule(llm_client, mcp_client)
        update = MemoryUpdateModule(llm_client, mcp_client)
        
        print("Components initialized")
        
        # Test basic extraction
        test_text = "Production test: The Spark MCP server is running successfully with BGE embeddings and Supabase backend."
        
        extracted = await extraction.extract_memories(test_text, "production-test")
        print(f"Extracted {len(extracted)} memories")
        
        if extracted:
            # Test update process
            for memory_data in extracted:
                result = await update.process_memory_update(memory_data, "production-test")
                print(f"Update result: {result.operation}")
        
        print("SUCCESS: Pipeline test completed")
        return True
    except Exception as e:
        print(f"ERROR: {e}")
        return False

result = asyncio.run(test())
'''
        
        # Write test script to container and execute
        with open("/tmp/test_pipeline.py", "w") as f:
            f.write(test_script)
        
        # Copy to container and run
        os.system("docker cp /tmp/test_pipeline.py spark-mcp-server:/app/test_pipeline.py")
        result = os.popen("docker exec spark-mcp-server python test_pipeline.py").read()
        
        if "SUCCESS: Pipeline test completed" in result:
            print("✅ Production memory pipeline working")
            return True
        else:
            print(f"❌ Pipeline test failed: {result}")
            return False
    except Exception as e:
        print(f"❌ Pipeline test failed: {e}")
        return False

async def main():
    """Run all production tests"""
    print("🚀 Starting Production MCP Server Tests")
    print("=" * 50)
    
    tests = [
        test_container_imports,
        test_bge_connectivity_from_container,
        test_supabase_connectivity,
        test_production_memory_pipeline,
    ]
    
    results = []
    for test in tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            results.append(False)
        print()
    
    # Summary
    passed = sum(results)
    total = len(results)
    print("=" * 50)
    print(f"🏁 Production Test Summary: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ Production deployment successful! Ready for use.")
        return 0
    else:
        print("❌ Some production tests failed. Check logs above.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)