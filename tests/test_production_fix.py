#!/usr/bin/env python3
"""
Test the production fix for memory extraction
"""

import asyncio
import json
import sys
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from simple_llm_client import get_spark_memory_client

async def test_production_fix():
    """Test that the production fix works correctly."""
    
    print("🧪 Testing Production Memory Extraction Fix")
    print("=" * 60)
    
    # The original JIRA ticket text that was failing
    test_text = "User analyzed JIRA ticket CS1000-7209 from MegaOne Store customer support. Customer rod<PERSON>19<PERSON> complained about receiving polishing pad without water holes despite product description advertising holes for wet polishing."
    
    print(f"Input text: {test_text}")
    print()
    
    try:
        # Test LLM client configuration
        print("🔧 Testing LLM Client Configuration...")
        llm_client = get_spark_memory_client()
        
        print(f"✅ LLM Provider: {os.getenv('LLM_PROVIDER')}")
        print(f"✅ LLM Model: {os.getenv('LLM_CHOICE')}")
        print(f"✅ API Key: {'SET' if os.getenv('LLM_API_KEY') else 'NOT SET'}")
        print(f"✅ Base URL: {os.getenv('LLM_BASE_URL')}")
        print()
        
        # Test memory extraction directly
        print("📝 Testing Memory Extraction...")
        
        # Build extraction prompt (same as in memory_extraction.py)
        prompt = f"""
Extract key memories from this conversation that should be stored for future reference.

Focus on:
- Factual information about the user (preferences, background, context)
- Important decisions or conclusions reached
- Actionable information or commitments made
- Significant events or experiences mentioned
- Skills, knowledge, or expertise demonstrated

Previous Message: 
Current Message: {test_text}

Conversation Summary: None
Recent Context: None

Extract only factual, actionable information that would be valuable to remember in future conversations.
Avoid storing:
- Temporary conversation state
- Generic responses or acknowledgments
- Redundant information already covered in summary

Return your response as a JSON list of memory strings. If no significant memories should be stored, return an empty list [].

Example response format:
["User prefers React over Vue for frontend development", "User is working on a machine learning project about sentiment analysis"]
        """.strip()
        
        print("Sending extraction request to LLM...")
        raw_response = await llm_client.generate(prompt)
        
        print(f"✅ LLM Response received: {len(raw_response)} characters")
        print(f"Raw response: {raw_response[:200]}...")
        print()
        
        # Parse the response
        try:
            memories = json.loads(raw_response)
            print(f"📊 Extraction Results:")
            print(f"Candidates processed: {len(memories)}")
            
            if memories:
                for i, memory in enumerate(memories, 1):
                    print(f"  {i}. {memory}")
                print()
                print("🎉 SUCCESS: Memory extraction is working correctly!")
                print("✅ The original '0 candidates processed' issue has been resolved")
                
                return True
            else:
                print("⚠️  WARNING: No memories extracted, but LLM is responding")
                return False
                
        except json.JSONDecodeError as e:
            print(f"❌ JSON parsing failed: {e}")
            print(f"Raw response: {raw_response}")
            return False
            
    except ValueError as e:
        print(f"❌ Configuration error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_production_fix())
    sys.exit(0 if success else 1)