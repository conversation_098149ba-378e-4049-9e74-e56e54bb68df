#!/usr/bin/env python3
"""
Test the performance fix to ensure sub-second response times
"""

import asyncio
import json
import time
import sys
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from simple_llm_client import get_spark_memory_client
from memory_extraction import MemoryExtractionModule

async def test_performance_fix():
    """Test that the performance fix reduces processing time."""
    
    print("🚀 Testing Performance Fix")
    print("=" * 40)
    print("Target: < 2 seconds (vs previous 26 seconds)")
    print()
    
    # The JIRA ticket text that was taking 26 seconds
    test_text = "User analyzed JIRA ticket CS1000-7209 from MegaOne Store customer support. Customer rodo1965 complained about receiving polishing pad without water holes despite product description advertising holes for wet polishing."
    
    try:
        # Initialize components (same as container)
        llm_client = get_spark_memory_client()
        print("✅ LLM client initialized")
        
        # Mock BGE client
        class MockBGEClient:
            async def embed_single(self, text):
                return [0.1] * 768
        
        bge_client = MockBGEClient()
        extraction = MemoryExtractionModule(llm_client, bge_client)
        print("✅ Extraction module initialized")
        print()
        
        # Test extraction performance
        print("⏱️  Testing extraction performance...")
        start_time = time.time()
        
        # Create message pair and context (same as container)
        message_pair = ("", test_text)
        context = {
            "conversation_summary": "None",
            "recent_messages": [],
            "user_id": "user"
        }
        
        # Extract memories
        memories = await extraction.extract_memories(message_pair, context)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"📊 Results:")
        print(f"Duration: {duration:.2f} seconds")
        print(f"Candidates: {len(memories)}")
        print(f"Target: < 2.0 seconds")
        print()
        
        if memories:
            for i, memory in enumerate(memories, 1):
                print(f"  {i}. {memory}")
            print()
        
        # Performance assessment
        if duration < 2.0:
            print("🎉 SUCCESS: Performance fix working!")
            print(f"✅ Achieved {duration:.2f}s (was 26s)")
            print("✅ Should now work within MCP timeout limits")
            return True
        elif duration < 10.0:
            print("⚠️  IMPROVED: Better but still slow")
            print(f"⚡ Reduced from 26s to {duration:.2f}s")
            print("⚠️  May still timeout in some MCP clients")
            return True
        else:
            print("❌ STILL SLOW: Performance fix didn't work")
            print(f"❌ Still taking {duration:.2f}s")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_performance_fix())
    if success:
        print("\n✅ The container should now respond quickly!")
        print("🔧 Try your add_memories request again - it should work now.")
    else:
        print("\n❌ Performance issue still exists.")
    
    sys.exit(0 if success else 1)