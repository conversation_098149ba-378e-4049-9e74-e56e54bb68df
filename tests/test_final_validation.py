#!/usr/bin/env python3
"""
Final validation test for vector storage format correction.
Tests both add and search without cleanup to validate end-to-end functionality.
"""

import asyncio
import json
import time
from mcp import ClientSession
from mcp.client.sse import sse_client

MCP_SERVER_URL = "http://localhost:8050/sse"
TEST_USER_ID = "final_validation_test"

async def test_final_validation():
    """Test complete add + search workflow"""
    print("🎯 Final Vector Storage Format Validation")
    print("=" * 50)
    
    try:
        async with sse_client(MCP_SERVER_URL) as (read, write):
            async with ClientSession(read, write) as session:
                print("✅ Connected to MCP server")
                await session.initialize()
                
                # Clean up previous test data
                print(f"\n🧹 Cleaning up previous test data...")
                await session.call_tool("delete_all_memories", arguments={"user_id": TEST_USER_ID})
                
                # Add a test memory
                print(f"\n📝 Adding test memory...")
                test_text = "I prefer Python programming for backend development and API design with PostgreSQL databases."
                
                add_result = await session.call_tool(
                    "add_memories",
                    arguments={"text": test_text, "user_id": TEST_USER_ID}
                )
                
                if add_result.content:
                    add_response = json.loads(add_result.content[0].text)
                    candidates = add_response.get('candidates_processed', 0)
                    print(f"✅ Added memory with {candidates} candidates processed")
                else:
                    print(f"❌ Failed to add memory")
                    return
                
                # Wait for processing
                print(f"\n⏳ Waiting for memory processing...")
                await asyncio.sleep(2)
                
                # Test semantic search
                print(f"\n🔍 Testing semantic search...")
                search_queries = [
                    "programming languages and development",
                    "database preferences and data storage",
                    "backend technology stack"
                ]
                
                total_found = 0
                
                for i, query in enumerate(search_queries):
                    search_result = await session.call_tool(
                        "search_memory",
                        arguments={"query": query, "user_id": TEST_USER_ID, "limit": 5}
                    )
                    
                    if search_result.content:
                        search_response = json.loads(search_result.content[0].text)
                        memories_found = len(search_response.get('results', []))
                        total_found += memories_found
                        
                        status = "✅" if memories_found > 0 else "❌"
                        print(f"  {status} Query {i+1}: '{query}' → {memories_found} results")
                        
                        if memories_found > 0:
                            for j, result in enumerate(search_response['results'][:2]):
                                preview = result[:60] + "..." if len(result) > 60 else result
                                print(f"    {j+1}. {preview}")
                    else:
                        print(f"  ❌ Query {i+1}: Failed to search")
                
                # Test list memories
                print(f"\n📋 Testing memory retrieval...")
                list_result = await session.call_tool(
                    "list_memories", 
                    arguments={"user_id": TEST_USER_ID, "limit": 10}
                )
                
                if list_result.content:
                    list_response = json.loads(list_result.content[0].text)
                    total_memories = len(list_response.get('memories', []))
                    print(f"✅ Retrieved {total_memories} total memories")
                else:
                    print(f"❌ Failed to retrieve memories")
                
                # Final assessment
                print(f"\n🎯 Final Assessment:")
                
                if candidates > 0:
                    print(f"  ✅ Memory extraction: WORKING ({candidates} candidates)")
                else:
                    print(f"  ❌ Memory extraction: FAILED (0 candidates)")
                
                if total_found > 0:
                    print(f"  ✅ Semantic search: WORKING ({total_found} total results)")
                    print(f"  ✅ Vector storage format: FIXED")
                    print(f"  ✅ PostgreSQL integration: STABLE")
                else:
                    print(f"  ❌ Semantic search: FAILED (0 results)")
                    print(f"  ⚠️  May need threshold adjustment")
                
                if candidates > 0 and total_found > 0:
                    print(f"\n🎉 VECTOR STORAGE FORMAT CORRECTION: SUCCESS!")
                    print(f"   - Memory extraction pipeline operational")
                    print(f"   - PostgreSQL vector storage working correctly")
                    print(f"   - Semantic search returning relevant results")
                    print(f"   - End-to-end functionality restored")
                else:
                    print(f"\n⚠️  PARTIAL SUCCESS - Issues remain:")
                    if candidates == 0:
                        print(f"   - Memory extraction pipeline needs investigation")
                    if total_found == 0:
                        print(f"   - Semantic search threshold may need adjustment")
                
                # Clean up test data
                print(f"\n🧹 Final cleanup...")
                await session.call_tool("delete_all_memories", arguments={"user_id": TEST_USER_ID})
                print(f"✅ Test data cleaned up")
                
    except Exception as e:
        print(f"❌ Validation test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_final_validation())