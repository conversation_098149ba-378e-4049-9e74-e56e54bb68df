"""
Comprehensive Validation Tests for Memory Intelligence Enhancement

Tests all 4 phases of the memory intelligence enhancement implementation
to ensure performance targets and functionality requirements are met.
"""

import asyncio
import time
import json
import logging
from typing import List, Dict, Any, Tuple
import numpy as np
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import required modules
from src.memory_extraction import MemoryExtractionModule
from src.memory_update import MemoryUpdateModule
from src.memory_database import MemoryDatabase
from src.graph_memory_service import GraphMemoryService
from src.rolling_summary import RollingSummaryManager
from src.bge_embedding_client import BGEEmbeddingClient
from src.llm_client import LLMClient
from src.config import get_config

config = get_config()


class MemoryIntelligenceValidator:
    """Validator for Memory Intelligence Enhancement phases."""
    
    def __init__(self):
        """Initialize validator with test components."""
        self.results = {
            "phase_1": {},
            "phase_2": {},
            "phase_3": {},
            "phase_4": {},
            "performance": {}
        }
        
    async def setup_test_environment(self):
        """Set up test environment with all components."""
        logger.info("Setting up test environment...")
        
        # Initialize components
        self.llm_client = LLMClient()
        self.bge_client = BGEEmbeddingClient()
        self.db_client = MemoryDatabase()
        self.graph_service = GraphMemoryService(self.llm_client)
        
        # Initialize enhanced components
        self.extraction = MemoryExtractionModule(self.llm_client, self.db_client, self.bge_client)
        self.update = MemoryUpdateModule(self.llm_client, self.db_client, self.bge_client)
        self.summary = RollingSummaryManager(self.llm_client, self.db_client, graph_service=self.graph_service)
        
        # Initialize databases
        await self.db_client.initialize()
        await self.graph_service.initialize()
        
        logger.info("Test environment setup complete")
        
    async def validate_phase_1_extraction_enhancement(self):
        """Validate Phase 1: Enhanced extraction with evolution focus."""
        logger.info("\n=== PHASE 1 VALIDATION: Enhanced Extraction ===")
        
        test_cases = [
            # Evolution detection case
            ("I used to use React but now I prefer Vue.js for frontend development", 
             ["preference_evolution", "technology_change"]),
            # Relationship detection case
            ("I'm working with Sarah on the new AI project using Python and TensorFlow",
             ["collaboration", "project_relationship"]),
            # Temporal marker case
            ("Recently learned Docker and started using it for all deployments",
             ["temporal_learning", "skill_acquisition"])
        ]
        
        phase_1_results = {
            "evolution_detection": 0,
            "relationship_detection": 0,
            "temporal_detection": 0,
            "extraction_times": []
        }
        
        for text, expected_patterns in test_cases:
            start_time = time.time()
            
            # Test enhanced extraction
            context = {
                "user_id": "test_user",
                "conversation_summary": "User is a software developer interested in modern technologies",
                "recent_messages": []
            }
            
            # Extract with enhanced metadata
            if hasattr(self.extraction, 'extract_with_enhanced_metadata'):
                candidates, metadata = await self.extraction.extract_with_enhanced_metadata(
                    ("", text), context, {"test_mode": True}
                )
                
                # Check for expected patterns
                if "evolution" in expected_patterns[0]:
                    if metadata.get("evolution_detected"):
                        phase_1_results["evolution_detection"] += 1
                        
                if "relationship" in expected_patterns[1]:
                    if metadata.get("relationships_detected"):
                        phase_1_results["relationship_detection"] += 1
                        
                if "temporal" in expected_patterns[0]:
                    if metadata.get("temporal_markers"):
                        phase_1_results["temporal_detection"] += 1
            
            extraction_time = (time.time() - start_time) * 1000
            phase_1_results["extraction_times"].append(extraction_time)
            
        # Calculate success rates
        phase_1_results["evolution_success_rate"] = phase_1_results["evolution_detection"] / 1 * 100
        phase_1_results["relationship_success_rate"] = phase_1_results["relationship_detection"] / 1 * 100
        phase_1_results["temporal_success_rate"] = phase_1_results["temporal_detection"] / 1 * 100
        phase_1_results["avg_extraction_time"] = np.mean(phase_1_results["extraction_times"])
        
        self.results["phase_1"] = phase_1_results
        
        logger.info(f"Phase 1 Results:")
        logger.info(f"  Evolution Detection: {phase_1_results['evolution_success_rate']:.1f}%")
        logger.info(f"  Relationship Detection: {phase_1_results['relationship_success_rate']:.1f}%")
        logger.info(f"  Temporal Detection: {phase_1_results['temporal_success_rate']:.1f}%")
        logger.info(f"  Avg Extraction Time: {phase_1_results['avg_extraction_time']:.0f}ms")
        
    async def validate_phase_2_graph_integration(self):
        """Validate Phase 2: Neo4j graph integration."""
        logger.info("\n=== PHASE 2 VALIDATION: Graph Integration ===")
        
        phase_2_results = {
            "entity_extraction_success": 0,
            "relationship_creation_success": 0,
            "graph_traversal_success": 0,
            "hybrid_search_improvement": 0,
            "processing_times": []
        }
        
        test_memories = [
            "John Smith leads the AI team and works with Sarah on computer vision",
            "The AI team uses PyTorch for deep learning projects",
            "Sarah specializes in computer vision and collaborates with John"
        ]
        
        # Test entity extraction and graph creation
        for i, memory in enumerate(test_memories):
            start_time = time.time()
            
            # Store memory with graph processing
            result = await self.db_client.store_memory_with_graph(
                "test_user", memory, 
                await self.bge_client.embed_single(memory)
            )
            
            if result.get("graph_processing", {}).get("entities_created", 0) > 0:
                phase_2_results["entity_extraction_success"] += 1
                
            processing_time = (time.time() - start_time) * 1000
            phase_2_results["processing_times"].append(processing_time)
            
        # Test relationship discovery
        related_memories = await self.db_client.find_memory_relationships(
            "1", "test_user", depth=2
        )
        if related_memories:
            phase_2_results["relationship_creation_success"] = 1
            
        # Test hybrid search improvement
        query_embedding = await self.bge_client.embed_single("Tell me about the AI team")
        
        # Regular search
        regular_results = await self.db_client.similarity_search(
            query_embedding, "test_user", threshold=0.7
        )
        
        # Graph-enhanced search
        enhanced_results = await self.db_client.similarity_search_with_graph(
            query_embedding, "test_user", threshold=0.7
        )
        
        if enhanced_results and any(r.get("graph_enhanced") for r in enhanced_results):
            phase_2_results["graph_traversal_success"] = 1
            
        # Calculate improvement
        phase_2_results["entity_extraction_rate"] = phase_2_results["entity_extraction_success"] / len(test_memories) * 100
        phase_2_results["avg_processing_time"] = np.mean(phase_2_results["processing_times"])
        
        self.results["phase_2"] = phase_2_results
        
        logger.info(f"Phase 2 Results:")
        logger.info(f"  Entity Extraction Rate: {phase_2_results['entity_extraction_rate']:.1f}%")
        logger.info(f"  Relationship Creation: {'Success' if phase_2_results['relationship_creation_success'] else 'Failed'}")
        logger.info(f"  Graph Traversal: {'Success' if phase_2_results['graph_traversal_success'] else 'Failed'}")
        logger.info(f"  Avg Processing Time: {phase_2_results['avg_processing_time']:.0f}ms")
        
    async def validate_phase_3_history_tracking(self):
        """Validate Phase 3: History-based learning."""
        logger.info("\n=== PHASE 3 VALIDATION: History Tracking ===")
        
        phase_3_results = {
            "history_tracking_success": 0,
            "evolution_pattern_detection": 0,
            "history_aware_decisions": 0,
            "stability_scoring_accuracy": 0,
            "processing_times": []
        }
        
        # Create test memory with evolution
        test_user = "test_history_user"
        memory_content_v1 = "I prefer using JavaScript for web development"
        memory_content_v2 = "I'm switching from JavaScript to TypeScript for better type safety"
        memory_content_v3 = "TypeScript has become my primary language for all projects"
        
        # Store initial memory
        embedding_v1 = await self.bge_client.embed_single(memory_content_v1)
        memory_id = await self.db_client.store_memory(test_user, memory_content_v1, embedding_v1)
        
        # Simulate updates to create history
        await asyncio.sleep(0.1)  # Small delay to ensure different timestamps
        
        # Update memory (v2)
        embedding_v2 = await self.bge_client.embed_single(memory_content_v2)
        await self.db_client.update_memory(memory_id, memory_content_v2, embedding_v2)
        
        await asyncio.sleep(0.1)
        
        # Update memory (v3)
        embedding_v3 = await self.bge_client.embed_single(memory_content_v3)
        await self.db_client.update_memory(memory_id, memory_content_v3, embedding_v3)
        
        # Test history analysis
        start_time = time.time()
        history_analysis = await self.update.analyze_memory_history(str(memory_id), test_user)
        history_time = (time.time() - start_time) * 1000
        phase_3_results["processing_times"].append(history_time)
        
        # Validate history tracking
        if history_analysis.get("total_updates", 0) >= 2:
            phase_3_results["history_tracking_success"] = 1
            
        if history_analysis.get("evolution_pattern") in ["evolutionary", "rapid_evolution", "iterative_refinement"]:
            phase_3_results["evolution_pattern_detection"] = 1
            
        # Test history-aware decision making
        new_candidate = "I'm fully committed to TypeScript and no longer use JavaScript"
        decision_result = await self.update.process_candidate_with_history_analysis(
            new_candidate, test_user
        )
        
        if decision_result.get("history_enhanced"):
            phase_3_results["history_aware_decisions"] = 1
            
        if decision_result.get("confidence", 0) > 0.7:
            phase_3_results["stability_scoring_accuracy"] = 1
            
        # Calculate results
        phase_3_results["avg_processing_time"] = np.mean(phase_3_results["processing_times"])
        phase_3_results["success_rate"] = (
            phase_3_results["history_tracking_success"] + 
            phase_3_results["evolution_pattern_detection"] +
            phase_3_results["history_aware_decisions"] +
            phase_3_results["stability_scoring_accuracy"]
        ) / 4 * 100
        
        self.results["phase_3"] = phase_3_results
        
        logger.info(f"Phase 3 Results:")
        logger.info(f"  History Tracking: {'Success' if phase_3_results['history_tracking_success'] else 'Failed'}")
        logger.info(f"  Evolution Detection: {'Success' if phase_3_results['evolution_pattern_detection'] else 'Failed'}")
        logger.info(f"  History-Aware Decisions: {'Success' if phase_3_results['history_aware_decisions'] else 'Failed'}")
        logger.info(f"  Overall Success Rate: {phase_3_results['success_rate']:.1f}%")
        
    async def validate_performance_targets(self):
        """Validate overall performance against LOCOMO targets."""
        logger.info("\n=== PERFORMANCE VALIDATION ===")
        
        performance_results = {
            "p95_latency": [],
            "relevance_scores": [],
            "memory_operations": []
        }
        
        # Test P95 latency with realistic workload
        test_messages = [
            "I'm working on a new machine learning project",
            "The project uses TensorFlow and Python",
            "I collaborate with the data science team",
            "We're focusing on natural language processing",
            "Recently started using transformer models"
        ]
        
        for message in test_messages:
            start_time = time.time()
            
            # Full pipeline execution
            context = {"user_id": "perf_test_user", "recent_messages": []}
            candidates = await self.extraction.extract_memories((message, message), context)
            
            if candidates:
                embedding = await self.bge_client.embed_single(candidates[0])
                result = await self.update.process_candidate_memory_with_embedding(
                    candidates[0], embedding, "perf_test_user"
                )
                performance_results["memory_operations"].append(result["operation"])
            
            total_time = (time.time() - start_time) * 1000
            performance_results["p95_latency"].append(total_time)
            
        # Calculate P95 latency
        p95_latency = np.percentile(performance_results["p95_latency"], 95)
        avg_latency = np.mean(performance_results["p95_latency"])
        
        # Test relevance improvement
        # This would require more sophisticated testing with ground truth data
        # For now, we'll check if graph enhancement is working
        test_query = "Tell me about my machine learning work"
        query_embedding = await self.bge_client.embed_single(test_query)
        
        enhanced_results = await self.db_client.similarity_search_with_graph(
            query_embedding, "perf_test_user", threshold=0.7
        )
        
        relevance_improvement = len([r for r in enhanced_results if r.get("graph_enhanced")]) > 0
        
        performance_results["p95_latency_ms"] = p95_latency
        performance_results["avg_latency_ms"] = avg_latency
        performance_results["meets_latency_target"] = p95_latency < 1500  # Target: <1.5s
        performance_results["relevance_enhanced"] = relevance_improvement
        
        self.results["performance"] = performance_results
        
        logger.info(f"Performance Results:")
        logger.info(f"  P95 Latency: {p95_latency:.0f}ms (Target: <1500ms)")
        logger.info(f"  Average Latency: {avg_latency:.0f}ms")
        logger.info(f"  Meets Latency Target: {'✓' if performance_results['meets_latency_target'] else '✗'}")
        logger.info(f"  Relevance Enhancement: {'✓' if relevance_improvement else '✗'}")
        
    def generate_validation_report(self):
        """Generate comprehensive validation report."""
        logger.info("\n=== VALIDATION REPORT ===")
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "overall_success": True,
            "phase_results": self.results,
            "recommendations": []
        }
        
        # Check Phase 1
        if self.results["phase_1"].get("avg_extraction_time", float('inf')) > 500:
            report["recommendations"].append("Phase 1: Consider optimizing extraction prompts for better performance")
            
        # Check Phase 2
        if not self.results["phase_2"].get("graph_traversal_success"):
            report["recommendations"].append("Phase 2: Graph traversal needs attention")
            report["overall_success"] = False
            
        # Check Phase 3
        if self.results["phase_3"].get("success_rate", 0) < 75:
            report["recommendations"].append("Phase 3: History tracking accuracy below target")
            
        # Check Performance
        if not self.results["performance"].get("meets_latency_target"):
            report["recommendations"].append("Performance: P95 latency exceeds 1.5s target")
            report["overall_success"] = False
            
        # Save report
        with open("/home/<USER>/dev/tools/mcp-mem0/tests/validation_report.json", "w") as f:
            json.dump(report, f, indent=2)
            
        logger.info(f"\nOverall Validation: {'PASSED ✓' if report['overall_success'] else 'FAILED ✗'}")
        
        if report["recommendations"]:
            logger.info("\nRecommendations:")
            for rec in report["recommendations"]:
                logger.info(f"  - {rec}")
                
        return report
        
    async def run_full_validation(self):
        """Run complete validation suite."""
        try:
            await self.setup_test_environment()
            
            # Run all phase validations
            await self.validate_phase_1_extraction_enhancement()
            await self.validate_phase_2_graph_integration()
            await self.validate_phase_3_history_tracking()
            await self.validate_performance_targets()
            
            # Generate report
            report = self.generate_validation_report()
            
            # Cleanup
            await self.db_client.close()
            if hasattr(self.graph_service, 'close'):
                await self.graph_service.close()
                
            return report
            
        except Exception as e:
            logger.error(f"Validation failed with error: {e}")
            raise


async def main():
    """Main validation entry point."""
    validator = MemoryIntelligenceValidator()
    report = await validator.run_full_validation()
    
    print("\n" + "="*50)
    print("MEMORY INTELLIGENCE ENHANCEMENT VALIDATION COMPLETE")
    print("="*50)
    print(f"Report saved to: tests/validation_report.json")
    print(f"Overall Result: {'PASSED ✓' if report['overall_success'] else 'FAILED ✗'}")


if __name__ == "__main__":
    asyncio.run(main())