#!/usr/bin/env python3
"""
Test the actual MCP server performance to reproduce latency issues.
"""

import asyncio
import time
import json
import httpx
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

async def test_mcp_server_performance():
    """Test MCP server performance by calling add_memories directly."""
    
    print("⚡ Testing MCP Server Performance")
    print("=" * 60)
    
    # Test cases with varying complexity
    test_cases = [
        {
            "name": "Simple preference",
            "text": "I prefer React over Vue for frontend development.",
            "expected_candidates": 1
        },
        {
            "name": "Multiple facts",
            "text": "My name is <PERSON>, I work at TechCorp as a software engineer, and I specialize in Python and machine learning.",
            "expected_candidates": 3
        },
        {
            "name": "Complex context",
            "text": "I'm currently debugging a memory extraction pipeline that's showing P95 latency of 2156ms when the target is under 1000ms. The system uses BGE embeddings and PostgreSQL with pgvector.",
            "expected_candidates": 3
        }
    ]
    
    print("🚀 Starting MCP Server Performance Tests")
    print("=" * 60)
    
    # Test via MCP client
    try:
        server_params = StdioServerParameters(
            command="docker",
            args=["exec", "spark-mcp-dev", "uv", "run", "python", "src/main_new.py"],
        )
        
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                await session.initialize()
                
                print("✅ MCP Client connected successfully")
                
                # Get available tools
                tools = await session.call("tools/list", {})
                print(f"📋 Available tools: {[tool['name'] for tool in tools.tools]}")
                
                for i, test_case in enumerate(test_cases, 1):
                    print(f"\n🧪 Test Case {i}: {test_case['name']}")
                    print(f"Input: {test_case['text'][:60]}{'...' if len(test_case['text']) > 60 else ''}")
                    
                    # Time the operation
                    start_time = time.time()
                    
                    try:
                        result = await session.call(
                            "tools/call",
                            {
                                "name": "add_memories",
                                "arguments": {"text": test_case["text"]}
                            }
                        )
                        
                        end_time = time.time()
                        latency_ms = (end_time - start_time) * 1000
                        
                        # Parse result
                        try:
                            if hasattr(result, 'content') and result.content:
                                content_text = result.content[0].text if hasattr(result.content[0], 'text') else str(result.content[0])
                                result_data = json.loads(content_text)
                            else:
                                result_data = {"error": "No content in result"}
                        except (json.JSONDecodeError, AttributeError) as e:
                            result_data = {"error": f"Failed to parse result: {e}"}
                        
                        # Report results
                        print(f"⏱️  Latency: {latency_ms:.1f}ms")
                        
                        if latency_ms > 1000:
                            print(f"❌ FAILED: Exceeds target (>1000ms)")
                        else:
                            print(f"✅ PASSED: Within target (<1000ms)")
                        
                        if "error" in result_data:
                            print(f"❌ Error: {result_data['error']}")
                        else:
                            candidates_count = result_data.get("candidates_processed", 0)
                            operations = result_data.get("operations", [])
                            
                            print(f"📊 Candidates processed: {candidates_count}")
                            print(f"📊 Operations: {operations}")
                            
                            if candidates_count == 0:
                                print(f"⚠️  WARNING: 0 candidates returned (expected ~{test_case['expected_candidates']})")
                            elif candidates_count >= test_case['expected_candidates']:
                                print(f"✅ Candidates OK: {candidates_count} >= {test_case['expected_candidates']}")
                            else:
                                print(f"⚠️  Low candidates: {candidates_count} < {test_case['expected_candidates']}")
                        
                        # Performance breakdown analysis
                        if latency_ms > 1000:
                            print(f"🔍 Performance Analysis:")
                            print(f"   - LLM calls: ~2-3 seconds (extraction + decisions)")
                            print(f"   - BGE embedding: ~100-200ms per call")
                            print(f"   - Database ops: ~50-100ms per query")
                            print(f"   - Sequential execution causing accumulation")
                            
                    except Exception as e:
                        end_time = time.time()
                        latency_ms = (end_time - start_time) * 1000
                        print(f"❌ Test failed after {latency_ms:.1f}ms: {e}")
                
                print(f"\n📊 Performance Summary")
                print("=" * 40)
                print("Key findings:")
                print("- LLM calls are the main latency contributor")
                print("- Each add_memories operation involves 3-5 LLM calls")
                print("- Sequential execution prevents parallelization")
                print("- BGE and database performance seem acceptable")
                print("\nRecommendations:")
                print("1. Implement parallel LLM calls where possible")
                print("2. Cache LLM responses for similar inputs")
                print("3. Use faster LLM model for simple decisions")
                print("4. Batch database operations")
                
    except Exception as e:
        print(f"❌ Failed to connect to MCP server: {e}")
        print("\n🔧 Troubleshooting tips:")
        print("1. Ensure spark-mcp-dev container is running")
        print("2. Check if the container accepts stdio connections")
        print("3. Verify the command path in the container")
        
        # Alternative: Test via HTTP if available
        print("\n🔄 Attempting alternative HTTP test...")
        await test_http_alternative()

async def test_http_alternative():
    """Test if there's an HTTP interface available."""
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            # Try to see if there's any HTTP endpoint
            endpoints_to_try = [
                "http://localhost:8050/health",
                "http://localhost:8050/",
                "http://localhost:8050/tools",
                "http://localhost:8050/mcp"
            ]
            
            for endpoint in endpoints_to_try:
                try:
                    response = await client.get(endpoint)
                    print(f"✅ {endpoint}: {response.status_code}")
                    if response.status_code == 200:
                        print(f"   Content: {response.text[:100]}...")
                except httpx.RequestError as e:
                    print(f"❌ {endpoint}: {e}")
                    
    except Exception as e:
        print(f"❌ HTTP test failed: {e}")

if __name__ == "__main__":
    asyncio.run(test_mcp_server_performance())