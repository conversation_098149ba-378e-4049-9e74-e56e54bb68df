"""
BGE Mock Server for Testing

Simulates the BGE embedding server for development and testing.
Returns consistent embeddings for testing purposes.
"""

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import List
import hashlib
import numpy as np

app = FastAPI()

class EmbedRequest(BaseModel):
    texts: List[str]
    add_instruction: bool = True

class EmbedResponse(BaseModel):
    embeddings: List[List[float]]
    model: str = "BAAI/bge-base-en-v1.5"
    dimension: int = 768

@app.get("/health")
async def health():
    return {"status": "healthy", "model": "BAAI/bge-base-en-v1.5"}

@app.post("/embeddings")
async def create_embeddings(request: EmbedRequest):
    """Generate mock embeddings that are consistent for the same input."""
    embeddings = []
    
    for text in request.texts:
        # Add instruction prefix if requested
        if request.add_instruction:
            text = "Represent this sentence for searching relevant passages: " + text
        
        # Generate deterministic embedding based on text hash
        text_hash = hashlib.sha256(text.encode()).hexdigest()
        seed = int(text_hash[:8], 16)
        np.random.seed(seed)
        
        # Generate 768-dimensional embedding
        embedding = np.random.normal(0, 0.1, 768).tolist()
        
        # Normalize to unit vector (like real BGE)
        norm = np.linalg.norm(embedding)
        embedding = (np.array(embedding) / norm).tolist()
        
        embeddings.append(embedding)
    
    return EmbedResponse(embeddings=embeddings)

@app.get("/")
async def root():
    return {"message": "BGE Mock Server", "version": "1.0.0"}
