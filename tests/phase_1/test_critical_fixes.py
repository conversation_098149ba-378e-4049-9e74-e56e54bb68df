"""
Phase 1 Tests: Critical Fixes

Tests for:
- Semantic search functionality
- P95 latency improvements
- Basic conflict resolution
"""

import pytest
import asyncio
import time
from typing import List
import httpx

# Test configuration
BASE_URL = "http://localhost:8050"
TEST_USER_ID = "test_phase_1"

@pytest.mark.asyncio
async def test_semantic_search_accuracy():
    """Test that semantic search returns relevant results."""
    async with httpx.AsyncClient() as client:
        # Add test memories
        test_memories = [
            "I love Italian food, especially pasta",
            "My favorite programming language is Python",
            "I enjoy hiking in the mountains on weekends",
            "I'm allergic to shellfish and peanuts",
            "I work as a software engineer at a startup"
        ]
        
        # Store memories
        for memory in test_memories:
            response = await client.post(
                f"{BASE_URL}/tools/add_memories",
                json={
                    "text": memory,
                    "user_id": TEST_USER_ID
                }
            )
            assert response.status_code == 200
        
        # Test semantic search
        search_queries = [
            ("food preferences", ["Italian food", "shellfish"]),
            ("programming", ["Python", "software engineer"]),
            ("outdoor activities", ["hiking", "mountains"])
        ]
        
        for query, expected_keywords in search_queries:
            response = await client.post(
                f"{BASE_URL}/tools/search_memory",
                json={
                    "query": query,
                    "user_id": TEST_USER_ID,
                    "limit": 3
                }
            )
            assert response.status_code == 200
            
            results = response.json()
            assert results["success"] is True
            assert len(results["results"]) > 0
            
            # Check if relevant results are returned
            found_content = " ".join([r["content"] for r in results["results"]])
            relevant_found = any(keyword in found_content for keyword in expected_keywords)
            assert relevant_found, f"No relevant results for query: {query}"

@pytest.mark.asyncio
async def test_p95_latency():
    """Test that P95 latency is under 1 second."""
    async with httpx.AsyncClient() as client:
        latencies = []
        
        # Run 100 operations to get meaningful P95
        for i in range(100):
            start_time = time.time()
            
            response = await client.post(
                f"{BASE_URL}/tools/add_memories",
                json={
                    "text": f"Test memory {i} for latency testing",
                    "user_id": f"{TEST_USER_ID}_latency"
                }
            )
            
            end_time = time.time()
            latency = (end_time - start_time) * 1000  # Convert to ms
            latencies.append(latency)
            
            assert response.status_code == 200
        
        # Calculate P95
        latencies.sort()
        p95_index = int(len(latencies) * 0.95)
        p95_latency = latencies[p95_index]
        
        print(f"P95 Latency: {p95_latency:.2f}ms")
        assert p95_latency < 1000, f"P95 latency {p95_latency}ms exceeds 1000ms target"

@pytest.mark.asyncio
async def test_conflict_resolution():
    """Test that conflicting memories are properly resolved."""
    async with httpx.AsyncClient() as client:
        # Add initial memory
        response = await client.post(
            f"{BASE_URL}/tools/add_memories",
            json={
                "text": "My favorite color is blue",
                "user_id": TEST_USER_ID
            }
        )
        assert response.status_code == 200
        
        # Add conflicting memory
        response = await client.post(
            f"{BASE_URL}/tools/add_memories", 
            json={
                "text": "Actually, my favorite color is now green",
                "user_id": TEST_USER_ID
            }
        )
        assert response.status_code == 200
        
        # Search for favorite color
        response = await client.post(
            f"{BASE_URL}/tools/search_memory",
            json={
                "query": "favorite color",
                "user_id": TEST_USER_ID,
                "limit": 5
            }
        )
        assert response.status_code == 200
        
        results = response.json()
        memories = results["results"]
        
        # Verify that green is prioritized over blue
        if len(memories) > 0:
            # Most recent/relevant should mention green
            assert "green" in memories[0]["content"].lower()
            
        # Verify no exact duplicates exist
        contents = [m["content"] for m in memories]
        assert len(contents) == len(set(contents)), "Duplicate memories found"

@pytest.mark.asyncio
async def test_health_check():
    """Test that health check endpoint works."""
    async with httpx.AsyncClient() as client:
        response = await client.get(f"{BASE_URL}/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "ok"
        assert "ready" in data

# Cleanup fixture
@pytest.fixture(autouse=True)
async def cleanup():
    """Clean up test data after each test."""
    yield
    # Cleanup logic here if needed
    async with httpx.AsyncClient() as client:
        # Delete test memories
        await client.post(
            f"{BASE_URL}/tools/delete_all_memories",
            json={"user_id": TEST_USER_ID}
        )
        await client.post(
            f"{BASE_URL}/tools/delete_all_memories",
            json={"user_id": f"{TEST_USER_ID}_latency"}
        )
