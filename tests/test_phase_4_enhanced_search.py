"""
Test Suite for Phase 4: Enhanced Search Implementation

Tests metadata filtering, query expansion, recency boosting, and entity search.
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List
import os
import sys

# Add parent directory to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.enhanced_search import EnhancedMemorySearch
from src.enhanced_search_handler import (
    search_memory_enhanced,
    parse_search_filters,
    search_by_entity_handler
)
from src.memory_database import MemoryDatabase
from src.bge_embedding_client import BGEEmbeddingClient
from src.llm_client import LLMClient
from src.rolling_summary import RollingSummaryManager
from src.graph_memory_service import GraphMemoryService
from src.config import get_config

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestPhase4EnhancedSearch:
    """Test suite for Phase 4 enhanced search features."""
    
    def __init__(self):
        """Initialize test environment."""
        self.config = get_config()
        self.test_user = "test_phase4_user"
        self.results = {
            "filter_tests": {},
            "expansion_tests": {},
            "recency_tests": {},
            "entity_tests": {},
            "performance": {}
        }
    
    async def setup(self):
        """Set up test environment."""
        logger.info("Setting up Phase 4 test environment...")
        
        # Initialize components
        self.llm_client = LLMClient()
        self.bge_client = BGEEmbeddingClient()
        self.db_client = MemoryDatabase()
        self.graph_service = GraphMemoryService(self.llm_client)
        self.summary_manager = RollingSummaryManager(
            self.llm_client, self.db_client, graph_service=self.graph_service
        )
        
        # Initialize databases
        await self.db_client.initialize()
        await self.graph_service.initialize()
        
        # Initialize enhanced search
        self.enhanced_search = EnhancedMemorySearch(
            db_client=self.db_client,
            llm_client=self.llm_client,
            bge_client=self.bge_client,
            graph_service=self.graph_service,
            summary_manager=self.summary_manager
        )
        
        # Create test data
        await self._create_test_memories()
        
        logger.info("Phase 4 test environment ready")
    
    async def _create_test_memories(self):
        """Create test memories with different timestamps and metadata."""
        test_memories = [
            # Recent memories (last 24 hours)
            {
                "content": "Working on the new Python API project with FastAPI",
                "metadata": {"context": "work", "topics": ["python", "api", "fastapi"]},
                "days_ago": 0.5
            },
            {
                "content": "Meeting with John about the database migration strategy",
                "metadata": {"context": "work", "topics": ["database", "migration", "meeting"]},
                "days_ago": 0.8
            },
            # Last week memories
            {
                "content": "Started learning Rust for system programming",
                "metadata": {"context": "learning", "topics": ["rust", "programming", "systems"]},
                "days_ago": 5
            },
            {
                "content": "Completed the React frontend for the dashboard project",
                "metadata": {"context": "project", "topics": ["react", "frontend", "dashboard"]},
                "days_ago": 6
            },
            # Older memories (last month)
            {
                "content": "Presented machine learning workshop at the tech conference",
                "metadata": {"context": "presentation", "topics": ["ml", "workshop", "conference"]},
                "days_ago": 25
            },
            {
                "content": "Collaborated with Sarah on the data pipeline optimization",
                "metadata": {"context": "collaboration", "topics": ["data", "pipeline", "optimization"]},
                "days_ago": 28
            }
        ]
        
        # Store test memories
        for memory_data in test_memories:
            content = memory_data["content"]
            metadata = memory_data["metadata"]
            
            # Generate embedding
            embedding = await self.bge_client.embed_single(content)
            
            # Store with metadata
            memory_id = await self.db_client.store_memory(
                self.test_user, content, embedding, metadata
            )
            
            # Process for graph if available
            if self.graph_service:
                await self.graph_service.process_memory_for_graph(
                    str(memory_id), content, self.test_user
                )
            
            logger.debug(f"Created test memory: {memory_id}")
    
    async def test_filter_parsing(self):
        """Test filter string parsing."""
        logger.info("\n=== Testing Filter Parsing ===")
        
        test_cases = [
            ("last:7d", {"date_range": True}),
            ("context:work", {"context": "work"}),
            ("topics:python;rust", {"topics": ["python", "rust"]}),
            ("last:30d,context:project,recency:true", {
                "date_range": True,
                "context": "project",
                "recency_boost": True
            })
        ]
        
        for filter_str, expected in test_cases:
            parsed = parse_search_filters(filter_str)
            
            # Check expected fields
            success = True
            for key, value in expected.items():
                if key == "date_range" and value:
                    success = success and "date_range" in parsed
                else:
                    success = success and parsed.get(key) == value
            
            self.results["filter_tests"][filter_str] = success
            logger.info(f"  Filter '{filter_str}': {'✓' if success else '✗'}")
    
    async def test_metadata_filtering(self):
        """Test search with metadata filters."""
        logger.info("\n=== Testing Metadata Filtering ===")
        
        # Test context filter
        context_filter = {"context": "work"}
        result = await self.enhanced_search.search_with_filters(
            query="project",
            user_id=self.test_user,
            limit=5,
            filters=context_filter,
            expand_query=False
        )
        
        work_results = len(result.get("results", []))
        self.results["filter_tests"]["context_filter"] = work_results > 0
        logger.info(f"  Context filter (work): {work_results} results")
        
        # Test date range filter
        date_filter = {
            "date_range": (
                datetime.utcnow() - timedelta(days=7),
                datetime.utcnow()
            )
        }
        result = await self.enhanced_search.search_with_filters(
            query="programming",
            user_id=self.test_user,
            limit=5,
            filters=date_filter,
            expand_query=False
        )
        
        recent_results = len(result.get("results", []))
        self.results["filter_tests"]["date_filter"] = recent_results > 0
        logger.info(f"  Date filter (last 7 days): {recent_results} results")
        
        # Test topics filter
        topics_filter = {"topics": ["python", "api"]}
        result = await self.enhanced_search.search_with_filters(
            query="development",
            user_id=self.test_user,
            limit=5,
            filters=topics_filter,
            expand_query=False
        )
        
        topic_results = len(result.get("results", []))
        self.results["filter_tests"]["topics_filter"] = topic_results > 0
        logger.info(f"  Topics filter (python, api): {topic_results} results")
    
    async def test_query_expansion(self):
        """Test query expansion with context."""
        logger.info("\n=== Testing Query Expansion ===")
        
        # Set up conversation context
        await self.summary_manager.update_summary_async(
            self.test_user,
            ["Working on Python API development", "Using FastAPI framework"]
        )
        
        # Test with expansion enabled
        expanded_result = await self.enhanced_search.search_with_filters(
            query="backend development",
            user_id=self.test_user,
            limit=5,
            filters=None,
            expand_query=True
        )
        
        # Test without expansion
        basic_result = await self.enhanced_search.search_with_filters(
            query="backend development",
            user_id=self.test_user,
            limit=5,
            filters=None,
            expand_query=False
        )
        
        expanded_count = len(expanded_result.get("results", []))
        basic_count = len(basic_result.get("results", []))
        expansions = expanded_result.get("metadata", {}).get("expanded_queries", [])
        
        self.results["expansion_tests"]["expansion_generated"] = len(expansions) > 0
        self.results["expansion_tests"]["more_results"] = expanded_count >= basic_count
        
        logger.info(f"  Expansions generated: {len(expansions)}")
        logger.info(f"  Results with expansion: {expanded_count}")
        logger.info(f"  Results without expansion: {basic_count}")
        if expansions:
            logger.info(f"  Sample expansions: {expansions[:2]}")
    
    async def test_recency_boosting(self):
        """Test recency boosting feature."""
        logger.info("\n=== Testing Recency Boosting ===")
        
        # Search with recency boost
        boosted_result = await self.enhanced_search.search_with_filters(
            query="project",
            user_id=self.test_user,
            limit=5,
            filters={"recency_boost": True},
            expand_query=False
        )
        
        # Search without recency boost
        unboosted_result = await self.enhanced_search.search_with_filters(
            query="project",
            user_id=self.test_user,
            limit=5,
            filters={"recency_boost": False},
            expand_query=False
        )
        
        # Check if recent memories rank higher with boost
        if boosted_result.get("results") and unboosted_result.get("results"):
            boosted_top = boosted_result["results"][0]
            unboosted_top = unboosted_result["results"][0]
            
            # Recent memories should rank higher with boost
            boost_effect = (
                boosted_top.get("days_old", float('inf')) <= 
                unboosted_top.get("days_old", float('inf'))
            )
            
            self.results["recency_tests"]["boost_effect"] = boost_effect
            logger.info(f"  Recency boost effective: {'✓' if boost_effect else '✗'}")
        else:
            self.results["recency_tests"]["boost_effect"] = False
            logger.info("  Recency boost test: No results to compare")
    
    async def test_entity_search(self):
        """Test entity-based search."""
        logger.info("\n=== Testing Entity Search ===")
        
        # Create mock memory system
        class MockMemorySystem:
            def __init__(self, db, enhanced_search):
                self.db = db
                self.enhanced_search = enhanced_search
        
        mock_system = MockMemorySystem(self.db_client, self.enhanced_search)
        
        # Test entity search for "John"
        result = await search_by_entity_handler(
            memory_system=mock_system,
            entity_name="John",
            user_id=self.test_user,
            limit=5
        )
        
        entity_found = result.get("success", False)
        entity_results = len(result.get("results", []))
        
        self.results["entity_tests"]["entity_search_works"] = entity_found
        self.results["entity_tests"]["entity_results_count"] = entity_results
        
        logger.info(f"  Entity search (John): {entity_results} results")
        
        # Test entity that doesn't exist
        result = await search_by_entity_handler(
            memory_system=mock_system,
            entity_name="NonExistentEntity",
            user_id=self.test_user,
            limit=5
        )
        
        no_results = len(result.get("results", [])) == 0
        self.results["entity_tests"]["handles_missing_entity"] = no_results
        logger.info(f"  Non-existent entity handled: {'✓' if no_results else '✗'}")
    
    async def test_performance(self):
        """Test search performance metrics."""
        logger.info("\n=== Testing Performance ===")
        
        # Test basic search performance
        start_time = time.time()
        await self.enhanced_search.search_with_filters(
            query="Python programming",
            user_id=self.test_user,
            limit=5,
            filters=None,
            expand_query=False
        )
        basic_time = (time.time() - start_time) * 1000
        
        # Test enhanced search performance
        start_time = time.time()
        await self.enhanced_search.search_with_filters(
            query="Python programming",
            user_id=self.test_user,
            limit=5,
            filters={"context": "work", "recency_boost": True},
            expand_query=True
        )
        enhanced_time = (time.time() - start_time) * 1000
        
        self.results["performance"]["basic_search_ms"] = basic_time
        self.results["performance"]["enhanced_search_ms"] = enhanced_time
        self.results["performance"]["meets_target"] = enhanced_time < 1500  # Target: <1.5s
        
        logger.info(f"  Basic search: {basic_time:.0f}ms")
        logger.info(f"  Enhanced search: {enhanced_time:.0f}ms")
        logger.info(f"  Meets P95 target (<1500ms): {'✓' if enhanced_time < 1500 else '✗'}")
    
    def generate_report(self):
        """Generate test report."""
        logger.info("\n" + "="*50)
        logger.info("PHASE 4 ENHANCED SEARCH TEST REPORT")
        logger.info("="*50)
        
        # Filter tests
        filter_success = sum(1 for v in self.results["filter_tests"].values() if v)
        filter_total = len(self.results["filter_tests"])
        logger.info(f"\nFilter Tests: {filter_success}/{filter_total} passed")
        
        # Expansion tests
        expansion_success = sum(1 for v in self.results["expansion_tests"].values() if v)
        expansion_total = len(self.results["expansion_tests"])
        logger.info(f"Expansion Tests: {expansion_success}/{expansion_total} passed")
        
        # Recency tests
        recency_success = sum(1 for v in self.results["recency_tests"].values() if v)
        recency_total = len(self.results["recency_tests"])
        logger.info(f"Recency Tests: {recency_success}/{recency_total} passed")
        
        # Entity tests
        entity_success = sum(1 for v in self.results["entity_tests"].values() if isinstance(v, bool) and v)
        entity_total = sum(1 for v in self.results["entity_tests"].values() if isinstance(v, bool))
        logger.info(f"Entity Tests: {entity_success}/{entity_total} passed")
        
        # Performance
        logger.info(f"\nPerformance:")
        logger.info(f"  Basic Search: {self.results['performance'].get('basic_search_ms', 0):.0f}ms")
        logger.info(f"  Enhanced Search: {self.results['performance'].get('enhanced_search_ms', 0):.0f}ms")
        logger.info(f"  Meets Target: {'✓' if self.results['performance'].get('meets_target', False) else '✗'}")
        
        # Overall success
        total_passed = filter_success + expansion_success + recency_success + entity_success
        total_tests = filter_total + expansion_total + recency_total + entity_total
        success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        logger.info(f"\nOverall Success Rate: {success_rate:.1f}% ({total_passed}/{total_tests})")
        
        # Save report
        with open("/home/<USER>/dev/tools/mcp-mem0/tests/phase_4_test_report.json", "w") as f:
            json.dump(self.results, f, indent=2)
        
        logger.info("\nReport saved to: tests/phase_4_test_report.json")
        
        return success_rate >= 75  # Consider successful if 75% tests pass
    
    async def run_all_tests(self):
        """Run all Phase 4 tests."""
        try:
            await self.setup()
            
            # Run test suites
            await self.test_filter_parsing()
            await self.test_metadata_filtering()
            await self.test_query_expansion()
            await self.test_recency_boosting()
            await self.test_entity_search()
            await self.test_performance()
            
            # Generate report
            success = self.generate_report()
            
            # Cleanup
            await self.db_client.close()
            if hasattr(self.graph_service, 'close'):
                await self.graph_service.close()
            
            return success
            
        except Exception as e:
            logger.error(f"Test suite failed: {e}")
            return False


async def main():
    """Main test entry point."""
    tester = TestPhase4EnhancedSearch()
    success = await tester.run_all_tests()
    
    print("\n" + "="*50)
    print("PHASE 4 TESTING COMPLETE")
    print("="*50)
    print(f"Result: {'PASSED ✓' if success else 'FAILED ✗'}")
    
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)