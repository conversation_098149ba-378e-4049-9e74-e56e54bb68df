#!/usr/bin/env python3
"""
Integration test for coroutine serialization fix with real memory system components.

This test validates that the coroutine serialization fix works correctly
in the context of the actual memory system operations.
"""

import asyncio
import json
import sys
import os
import time
from typing import Any, Dict, List

# Add src directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from performance_monitor import PerformanceMonitor


class MockMemoryOperation:
    """Mock memory operation that simulates real memory system behavior."""
    
    def __init__(self, operation_type: str):
        self.operation_type = operation_type
    
    async def extract_memories(self, text: str) -> List[str]:
        """Mock memory extraction that returns coroutine."""
        await asyncio.sleep(0.01)  # Simulate processing time
        return [f"extracted_memory_from_{text}", f"additional_context_{text}"]
    
    async def search_memories(self, query: str) -> List[Dict[str, Any]]:
        """Mock memory search that returns coroutine."""
        await asyncio.sleep(0.005)  # Simulate search time
        return [
            {"content": f"result_for_{query}", "score": 0.95},
            {"content": f"related_to_{query}", "score": 0.87}
        ]
    
    async def update_memory(self, memory_id: str, content: str) -> Dict[str, Any]:
        """Mock memory update that returns coroutine."""
        await asyncio.sleep(0.008)  # Simulate update time
        return {
            "id": memory_id,
            "content": content,
            "updated": True,
            "timestamp": time.time()
        }


class IntegrationTestSuite:
    """Integration test suite for coroutine serialization fix."""
    
    def __init__(self):
        self.monitor = PerformanceMonitor()
        self.mock_memory = MockMemoryOperation("test")
    
    async def test_memory_extraction_with_lambda(self):
        """Test memory extraction using lambda that returns coroutine."""
        print("Testing memory extraction with lambda...")
        
        # This pattern simulates how memory operations might be wrapped
        extract_lambda = lambda text: self.mock_memory.extract_memories(text)
        
        result = await self.monitor.track_operation(
            "memory_extraction", extract_lambda, "test input text"
        )
        
        # Validate result
        assert isinstance(result, list)
        assert len(result) == 2
        assert "extracted_memory_from_test input text" in result
        assert "additional_context_test input text" in result
        
        # Test JSON serialization
        json_str = json.dumps({"extraction_result": result})
        parsed = json.loads(json_str)
        assert parsed["extraction_result"] == result
        
        print("✅ Memory extraction with lambda test passed")
        return True
    
    async def test_memory_search_with_wrapper(self):
        """Test memory search using wrapper function that returns coroutine."""
        print("Testing memory search with wrapper function...")
        
        def search_wrapper(query: str):
            """Wrapper function that returns a coroutine."""
            return self.mock_memory.search_memories(query)
        
        result = await self.monitor.track_operation(
            "memory_search", search_wrapper, "test query"
        )
        
        # Validate result
        assert isinstance(result, list)
        assert len(result) == 2
        assert result[0]["content"] == "result_for_test query"
        assert result[0]["score"] == 0.95
        assert result[1]["content"] == "related_to_test query"
        assert result[1]["score"] == 0.87
        
        # Test JSON serialization
        json_str = json.dumps({"search_results": result})
        parsed = json.loads(json_str)
        assert parsed["search_results"] == result
        
        print("✅ Memory search with wrapper function test passed")
        return True
    
    async def test_memory_update_with_closure(self):
        """Test memory update using closure that returns coroutine."""
        print("Testing memory update with closure...")
        
        def create_update_closure(memory_id: str):
            """Create a closure that returns a coroutine."""
            def update_closure(content: str):
                return self.mock_memory.update_memory(memory_id, content)
            return update_closure
        
        update_func = create_update_closure("test_memory_123")
        
        result = await self.monitor.track_operation(
            "memory_update", update_func, "updated content"
        )
        
        # Validate result
        assert isinstance(result, dict)
        assert result["id"] == "test_memory_123"
        assert result["content"] == "updated content"
        assert result["updated"] is True
        assert "timestamp" in result
        
        # Test JSON serialization
        json_str = json.dumps({"update_result": result})
        parsed = json.loads(json_str)
        assert parsed["update_result"] == result
        
        print("✅ Memory update with closure test passed")
        return True
    
    async def test_chained_operations(self):
        """Test chained memory operations that return coroutines."""
        print("Testing chained memory operations...")
        
        async def chained_operation(input_text: str) -> Dict[str, Any]:
            """Simulate a complex operation that chains multiple async calls."""
            # Extract memories
            extracted = await self.mock_memory.extract_memories(input_text)
            
            # Search for related memories
            search_results = await self.mock_memory.search_memories(extracted[0])
            
            # Update a memory
            update_result = await self.mock_memory.update_memory(
                "chain_test", f"Processed: {input_text}"
            )
            
            return {
                "input": input_text,
                "extracted_count": len(extracted),
                "search_count": len(search_results),
                "update_success": update_result["updated"],
                "processing_chain": "extract->search->update"
            }
        
        def chain_wrapper(text: str):
            """Wrapper that returns the chained operation coroutine."""
            return chained_operation(text)
        
        result = await self.monitor.track_operation(
            "chained_operations", chain_wrapper, "complex input text"
        )
        
        # Validate result
        assert result["input"] == "complex input text"
        assert result["extracted_count"] == 2
        assert result["search_count"] == 2
        assert result["update_success"] is True
        assert result["processing_chain"] == "extract->search->update"
        
        # Test JSON serialization
        json_str = json.dumps({"chain_result": result})
        parsed = json.loads(json_str)
        assert parsed["chain_result"] == result
        
        print("✅ Chained memory operations test passed")
        return True
    
    async def test_performance_tracking_accuracy(self):
        """Test that performance tracking works correctly with coroutines."""
        print("Testing performance tracking accuracy...")
        
        async def timed_memory_operation(duration: float) -> Dict[str, Any]:
            """Memory operation with controlled timing."""
            start = time.time()
            await asyncio.sleep(duration)
            end = time.time()
            return {
                "operation": "timed_memory_op",
                "requested_duration": duration,
                "actual_duration": end - start,
                "success": True
            }
        
        def timing_wrapper(duration: float):
            return timed_memory_operation(duration)
        
        # Test with 50ms operation
        test_duration = 0.05
        result = await self.monitor.track_operation(
            "timing_test", timing_wrapper, test_duration
        )
        
        # Validate result
        assert result["operation"] == "timed_memory_op"
        assert result["requested_duration"] == test_duration
        assert result["success"] is True
        
        # Check performance metrics
        recent_ops = self.monitor.get_recent_operations(limit=1)
        assert len(recent_ops) > 0
        
        last_op = recent_ops[-1]
        assert last_op["operation"] == "timing_test"
        assert last_op["success"] is True
        # Should be approximately 50ms (allowing for some variance)
        assert 40 <= last_op["latency_ms"] <= 100
        
        # Test JSON serialization
        json_str = json.dumps({"timing_result": result})
        parsed = json.loads(json_str)
        assert parsed["timing_result"] == result
        
        print("✅ Performance tracking accuracy test passed")
        return True
    
    async def run_all_tests(self):
        """Run all integration tests."""
        tests = [
            ("Memory Extraction with Lambda", self.test_memory_extraction_with_lambda),
            ("Memory Search with Wrapper", self.test_memory_search_with_wrapper),
            ("Memory Update with Closure", self.test_memory_update_with_closure),
            ("Chained Operations", self.test_chained_operations),
            ("Performance Tracking Accuracy", self.test_performance_tracking_accuracy),
        ]
        
        results = []
        for test_name, test_func in tests:
            try:
                success = await test_func()
                results.append((test_name, success))
            except Exception as e:
                print(f"❌ {test_name} failed with exception: {e}")
                import traceback
                traceback.print_exc()
                results.append((test_name, False))
        
        return results


async def main():
    """Main integration test runner."""
    print("🧪 Running integration tests for coroutine serialization fix...\n")
    
    test_suite = IntegrationTestSuite()
    results = await test_suite.run_all_tests()
    
    # Summary
    print("\n" + "=" * 70)
    print("INTEGRATION TEST SUMMARY:")
    print("=" * 70)
    
    passed = 0
    for test_name, result in results:
        status = "PASSED" if result else "FAILED"
        emoji = "✅" if result else "❌"
        print(f"{emoji} {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nTotal: {passed}/{len(results)} integration tests passed")
    
    if passed == len(results):
        print("🎉 All integration tests passed!")
        print("\n✅ Coroutine serialization fix is working correctly in real scenarios!")
        print("✅ Memory operations can be safely wrapped and tracked!")
        print("✅ JSON serialization works for all operation results!")
        return 0
    else:
        print("❌ Some integration tests failed!")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
