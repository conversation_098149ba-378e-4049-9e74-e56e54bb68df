#!/usr/bin/env python3
"""
Test the real MCP server directly via MCP protocol
"""

import asyncio
import json
import httpx
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from mcp.client.sse import sse_client

async def test_spark_memory_mcp():
    """Test the actual spark-memory MCP server."""
    
    print("🧪 Testing Real Spark Memory MCP Server")
    print("=" * 50)
    
    # The JIRA ticket text that should extract 2 candidates
    test_text = "User analyzed JIRA ticket CS1000-7209 from MegaOne Store customer support. Customer rod<PERSON>19<PERSON> complained about receiving polishing pad without water holes despite product description advertising holes for wet polishing."
    
    try:
        # Connect to the MCP server via SSE
        print("🔌 Connecting to MCP server at http://************:8050/sse...")
        
        async with sse_client("http://************:8050/sse") as (read, write):
            async with ClientSession(read, write) as session:
                print("✅ Connected to MCP server")
                
                # Initialize the session
                await session.initialize()
                print("✅ Session initialized")
                
                # List available tools
                tools_result = await session.list_tools()
                print(f"📋 Available tools: {[tool.name for tool in tools_result.tools]}")
                
                # Check if add_memories tool is available
                if not any(tool.name == "add_memories" for tool in tools_result.tools):
                    print("❌ add_memories tool not found!")
                    return False
                
                print("✅ add_memories tool found")
                print()
                
                # Test add_memories
                print("📝 Testing add_memories with JIRA ticket text...")
                print(f"Text: {test_text[:100]}...")
                print()
                
                # Call add_memories tool
                result = await session.call_tool(
                    "add_memories",
                    arguments={
                        "text": test_text,
                        "user_id": "test_user_direct"
                    }
                )
                
                print("📊 MCP Server Response:")
                print(f"Content: {result.content}")
                
                # Parse the response
                if result.content and len(result.content) > 0:
                    response_text = result.content[0].text
                    try:
                        parsed_response = json.loads(response_text)
                        
                        print(f"\n✅ Parsed Response:")
                        print(f"Success: {parsed_response.get('success')}")
                        print(f"Candidates processed: {parsed_response.get('candidates_processed')}")
                        print(f"Operations: {parsed_response.get('operations')}")
                        print(f"User ID: {parsed_response.get('user_id')}")
                        
                        candidates_processed = parsed_response.get('candidates_processed', 0)
                        
                        if candidates_processed > 0:
                            print(f"\n🎉 SUCCESS: MCP server processed {candidates_processed} candidates!")
                            print("✅ The memory extraction is working correctly")
                            return True
                        else:
                            print(f"\n❌ ISSUE: Still getting 0 candidates processed")
                            print("🔍 The MCP server is responding but not extracting memories")
                            return False
                            
                    except json.JSONDecodeError:
                        print(f"❌ Failed to parse response as JSON: {response_text}")
                        return False
                else:
                    print("❌ No content in MCP response")
                    return False
                    
    except Exception as e:
        print(f"❌ MCP connection failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_spark_memory_mcp())
    if success:
        print("\n✅ MCP server is working correctly!")
    else:
        print("\n❌ MCP server test failed - investigating container logs...")
        print("🔍 Check: docker logs spark-mcp-server")