#!/usr/bin/env python3
"""
Simple test script to verify individual MCP tools.
"""

import asyncio
import json
import sys

try:
    from fastmcp import Client
    from fastmcp.client.transports import SSETransport
except ImportError:
    print("FastMCP not available. Installing...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "fastmcp", "--break-system-packages"])
    from fastmcp import Client
    from fastmcp.client.transports import SSETransport

async def test_tool(tool_name, arguments=None):
    """Test a single MCP tool."""
    if arguments is None:
        arguments = {}
    
    transport = SSETransport("http://localhost:8050/sse")
    
    try:
        async with Client(transport) as client:
            print(f"🔍 Testing {tool_name}...")
            result = await client.call_tool(tool_name, arguments)
            print(f"✅ {tool_name} succeeded")
            
            # Parse and display result
            response_text = result[0].text
            try:
                response_data = json.loads(response_text)
                print(f"   Response: {json.dumps(response_data, indent=2)[:200]}...")
            except:
                print(f"   Raw response: {response_text[:200]}...")
            
            return True
            
    except Exception as e:
        print(f"❌ {tool_name} failed: {e}")
        return False

async def main():
    """Test all MCP tools individually."""
    print("🚀 Simple MCP Tool Tests")
    print("=" * 40)
    
    # Test each tool
    tools_to_test = [
        ("health_check", {}),
        ("add_memories", {"text": "Test memory", "user_id": "test_user"}),
        ("search_memory", {"query": "test", "user_id": "test_user", "limit": 3}),
        ("list_memories", {"user_id": "test_user", "limit": 10}),
        ("get_performance_stats", {}),
        ("get_conversation_summary", {"user_id": "test_user"}),
        ("delete_all_memories", {"user_id": "test_user"})
    ]
    
    results = {}
    for tool_name, args in tools_to_test:
        results[tool_name] = await test_tool(tool_name, args)
        print()  # Add spacing
    
    # Summary
    print("=" * 40)
    print("📊 SUMMARY")
    print("=" * 40)
    
    passed = sum(1 for success in results.values() if success)
    total = len(results)
    
    for tool_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{tool_name:20} {status}")
    
    print(f"\nOverall: {passed}/{total} tools working")
    
    if passed == total:
        print("🎉 All tools working!")
        return 0
    else:
        print(f"⚠️  {total - passed} tools failed")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️  Tests interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test runner failed: {e}")
        sys.exit(1)
