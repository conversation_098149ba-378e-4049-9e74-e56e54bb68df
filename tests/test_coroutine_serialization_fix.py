#!/usr/bin/env python3
"""
Integration test for coroutine serialization fix.

This test validates that the performance monitor correctly handles coroutines
after fixing the coroutine serialization issue.
"""

import asyncio
import json
import sys
import os

# Add src directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))


def test_json_serialization_basic():
    """Test that basic JSON serialization works."""
    test_data = {
        "success": True,
        "result": "test_value",
        "metadata": {"time": 123.45}
    }
    
    try:
        json_str = json.dumps(test_data)
        parsed = json.loads(json_str)
        assert parsed == test_data
        print("✅ Basic JSON serialization works")
    except Exception as e:
        print(f"❌ Basic JSON serialization failed: {e}")
        raise


def test_performance_monitor_coroutine_handling():
    """
    Test the performance monitor's ability to handle coroutines correctly.
    """
    from performance_monitor import PerformanceMonitor
    
    monitor = PerformanceMonitor()
    
    async def test_async_function(value):
        """Test async function."""
        await asyncio.sleep(0.001)  # Minimal async operation
        return {"result": value, "type": "async_result"}
    
    def test_lambda_returning_coroutine(value):
        """Test lambda that returns a coroutine."""
        return test_async_function(value)
    
    async def run_test():
        # Test 1: Direct async function
        result1 = await monitor.track_operation(
            "test_async", test_async_function, "direct_async"
        )
        assert result1["result"] == "direct_async"
        assert result1["type"] == "async_result"
        
        # Test 2: Lambda returning coroutine (the fix we implemented)
        result2 = await monitor.track_operation(
            "test_lambda_coroutine", test_lambda_returning_coroutine, "lambda_coroutine"
        )
        assert result2["result"] == "lambda_coroutine"
        assert result2["type"] == "async_result"
        
        return True
    
    # Run the async test
    result = asyncio.run(run_test())
    assert result is True
    print("✅ Performance monitor coroutine handling test passed")


if __name__ == "__main__":
    """Run the tests directly."""
    print("Running coroutine serialization fix tests...")
    
    try:
        # Test 1: Basic JSON serialization
        test_json_serialization_basic()
        
        # Test 2: Performance monitor coroutine handling
        test_performance_monitor_coroutine_handling()
        
        print("\n🎉 All coroutine serialization fix tests passed!")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        sys.exit(1)