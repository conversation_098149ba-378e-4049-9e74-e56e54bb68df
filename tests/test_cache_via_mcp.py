#!/usr/bin/env python3
"""
Test Cache Performance via MCP Interface

Tests the LLM caching system by adding memories and measuring performance
improvements from cache hits vs cache misses.
"""

import asyncio
import time
import statistics
import logging
import sys
import json
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add the src directory to Python path to import our modules
sys.path.insert(0, './src')

async def test_cache_performance():
    """
    Test cache performance by measuring memory addition latency.
    
    This simulates what would happen with repeated patterns vs unique patterns.
    """
    try:
        # Import our modules
        from main_new import get_server_user_id
        from memory_database import MemoryDatabase
        from memory_extraction import MemoryExtractionModule
        from memory_update import MemoryUpdateModule
        from bge_embedding_client import BGEEmbeddingClient
        from llm_cache_service import initialize_llm_cache
        from simple_llm_client import get_spark_memory_client
        
        logger.info("=== Cache Performance Test Suite ===")
        
        # Initialize components
        user_id = get_server_user_id()
        
        # Initialize LLM cache service
        cache_service = await initialize_llm_cache()
        logger.info("✅ LLM cache service initialized")
        
        # Initialize BGE client
        bge_client = BGEEmbeddingClient()
        logger.info("✅ BGE embedding client initialized")
        
        # Initialize database
        db = MemoryDatabase(bge_client)
        await db.initialize()
        logger.info("✅ Memory database initialized")
        
        # Initialize LLM client
        llm_client = get_spark_memory_client()
        logger.info("✅ LLM client initialized")
        
        # Initialize memory modules
        extraction_module = MemoryExtractionModule(llm_client, cache_service)
        update_module = MemoryUpdateModule(llm_client, db, cache_service)
        logger.info("✅ Memory modules initialized")
        
        # Test messages
        unique_messages = [
            f"I prefer React over Vue for frontend development - test {i}"
            for i in range(5)
        ]
        
        repeated_messages = [
            "I prefer React over Vue for frontend development",
            "I work as a software engineer at TechCorp",
            "I specialize in Python and machine learning"
        ] * 3  # Repeat 3 times for cache hits
        
        async def measure_memory_operation(messages, test_name):
            """Measure latency for memory operations."""
            latencies = []
            
            for i, message in enumerate(messages):
                start_time = time.time()
                
                try:
                    # Extract memories from message
                    extracted = await extraction_module.extract_memories(
                        previous_message="",
                        current_message=message,
                        conversation_summary="",
                        recent_context="",
                        user_id=user_id
                    )
                    
                    # Process updates for each extracted memory
                    for memory_text in extracted:
                        await update_module.process_memory_update(
                            candidate_memory=memory_text,
                            user_id=user_id
                        )
                    
                    end_time = time.time()
                    latency_ms = (end_time - start_time) * 1000
                    latencies.append(latency_ms)
                    
                    logger.info(f"{test_name} iteration {i+1}: {latency_ms:.1f}ms")
                    
                except Exception as e:
                    logger.error(f"Error in {test_name} iteration {i+1}: {e}")
                    continue
                
                # Small delay between operations
                await asyncio.sleep(0.1)
            
            return latencies
        
        # Test 1: Baseline (unique messages - no cache hits)
        logger.info("\n📊 Phase 1: Testing baseline performance (no cache hits)")
        await cache_service.invalidate_cache()  # Clear cache
        baseline_latencies = await measure_memory_operation(unique_messages, "Baseline")
        
        # Test 2: Cached performance (repeated messages - should get cache hits)
        logger.info("\n📊 Phase 2: Testing cached performance (with cache hits)")
        cached_latencies = await measure_memory_operation(repeated_messages, "Cached")
        
        # Calculate statistics
        def calculate_stats(latencies, name):
            if not latencies:
                return None
            
            stats = {
                "test_name": name,
                "count": len(latencies),
                "min_ms": min(latencies),
                "max_ms": max(latencies),
                "mean_ms": statistics.mean(latencies),
                "median_ms": statistics.median(latencies),
                "p95_ms": statistics.quantiles(latencies, n=20)[18] if len(latencies) >= 20 else max(latencies),
                "std_dev_ms": statistics.stdev(latencies) if len(latencies) > 1 else 0
            }
            return stats
        
        baseline_stats = calculate_stats(baseline_latencies, "Baseline")
        cached_stats = calculate_stats(cached_latencies, "Cached")
        
        # Get cache metrics
        cache_metrics = cache_service.get_metrics()
        
        # Generate report
        print("\n" + "="*80)
        print("🚀 CACHE PERFORMANCE TEST RESULTS")
        print("="*80)
        
        if baseline_stats and cached_stats:
            print(f"\n📈 Performance Comparison:")
            print(f"  Baseline Mean: {baseline_stats['mean_ms']:.1f}ms")
            print(f"  Cached Mean:   {cached_stats['mean_ms']:.1f}ms")
            
            improvement = ((baseline_stats['mean_ms'] - cached_stats['mean_ms']) / baseline_stats['mean_ms']) * 100
            print(f"  Improvement:   {improvement:.1f}%")
            
            if baseline_stats.get('p95_ms') and cached_stats.get('p95_ms'):
                p95_improvement = ((baseline_stats['p95_ms'] - cached_stats['p95_ms']) / baseline_stats['p95_ms']) * 100
                print(f"  P95 Baseline:  {baseline_stats['p95_ms']:.1f}ms")
                print(f"  P95 Cached:    {cached_stats['p95_ms']:.1f}ms")
                print(f"  P95 Improvement: {p95_improvement:.1f}%")
        
        print(f"\n💾 Cache Performance:")
        cache_stats = cache_metrics.get("cache_metrics", {})
        if cache_stats:
            hit_rate = cache_stats.get("cache_hits", 0) / max(cache_stats.get("total_requests", 1), 1)
            print(f"  Hit Rate:      {hit_rate*100:.1f}%")
            print(f"  Cache Hits:    {cache_stats.get('cache_hits', 0)}")
            print(f"  Cache Misses:  {cache_stats.get('cache_misses', 0)}")
            print(f"  Time Saved:    {cache_stats.get('total_time_saved_ms', 0)/1000:.1f}s")
        
        # Save results
        results = {
            "timestamp": datetime.now().isoformat(),
            "baseline_stats": baseline_stats,
            "cached_stats": cached_stats,
            "cache_metrics": cache_metrics
        }
        
        with open("cache_test_results.json", "w") as f:
            json.dump(results, f, indent=2)
        
        print(f"\n📁 Results saved to: cache_test_results.json")
        print("="*80)
        
        # Cleanup
        await cache_service.close()
        await db.close()
        await bge_client.close()
        
        logger.info("✅ Cache performance test completed successfully")
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(test_cache_performance())