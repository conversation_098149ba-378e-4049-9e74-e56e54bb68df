#!/usr/bin/env python3
"""
LLM Cache Performance Test Suite

Tests the LLM caching system to validate performance improvements
and measure actual P95 latency reduction.
"""

import asyncio
import json
import time
import logging
import statistics
from typing import List, Dict, Any
import httpx

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CachePerformanceTester:
    """
    Test suite for validating LLM cache performance improvements.
    """
    
    def __init__(self, server_url: str = "http://localhost:8050"):
        """Initialize performance tester."""
        self.server_url = server_url
        self.client = httpx.AsyncClient(timeout=30.0)
        self.test_results = []
        
    async def run_performance_test_suite(self) -> Dict[str, Any]:
        """
        Run comprehensive cache performance test suite.
        
        Returns:
            Test results with performance metrics
        """
        logger.info("Starting LLM Cache Performance Test Suite")
        
        # Test scenarios
        test_results = {}
        
        try:
            # 1. Baseline performance (no cache)
            logger.info("Phase 1: Testing baseline performance")
            await self.invalidate_cache()
            baseline_results = await self.test_memory_operations_latency("baseline", iterations=10)
            test_results["baseline"] = baseline_results
            
            # 2. Cache warming
            logger.info("Phase 2: Warming cache with common patterns")
            warm_results = await self.warm_cache_patterns()
            test_results["cache_warming"] = warm_results
            
            # 3. Cached performance
            logger.info("Phase 3: Testing cached performance")
            cached_results = await self.test_memory_operations_latency("cached", iterations=10, use_patterns=True)
            test_results["cached"] = cached_results
            
            # 4. Performance comparison
            logger.info("Phase 4: Analyzing performance improvements")
            comparison = self.analyze_performance_improvement(baseline_results, cached_results)
            test_results["performance_analysis"] = comparison
            
            # 5. Cache metrics
            logger.info("Phase 5: Collecting cache metrics")
            cache_metrics = await self.get_cache_metrics()
            test_results["cache_metrics"] = cache_metrics
            
            # 6. Generate report
            report = await self.generate_test_report()
            test_results["cache_performance_report"] = report
            
            logger.info("Cache performance test suite completed successfully")
            return test_results
            
        except Exception as e:
            logger.error(f"Test suite failed: {e}")
            test_results["error"] = str(e)
            return test_results
    
    async def test_memory_operations_latency(self, 
                                           test_phase: str, 
                                           iterations: int = 10,
                                           use_patterns: bool = False) -> Dict[str, Any]:
        """
        Test memory operation latency with multiple iterations.
        
        Args:
            test_phase: Name of test phase
            iterations: Number of test iterations
            use_patterns: Whether to use repeated patterns for cache testing
            
        Returns:
            Latency test results
        """
        latencies = []
        
        # Test data patterns
        if use_patterns:
            # Use repeated patterns to test cache hits
            test_messages = [
                "I prefer React over Vue for frontend development",
                "I work as a software engineer at TechCorp", 
                "I specialize in Python and machine learning",
                "I prefer React over Vue for frontend development",  # Repeat for cache hit
                "I work as a software engineer at TechCorp",         # Repeat for cache hit
                "I use Docker for containerization in my projects"
            ] * 2  # Duplicate to ensure cache hits
        else:
            # Use unique messages to avoid cache hits
            test_messages = []
            for i in range(iterations):
                test_messages.extend([
                    f"I prefer React over Vue for frontend development - test {i}",
                    f"I work as a software engineer at TechCorp - iteration {i}",
                    f"I specialize in Python and machine learning - run {i}",
                    f"I use Docker for containerization - test {i}",
                    f"I am learning about memory systems - iteration {i}",
                    f"I enjoy working on performance optimization - run {i}"
                ])
            test_messages = test_messages[:iterations]
        
        for i, message in enumerate(test_messages):
            try:
                start_time = time.time()
                
                # Call add_memories endpoint
                response = await self.client.post(
                    f"{self.server_url}/tools/add_memories",
                    json={"text": message}
                )
                response.raise_for_status()
                
                end_time = time.time()
                latency_ms = (end_time - start_time) * 1000
                latencies.append(latency_ms)
                
                logger.debug(f"{test_phase} iteration {i+1}: {latency_ms:.1f}ms")
                
                # Small delay between requests
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Error in {test_phase} iteration {i+1}: {e}")
                continue
        
        if not latencies:
            return {"error": "No successful operations"}
        
        # Calculate statistics
        results = {
            "test_phase": test_phase,
            "iterations": len(latencies),
            "latencies_ms": latencies,
            "min_ms": min(latencies),
            "max_ms": max(latencies),
            "mean_ms": statistics.mean(latencies),
            "median_ms": statistics.median(latencies),
            "p95_ms": statistics.quantiles(latencies, n=20)[18] if len(latencies) >= 20 else max(latencies),  # 95th percentile
            "std_dev_ms": statistics.stdev(latencies) if len(latencies) > 1 else 0
        }
        
        logger.info(f"{test_phase} results: P95={results['p95_ms']:.1f}ms, Mean={results['mean_ms']:.1f}ms")
        return results
    
    async def warm_cache_patterns(self) -> Dict[str, Any]:
        """Warm cache with common patterns."""
        try:
            response = await self.client.post(
                f"{self.server_url}/tools/cache_warm_common_patterns",
                json={}
            )
            response.raise_for_status()
            result = response.json()
            
            logger.info(f"Cache warming completed: {result.get('message', 'success')}")
            return result
            
        except Exception as e:
            logger.error(f"Cache warming failed: {e}")
            return {"error": str(e)}
    
    async def invalidate_cache(self) -> Dict[str, Any]:
        """Invalidate all cache entries."""
        try:
            response = await self.client.post(
                f"{self.server_url}/tools/invalidate_llm_cache",
                json={}
            )
            response.raise_for_status()
            result = response.json()
            
            logger.info("Cache invalidated successfully")
            return result
            
        except Exception as e:
            logger.error(f"Cache invalidation failed: {e}")
            return {"error": str(e)}
    
    async def get_cache_metrics(self) -> Dict[str, Any]:
        """Get current cache performance metrics."""
        try:
            response = await self.client.post(
                f"{self.server_url}/tools/get_performance_stats",
                json={}
            )
            response.raise_for_status()
            result = response.json()
            
            return result.get("cache_performance", {})
            
        except Exception as e:
            logger.error(f"Failed to get cache metrics: {e}")
            return {"error": str(e)}
    
    async def generate_test_report(self) -> Dict[str, Any]:
        """Generate cache performance report."""
        try:
            response = await self.client.post(
                f"{self.server_url}/tools/get_cache_performance_report",
                json={}
            )
            response.raise_for_status()
            return response.json()
            
        except Exception as e:
            logger.error(f"Failed to generate report: {e}")
            return {"error": str(e)}
    
    def analyze_performance_improvement(self, 
                                      baseline: Dict[str, Any], 
                                      cached: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze performance improvement from baseline to cached.
        
        Args:
            baseline: Baseline performance results
            cached: Cached performance results
            
        Returns:
            Performance improvement analysis
        """
        if "error" in baseline or "error" in cached:
            return {"error": "Cannot analyze - missing baseline or cached data"}
        
        baseline_p95 = baseline.get("p95_ms", 0)
        cached_p95 = cached.get("p95_ms", 0)
        baseline_mean = baseline.get("mean_ms", 0)
        cached_mean = cached.get("mean_ms", 0)
        
        # Calculate improvements
        p95_improvement = 0
        mean_improvement = 0
        
        if baseline_p95 > 0:
            p95_improvement = ((baseline_p95 - cached_p95) / baseline_p95) * 100
        
        if baseline_mean > 0:
            mean_improvement = ((baseline_mean - cached_mean) / baseline_mean) * 100
        
        # Performance targets
        p95_target_ms = 1000
        meets_p95_target = cached_p95 <= p95_target_ms
        
        analysis = {
            "baseline_performance": {
                "p95_ms": baseline_p95,
                "mean_ms": baseline_mean
            },
            "cached_performance": {
                "p95_ms": cached_p95,
                "mean_ms": cached_mean
            },
            "improvements": {
                "p95_improvement_percent": p95_improvement,
                "mean_improvement_percent": mean_improvement,
                "p95_reduction_ms": baseline_p95 - cached_p95,
                "mean_reduction_ms": baseline_mean - cached_mean
            },
            "target_analysis": {
                "p95_target_ms": p95_target_ms,
                "meets_p95_target": meets_p95_target,
                "target_gap_ms": max(0, cached_p95 - p95_target_ms)
            },
            "performance_rating": self._get_performance_rating(p95_improvement, meets_p95_target)
        }
        
        return analysis
    
    def _get_performance_rating(self, improvement_percent: float, meets_target: bool) -> str:
        """Get performance rating based on improvement."""
        if meets_target and improvement_percent >= 70:
            return "EXCELLENT - Target achieved with major improvement"
        elif meets_target and improvement_percent >= 50:
            return "VERY_GOOD - Target achieved with significant improvement"
        elif meets_target:
            return "GOOD - Target achieved"
        elif improvement_percent >= 70:
            return "GOOD - Major improvement but target not yet achieved"
        elif improvement_percent >= 50:
            return "FAIR - Significant improvement but target not achieved"
        elif improvement_percent >= 25:
            return "MARGINAL - Some improvement but target not achieved"
        else:
            return "POOR - Minimal or no improvement"
    
    async def close(self):
        """Close HTTP client."""
        await self.client.aclose()

async def main():
    """Run cache performance test suite."""
    tester = CachePerformanceTester()
    
    try:
        # Run test suite
        results = await tester.run_performance_test_suite()
        
        # Print summary
        print("\n" + "="*80)
        print("LLM CACHE PERFORMANCE TEST RESULTS")
        print("="*80)
        
        if "performance_analysis" in results:
            analysis = results["performance_analysis"]
            print(f"\nPerformance Analysis:")
            print(f"  Baseline P95: {analysis['baseline_performance']['p95_ms']:.1f}ms")
            print(f"  Cached P95:   {analysis['cached_performance']['p95_ms']:.1f}ms")
            print(f"  P95 Improvement: {analysis['improvements']['p95_improvement_percent']:.1f}%")
            print(f"  P95 Reduction:   {analysis['improvements']['p95_reduction_ms']:.1f}ms")
            print(f"  Meets Target:    {analysis['target_analysis']['meets_p95_target']}")
            print(f"  Rating:          {analysis['performance_rating']}")
        
        if "cache_metrics" in results:
            cache_stats = results["cache_metrics"].get("cache_metrics", {})
            if cache_stats:
                print(f"\nCache Performance:")
                print(f"  Hit Rate:        {cache_stats.get('hit_rate', 0)*100:.1f}%")
                print(f"  Cache Hits:      {cache_stats.get('cache_hits', 0)}")
                print(f"  Cache Misses:    {cache_stats.get('cache_misses', 0)}")
                print(f"  Time Saved:      {cache_stats.get('total_time_saved_ms', 0)/1000:.1f}s")
        
        # Save detailed results
        with open("cache_performance_test_results.json", "w") as f:
            json.dump(results, f, indent=2)
        
        print(f"\nDetailed results saved to: cache_performance_test_results.json")
        print("="*80)
        
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        print(f"ERROR: {e}")
    
    finally:
        await tester.close()

if __name__ == "__main__":
    asyncio.run(main())