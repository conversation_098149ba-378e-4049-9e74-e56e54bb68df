#!/usr/bin/env python3
"""
Test the actual MCP interface to see what's happening
"""

import asyncio
import json
import httpx

async def test_mcp_add_memories():
    """Test the MCP interface directly."""
    
    print("🧪 Testing MCP Interface - add_memories")
    print("=" * 50)
    
    # Test data - the JIRA ticket text
    test_text = "User analyzed JIRA ticket CS1000-7209 from MegaOne Store customer support. Customer rodo1965 complained about receiving polishing pad without water holes despite product description advertising holes for wet polishing."
    
    # MCP request format
    mcp_request = {
        "jsonrpc": "2.0",
        "id": "test-123",
        "method": "tools/call",
        "params": {
            "name": "add_memories",
            "arguments": {
                "text": test_text,
                "user_id": "test_user"
            }
        }
    }
    
    print(f"Sending MCP request...")
    print(f"Text: {test_text[:100]}...")
    print()
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            # Send to the MCP server
            response = await client.post(
                "http://localhost:8050/mcp",
                json=mcp_request,
                headers={"Content-Type": "application/json"}
            )
            
            print(f"Response status: {response.status_code}")
            print(f"Response headers: {dict(response.headers)}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"MCP Response: {json.dumps(result, indent=2)}")
                
                # Check if we got the expected result format
                if "result" in result:
                    tool_result = json.loads(result["result"]["content"][0]["text"])
                    print(f"\nParsed tool result:")
                    print(f"Success: {tool_result.get('success')}")
                    print(f"Candidates processed: {tool_result.get('candidates_processed')}")
                    print(f"Operations: {tool_result.get('operations')}")
                    
                    if tool_result.get('candidates_processed', 0) > 0:
                        print("✅ SUCCESS: MCP interface is working!")
                        return True
                    else:
                        print("❌ ISSUE: Still getting 0 candidates processed")
                        return False
                else:
                    print("❌ Unexpected response format")
                    return False
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                print(f"Response: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Error testing MCP interface: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_mcp_add_memories())
    if success:
        print("\n🎉 MCP interface test passed!")
    else:
        print("\n💥 MCP interface test failed!")