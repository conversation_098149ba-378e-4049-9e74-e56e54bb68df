#!/usr/bin/env python3
"""
Comprehensive integration test for coroutine serialization fix.

This test validates that the performance monitor correctly handles various
coroutine patterns and edge cases after fixing the serialization issue.
"""

import asyncio
import json
import sys
import os
import time
from typing import Any, Dict

# Add src directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from performance_monitor import PerformanceMonitor


class TestCoroutineSerializationFix:
    """Comprehensive test suite for coroutine serialization fix."""
    
    def __init__(self):
        self.monitor = PerformanceMonitor()
        self.test_results = []
    
    async def test_direct_async_function(self):
        """Test direct async function handling."""
        print("Testing direct async function...")
        
        async def direct_async(value: str) -> Dict[str, Any]:
            await asyncio.sleep(0.001)
            return {
                "result": value,
                "type": "direct_async",
                "timestamp": time.time()
            }
        
        result = await self.monitor.track_operation(
            "direct_async_test", direct_async, "test_value"
        )
        
        # Validate result structure
        assert result["result"] == "test_value"
        assert result["type"] == "direct_async"
        assert "timestamp" in result
        
        # Test JSON serialization
        json_str = json.dumps(result)
        parsed = json.loads(json_str)
        assert parsed == result
        
        print("✅ Direct async function test passed")
        return True
    
    async def test_lambda_returning_coroutine(self):
        """Test lambda that returns a coroutine (the main fix)."""
        print("Testing lambda returning coroutine...")
        
        async def async_helper(value: str) -> Dict[str, Any]:
            await asyncio.sleep(0.001)
            return {
                "result": value,
                "type": "lambda_coroutine",
                "processed": True
            }
        
        # This is the problematic pattern that needed fixing
        lambda_func = lambda x: async_helper(x)
        
        result = await self.monitor.track_operation(
            "lambda_coroutine_test", lambda_func, "lambda_test"
        )
        
        # Validate result
        assert result["result"] == "lambda_test"
        assert result["type"] == "lambda_coroutine"
        assert result["processed"] is True
        
        # Test JSON serialization
        json_str = json.dumps(result)
        parsed = json.loads(json_str)
        assert parsed == result
        
        print("✅ Lambda returning coroutine test passed")
        return True
    
    async def test_function_returning_coroutine(self):
        """Test regular function that returns a coroutine."""
        print("Testing function returning coroutine...")
        
        async def async_operation(data: Dict[str, Any]) -> Dict[str, Any]:
            await asyncio.sleep(0.001)
            return {
                **data,
                "processed": True,
                "processing_time": 0.001
            }
        
        def function_returning_coro(input_data: Dict[str, Any]):
            """Regular function that returns a coroutine."""
            return async_operation(input_data)
        
        input_data = {"value": "function_test", "type": "function_coroutine"}
        
        result = await self.monitor.track_operation(
            "function_coroutine_test", function_returning_coro, input_data
        )
        
        # Validate result
        assert result["value"] == "function_test"
        assert result["type"] == "function_coroutine"
        assert result["processed"] is True
        assert "processing_time" in result
        
        # Test JSON serialization
        json_str = json.dumps(result)
        parsed = json.loads(json_str)
        assert parsed == result
        
        print("✅ Function returning coroutine test passed")
        return True
    
    async def test_nested_coroutine_calls(self):
        """Test nested coroutine calls."""
        print("Testing nested coroutine calls...")
        
        async def inner_async(value: str) -> str:
            await asyncio.sleep(0.001)
            return f"inner_{value}"
        
        async def outer_async(value: str) -> Dict[str, Any]:
            inner_result = await inner_async(value)
            await asyncio.sleep(0.001)
            return {
                "result": inner_result,
                "type": "nested_async",
                "levels": 2
            }
        
        def wrapper_function(value: str):
            """Function that returns a coroutine with nested async calls."""
            return outer_async(value)
        
        result = await self.monitor.track_operation(
            "nested_coroutine_test", wrapper_function, "nested_test"
        )
        
        # Validate result
        assert result["result"] == "inner_nested_test"
        assert result["type"] == "nested_async"
        assert result["levels"] == 2
        
        # Test JSON serialization
        json_str = json.dumps(result)
        parsed = json.loads(json_str)
        assert parsed == result
        
        print("✅ Nested coroutine calls test passed")
        return True
    
    async def test_error_handling_with_coroutines(self):
        """Test error handling with coroutines."""
        print("Testing error handling with coroutines...")
        
        async def failing_async(should_fail: bool) -> Dict[str, Any]:
            await asyncio.sleep(0.001)
            if should_fail:
                raise ValueError("Intentional test error")
            return {"result": "success", "type": "error_test"}
        
        def error_wrapper(should_fail: bool):
            return failing_async(should_fail)
        
        # Test successful case
        result = await self.monitor.track_operation(
            "error_test_success", error_wrapper, False
        )
        assert result["result"] == "success"
        assert result["type"] == "error_test"
        
        # Test error case
        try:
            await self.monitor.track_operation(
                "error_test_failure", error_wrapper, True
            )
            assert False, "Expected ValueError to be raised"
        except ValueError as e:
            assert str(e) == "Intentional test error"
        
        print("✅ Error handling with coroutines test passed")
        return True
    
    async def test_performance_metrics_collection(self):
        """Test that performance metrics are properly collected."""
        print("Testing performance metrics collection...")
        
        async def timed_operation(duration: float) -> Dict[str, Any]:
            await asyncio.sleep(duration)
            return {
                "result": "timed_operation",
                "duration": duration,
                "type": "performance_test"
            }
        
        def timed_wrapper(duration: float):
            return timed_operation(duration)
        
        # Run operation and check metrics
        start_time = time.time()
        result = await self.monitor.track_operation(
            "performance_test", timed_wrapper, 0.01  # 10ms
        )
        end_time = time.time()
        
        # Validate result
        assert result["result"] == "timed_operation"
        assert result["duration"] == 0.01
        
        # Check that metrics were recorded
        stats = self.monitor.get_performance_stats()
        # If there's no data, stats will have {"status": "no_data"}
        # If there is data, stats will have performance_metrics
        if "status" in stats:
            assert stats["status"] != "no_data", "Expected performance data to be collected"
        else:
            assert "performance_metrics" in stats, "Expected performance_metrics in stats"
            assert stats["performance_metrics"]["total_operations"] > 0
        
        # Check that latency was recorded (should be >= 10ms)
        recent_ops = self.monitor.get_recent_operations(limit=1)
        assert len(recent_ops) > 0
        last_op = recent_ops[-1]
        assert last_op["operation"] == "performance_test"
        assert last_op["success"] is True
        assert last_op["latency_ms"] >= 10  # Should be at least 10ms
        
        print("✅ Performance metrics collection test passed")
        return True
    
    async def run_all_tests(self):
        """Run all tests and return results."""
        tests = [
            ("Direct Async Function", self.test_direct_async_function),
            ("Lambda Returning Coroutine", self.test_lambda_returning_coroutine),
            ("Function Returning Coroutine", self.test_function_returning_coroutine),
            ("Nested Coroutine Calls", self.test_nested_coroutine_calls),
            ("Error Handling with Coroutines", self.test_error_handling_with_coroutines),
            ("Performance Metrics Collection", self.test_performance_metrics_collection),
        ]
        
        results = []
        for test_name, test_func in tests:
            try:
                success = await test_func()
                results.append((test_name, success))
            except Exception as e:
                print(f"❌ {test_name} failed with exception: {e}")
                import traceback
                traceback.print_exc()
                results.append((test_name, False))
        
        return results


async def main():
    """Main test runner."""
    print("🧪 Running comprehensive coroutine serialization fix tests...\n")
    
    test_suite = TestCoroutineSerializationFix()
    results = await test_suite.run_all_tests()
    
    # Summary
    print("\n" + "=" * 60)
    print("COMPREHENSIVE TEST SUMMARY:")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "PASSED" if result else "FAILED"
        emoji = "✅" if result else "❌"
        print(f"{emoji} {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nTotal: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All comprehensive tests passed!")
        print("\n✅ Coroutine serialization fix is working correctly!")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
