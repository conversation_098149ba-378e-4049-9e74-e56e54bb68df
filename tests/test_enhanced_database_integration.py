"""
Comprehensive test suite for enhanced database integration.

Tests the new EnhancedDatabaseService and MemoryDatabase integration
with connection pooling, vector indexes, and performance optimization.
"""

import asyncio
import pytest
import os
import numpy as np
from typing import List, Dict, Any
import logging

# Import modules to test
import sys
sys.path.append('/home/<USER>/dev/tools/mcp-mem0/src')

from enhanced_database_service import EnhancedDatabaseService
from memory_database import MemoryDatabase
from bge_embedding_client import BGEEmbeddingClient

# Setup logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestEnhancedDatabaseIntegration:
    """Test suite for enhanced database integration."""
    
    @pytest.fixture
    async def db_service(self):
        """Initialize enhanced database service for testing."""
        service = EnhancedDatabaseService(
            database_url=os.getenv('DATABASE_URL'),
            min_connections=2,
            max_connections=5,
            command_timeout=10
        )
        await service.initialize()
        yield service
        await service.close()
    
    @pytest.fixture
    async def memory_db(self):
        """Initialize memory database for testing."""
        db = MemoryDatabase(database_url=os.getenv('DATABASE_URL'))
        await db.initialize()
        yield db
        await db.close()
    
    @pytest.fixture
    def sample_embedding(self):
        """Generate sample BGE embedding (768 dimensions)."""
        return np.random.random(768).tolist()
    
    @pytest.fixture
    def sample_embeddings(self):
        """Generate multiple sample embeddings for batch testing."""
        return [np.random.random(768).tolist() for _ in range(5)]
    
    async def test_database_service_initialization(self, db_service):
        """Test enhanced database service initialization."""
        # Verify service is initialized
        assert db_service.pool is not None
        
        # Test health check
        health = await db_service.health_check()
        assert health['status'] == 'healthy'
        assert 'response_time_ms' in health
        
        # Test metrics
        metrics = await db_service.get_metrics()
        assert 'total_queries' in metrics
        assert 'pool_size' in metrics
        
        logger.info("✅ Database service initialization test passed")
    
    async def test_connection_pooling(self, db_service):
        """Test connection pooling functionality."""
        # Perform multiple concurrent operations
        async def test_query():
            return await db_service.execute_sql("SELECT 1 as test_value")
        
        # Run 10 concurrent queries to test pooling
        tasks = [test_query() for _ in range(10)]
        results = await asyncio.gather(*tasks)
        
        # Verify all queries succeeded
        assert len(results) == 10
        for result in results:
            assert len(result) == 1
            assert result[0]['test_value'] == 1
        
        # Verify metrics updated
        metrics = await db_service.get_metrics()
        assert metrics['total_queries'] >= 10
        
        logger.info("✅ Connection pooling test passed")
    
    async def test_vector_index_creation(self, db_service):
        """Test that vector indexes are created properly."""
        # Check if HNSW index exists
        index_check = await db_service.execute_sql("""
            SELECT EXISTS (
                SELECT 1 FROM pg_indexes 
                WHERE tablename = 'memories' AND indexname = 'memories_embedding_hnsw_idx'
            )
        """)
        
        assert len(index_check) == 1
        # Note: Index creation might be async, so we just verify the function doesn't error
        
        logger.info("✅ Vector index creation test passed")
    
    async def test_memory_storage_and_retrieval(self, memory_db, sample_embedding):
        """Test basic memory storage and retrieval."""
        user_id = "test_user_storage"
        content = "This is a test memory for storage validation"
        metadata = {"test": True, "category": "integration_test"}
        
        # Store memory
        memory_id = await memory_db.store_memory(
            user_id=user_id,
            content=content,
            embedding=sample_embedding,
            metadata=metadata
        )
        
        assert isinstance(memory_id, int)
        assert memory_id > 0
        
        # Retrieve all memories for user
        memories = await memory_db.get_all_memories(user_id, limit=10)
        assert len(memories) >= 1
        
        # Find our memory
        stored_memory = None
        for memory in memories:
            if memory['id'] == memory_id:
                stored_memory = memory
                break
        
        assert stored_memory is not None
        assert stored_memory['content'] == content
        assert stored_memory['metadata'] == metadata
        
        # Cleanup
        await memory_db.delete_memory(memory_id)
        
        logger.info("✅ Memory storage and retrieval test passed")
    
    async def test_vector_similarity_search(self, memory_db, sample_embeddings):
        """Test vector similarity search with HNSW index."""
        user_id = "test_user_similarity"
        test_memories = []
        
        try:
            # Store multiple memories with different embeddings
            for i, embedding in enumerate(sample_embeddings):
                content = f"Test memory {i} for similarity search"
                memory_id = await memory_db.store_memory(
                    user_id=user_id,
                    content=content,
                    embedding=embedding,
                    metadata={"index": i}
                )
                test_memories.append(memory_id)
            
            # Perform similarity search using first embedding
            query_embedding = sample_embeddings[0]
            similar_memories = await memory_db.similarity_search(
                embedding=query_embedding,
                user_id=user_id,
                threshold=1.0,  # High threshold to get all results
                limit=5
            )
            
            # Should find at least one memory (itself with distance 0)
            assert len(similar_memories) >= 1
            
            # Verify structure of results
            for memory in similar_memories:
                assert 'id' in memory
                assert 'content' in memory
                assert 'distance' in memory
                assert memory['distance'] >= 0
            
            # The first result should be the exact match with distance ~0
            closest_memory = similar_memories[0]
            assert closest_memory['distance'] < 0.01  # Should be very close to 0
            
        finally:
            # Cleanup test memories
            for memory_id in test_memories:
                try:
                    await memory_db.delete_memory(memory_id)
                except:
                    pass  # Ignore cleanup errors
        
        logger.info("✅ Vector similarity search test passed")
    
    async def test_conversation_summary_operations(self, memory_db):
        """Test conversation summary storage and retrieval."""
        user_id = "test_user_summary"
        summary = "This is a test conversation summary with important context"
        
        # Store summary
        await memory_db.store_conversation_summary(user_id, summary)
        
        # Retrieve summary
        retrieved_summary = await memory_db.get_conversation_summary(user_id)
        assert retrieved_summary == summary
        
        # Update summary
        updated_summary = "Updated conversation summary with new context"
        await memory_db.store_conversation_summary(user_id, updated_summary)
        
        # Verify update
        final_summary = await memory_db.get_conversation_summary(user_id)
        assert final_summary == updated_summary
        
        logger.info("✅ Conversation summary operations test passed")
    
    async def test_recent_memories_retrieval(self, memory_db, sample_embeddings):
        """Test recent memories retrieval functionality."""
        user_id = "test_user_recent"
        test_memories = []
        
        try:
            # Store multiple memories
            for i, embedding in enumerate(sample_embeddings):
                content = f"Recent memory {i} - timestamp order test"
                memory_id = await memory_db.store_memory(
                    user_id=user_id,
                    content=content,
                    embedding=embedding
                )
                test_memories.append(memory_id)
                # Small delay to ensure different timestamps
                await asyncio.sleep(0.01)
            
            # Get recent memory contents
            recent_contents = await memory_db.get_recent_memories(user_id, limit=3)
            
            # Should get up to 3 recent memories
            assert len(recent_contents) <= 3
            assert len(recent_contents) >= 1
            
            # Verify content format
            for content in recent_contents:
                assert isinstance(content, str)
                assert "Recent memory" in content
            
        finally:
            # Cleanup
            for memory_id in test_memories:
                try:
                    await memory_db.delete_memory(memory_id)
                except:
                    pass
        
        logger.info("✅ Recent memories retrieval test passed")
    
    async def test_bulk_operations_performance(self, memory_db, sample_embeddings):
        """Test performance of bulk operations."""
        user_id = "test_user_bulk"
        test_memories = []
        
        try:
            # Measure bulk insert performance
            import time
            start_time = time.time()
            
            # Store multiple memories
            for i, embedding in enumerate(sample_embeddings):
                content = f"Bulk operation test memory {i}"
                memory_id = await memory_db.store_memory(
                    user_id=user_id,
                    content=content,
                    embedding=embedding
                )
                test_memories.append(memory_id)
            
            insert_time = time.time() - start_time
            
            # Measure bulk retrieval performance
            start_time = time.time()
            all_memories = await memory_db.get_all_memories(user_id, limit=50)
            retrieval_time = time.time() - start_time
            
            # Measure similarity search performance
            start_time = time.time()
            similar_memories = await memory_db.similarity_search(
                embedding=sample_embeddings[0],
                user_id=user_id,
                threshold=1.0,
                limit=10
            )
            search_time = time.time() - start_time
            
            # Performance assertions (reasonable thresholds)
            assert insert_time < 5.0  # 5 seconds for 5 inserts
            assert retrieval_time < 1.0  # 1 second for retrieval
            assert search_time < 2.0  # 2 seconds for similarity search
            
            # Verify data integrity
            assert len(all_memories) >= len(sample_embeddings)
            assert len(similar_memories) >= 1
            
            logger.info(f"✅ Bulk operations performance test passed")
            logger.info(f"   Insert time: {insert_time:.3f}s")
            logger.info(f"   Retrieval time: {retrieval_time:.3f}s")
            logger.info(f"   Search time: {search_time:.3f}s")
            
        finally:
            # Cleanup
            await memory_db.delete_all_memories(user_id)
    
    async def test_error_handling_and_recovery(self, memory_db):
        """Test error handling and recovery mechanisms."""
        # Test invalid embedding dimensions
        try:
            await memory_db.store_memory(
                user_id="test_error",
                content="Invalid embedding test",
                embedding=[1, 2, 3]  # Wrong dimensions
            )
            assert False, "Should have raised an error for invalid embedding"
        except Exception as e:
            logger.info(f"✅ Correctly caught invalid embedding error: {e}")
        
        # Test invalid user ID
        try:
            memories = await memory_db.get_all_memories("")  # Empty user ID
            # This might succeed with empty results, which is fine
            assert isinstance(memories, list)
        except Exception as e:
            logger.info(f"✅ Handled empty user ID: {e}")
        
        logger.info("✅ Error handling and recovery test passed")

# Async test runner for pytest
@pytest.mark.asyncio
async def test_enhanced_database_integration():
    """Run all database integration tests."""
    test_suite = TestEnhancedDatabaseIntegration()
    
    # Check if DATABASE_URL is available
    if not os.getenv('DATABASE_URL'):
        logger.warning("DATABASE_URL not set - skipping database integration tests")
        return
    
    logger.info("Starting enhanced database integration tests...")
    
    # Initialize fixtures
    db_service = EnhancedDatabaseService(
        database_url=os.getenv('DATABASE_URL'),
        min_connections=2,
        max_connections=5
    )
    
    try:
        await db_service.initialize()
        
        memory_db = MemoryDatabase(database_url=os.getenv('DATABASE_URL'))
        await memory_db.initialize()
        
        sample_embedding = np.random.random(768).tolist()
        sample_embeddings = [np.random.random(768).tolist() for _ in range(5)]
        
        # Run tests
        await test_suite.test_database_service_initialization(db_service)
        await test_suite.test_connection_pooling(db_service)
        await test_suite.test_vector_index_creation(db_service)
        await test_suite.test_memory_storage_and_retrieval(memory_db, sample_embedding)
        await test_suite.test_vector_similarity_search(memory_db, sample_embeddings)
        await test_suite.test_conversation_summary_operations(memory_db)
        await test_suite.test_recent_memories_retrieval(memory_db, sample_embeddings)
        await test_suite.test_bulk_operations_performance(memory_db, sample_embeddings)
        await test_suite.test_error_handling_and_recovery(memory_db)
        
        logger.info("🎉 All enhanced database integration tests passed!")
        
    finally:
        # Cleanup
        await db_service.close()
        await memory_db.close()

if __name__ == "__main__":
    # Run tests directly
    asyncio.run(test_enhanced_database_integration())