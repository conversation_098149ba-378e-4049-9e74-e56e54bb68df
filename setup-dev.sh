#!/bin/bash
# Development environment setup script

echo "🚀 Setting up Spark Memory MCP Development Environment..."

# Check BGE server connectivity
echo "🔍 Checking BGE embedding server..."
BGE_SERVER=${BGE_SERVER_URL:-http://************:8080}
if curl -s -f "$BGE_SERVER/health" > /dev/null; then
    echo "✅ BGE server is accessible at $BGE_SERVER"
else
    echo "❌ WARNING: Cannot reach BGE server at $BGE_SERVER"
    echo "   Please ensure BGE server is running on Ubuntu host"
    echo "   You can update BGE_SERVER_URL in .env file"
fi

# Check if .env exists
if [ ! -f .env ]; then
    echo "📝 Creating .env file from .env.example..."
    cp .env.example .env
    echo "⚠️  Please update .env with your API keys!"
    echo "   Especially set BGE_SERVER_URL=http://************:8080"
fi

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p logs coverage tests/unit tests/integration tests/e2e
mkdir -p tests/phase_{1..4}

# Stop any running containers
echo "🛑 Stopping existing containers..."
docker-compose -f docker-compose.dev.yml down

# Build development images
echo "🔨 Building development images..."
docker-compose -f docker-compose.dev.yml build

# Start core services
echo "🚀 Starting development services..."
docker-compose -f docker-compose.dev.yml up -d postgres-dev

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
for i in {1..30}; do
    if docker-compose -f docker-compose.dev.yml exec postgres-dev pg_isready -U postgres > /dev/null 2>&1; then
        echo "✅ PostgreSQL is ready!"
        break
    fi
    echo -n "."
    sleep 1
done

# Run database migrations
echo "📊 Setting up database..."
docker-compose -f docker-compose.dev.yml exec postgres-dev psql -U postgres -d spark_memory -c "CREATE EXTENSION IF NOT EXISTS vector;"

# Check if init_db.sql exists and run it
if [ -f scripts/init_db.sql ]; then
    echo "📊 Running database initialization script..."
    docker-compose -f docker-compose.dev.yml exec -T postgres-dev psql -U postgres -d spark_memory < scripts/init_db.sql
fi

echo ""
echo "✅ Development environment ready!"
echo ""
echo "🔧 Configuration:"
echo "  BGE Server: $BGE_SERVER"
echo "  Database: PostgreSQL on port 5433"
echo "  pgAdmin: http://localhost:5050 (<EMAIL>/admin)"
echo "  MCP Server: http://localhost:8050"
echo ""
echo "📋 Available commands:"
echo "  ./dev.sh start    - Start development server with hot reload"
echo "  ./dev.sh test     - Run test suite"
echo "  ./dev.sh logs     - View logs"
echo "  ./dev.sh shell    - Enter development shell"
echo "  ./dev.sh stop     - Stop all services"
echo "  ./dev.sh clean    - Clean up everything"
echo ""
echo "🚀 To start development:"
echo "  ./dev.sh start"
