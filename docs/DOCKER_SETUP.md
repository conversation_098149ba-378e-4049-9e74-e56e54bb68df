# Docker Deployment Guide for Spark Memory MCP Server

## Quick Start

### 1. Basic Deployment (Recommended)
```bash
# Copy Docker environment configuration
cp .env.docker .env

# Start Spark MCP Server (uses external Supabase at *************:8000)
docker-compose up -d

# Check status
docker-compose ps
curl http://localhost:8050/health
```

### 2. Full Stack Deployment
```bash
# Start all services including optional components
docker-compose --profile supabase-local --profile mock-bge --profile redis --profile monitoring up -d
```

## Environment Configuration

### Required Environment Variables
The `.env.docker` file contains the correct configuration for Docker deployment:

```bash
# MCP Server Configuration
TRANSPORT=sse
HOST=0.0.0.0
PORT=8050

# LLM Configuration
LLM_PROVIDER=mock                    # Use 'mock' for testing, 'openai' for production
LLM_API_KEY=                         # Add your API key for production
LLM_CHOICE=gpt-3.5-turbo

# BGE Embedding Server
BGE_SERVER_URL=http://host.docker.internal:8080  # External BGE server on host

# Database Connection (UNUSED - system uses Supabase MCP at *************:8000)
DATABASE_URL=""
```

### ⚠️ Important: EMBEDDING_MODEL_CHOICE is NOT Used
The new two-phase architecture ignores `EMBEDDING_MODEL_CHOICE` and uses BGE embeddings exclusively via `BGE_SERVER_URL`.

## Service Architecture

### Core Services (Always Running)
- **spark-mcp**: Main MCP server with two-phase memory architecture
- **External Supabase**: Uses external Supabase at *************:8000 via MCP tools (not containerized)

### Optional Services (Use Profiles)
- **postgres-local**: Local PostgreSQL for testing without external Supabase (profile: `local-postgres`)
- **bge-server**: Mock BGE server for testing (profile: `mock-bge`)
- **redis**: Caching layer (profile: `redis`)
- **prometheus**: Monitoring (profile: `monitoring`)

## Deployment Options

### Option 1: Production with External Services
```bash
# Use external BGE server and Supabase
# Edit .env to point to your external services
BGE_SERVER_URL=http://your-bge-server:8080
DATABASE_URL=*****************************************/db

# Deploy
docker-compose up -d
```

### Option 2: Local Development with Mock Services
```bash
# Use mock BGE server for testing
docker-compose --profile mock-bge up -d
```

### Option 3: Local Supabase Alternative
```bash
# Use local Supabase instead of external
docker-compose --profile supabase-local up -d

# Update .env to use local Supabase
DATABASE_URL=**************************************************/postgres
```

## Network Configuration

### Docker Networking
The services use a custom bridge network `spark-network` for secure internal communication.

### External Service Access
- `host.docker.internal` - Access services running on the Docker host
- Port mappings allow external access to key services

### Health Checks
All services include comprehensive health checks:
```bash
# Check all service health
docker-compose ps

# View service logs
docker-compose logs -f spark-mcp
docker-compose logs -f postgres
```

## Database Setup

### Automatic Initialization
PostgreSQL containers automatically run initialization scripts:
- `scripts/init_db.sql` - Main database schema with pgvector
- `scripts/supabase_init.sql` - Supabase-specific setup

### Manual Database Verification
```sql
-- Connect to PostgreSQL
docker-compose exec postgres psql -U postgres -d spark_memory

-- Verify tables
\dt

-- Check pgvector extension
SELECT * FROM pg_extension WHERE extname = 'vector';

-- View sample data
SELECT user_id, content, created_at FROM memories LIMIT 5;
```

## Scaling and Performance

### Horizontal Scaling
```bash
# Scale the MCP server
docker-compose up -d --scale spark-mcp=3

# Load balancer needed for multiple instances
```

### Resource Limits
Add resource limits to docker-compose.yml:
```yaml
deploy:
  resources:
    limits:
      memory: 1G
      cpus: '0.5'
```

### Volume Management
Persistent data storage:
- `spark_postgres_data` - PostgreSQL data
- `spark_supabase_data` - Supabase data
- `spark_redis_data` - Redis cache
- `spark_prometheus_data` - Monitoring metrics

## Monitoring and Debugging

### Service Logs
```bash
# Follow all logs
docker-compose logs -f

# Specific service logs
docker-compose logs -f spark-mcp
docker-compose logs -f postgres

# Log rotation
docker-compose logs --tail=100 spark-mcp
```

### Health Monitoring
```bash
# Check service health
curl http://localhost:8050/health

# Database connection test
curl -X POST http://localhost:8050/add_memories \
  -H "Content-Type: application/json" \
  -d '{"text": "Test memory", "user_id": "test"}'
```

### Performance Monitoring (with Prometheus profile)
```bash
# Start with monitoring
docker-compose --profile monitoring up -d

# Access Prometheus UI
open http://localhost:9090
```

## Troubleshooting

### Common Issues

**BGE Server Connection Failed**
```bash
# Check if BGE server is accessible from container
docker-compose exec spark-mcp curl http://host.docker.internal:8080/health

# Alternative: Use mock BGE server
docker-compose --profile mock-bge up -d
```

**Database Connection Error**
```bash
# Check PostgreSQL status
docker-compose exec postgres pg_isready -U postgres

# Verify database exists
docker-compose exec postgres psql -U postgres -l
```

**Permission Errors**
```bash
# Fix volume permissions
sudo chown -R $USER:$USER ./logs
sudo chown -R 999:999 ./postgres_data  # postgres user in container
```

### Reset Everything
```bash
# Stop all services
docker-compose down

# Remove volumes (WARNING: destroys data)
docker-compose down -v

# Remove images
docker-compose down --rmi all

# Clean rebuild
docker-compose build --no-cache
docker-compose up -d
```

## Security Considerations

### Production Deployment
1. **Change default passwords** in docker-compose.yml
2. **Set proper API keys** in .env file
3. **Use TLS/SSL** for external connections
4. **Restrict network access** with firewall rules
5. **Enable authentication** for dashboard access

### Environment Variables Security
```bash
# Use Docker secrets for sensitive data
echo "your_api_key" | docker secret create openai_api_key -

# Reference in compose file
secrets:
  - openai_api_key
```

## Backup and Recovery

### Database Backup
```bash
# Create backup
docker-compose exec postgres pg_dump -U postgres spark_memory > backup.sql

# Restore backup
docker-compose exec -T postgres psql -U postgres spark_memory < backup.sql
```

### Volume Backup
```bash
# Backup persistent volumes
docker run --rm -v spark_postgres_data:/data -v $(pwd):/backup alpine tar czf /backup/postgres_backup.tar.gz /data
```

This comprehensive Docker setup provides a production-ready deployment of the Spark Memory MCP Server with proper networking, health checks, and scaling capabilities.