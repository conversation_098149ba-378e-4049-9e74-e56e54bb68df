# Development Workflow & Testing Procedures

## Overview

This document outlines the complete development workflow for the Spark Memory MCP Server, including setup procedures, testing methodologies, and deployment practices.

## Development Environment Setup

### Prerequisites
- **Docker & Docker Compose**: Latest versions
- **Git**: For version control
- **Access**: Ubuntu server at ************
- **BGE Server**: Running on port 8080
- **Terminal**: Git Bash, WSL, or native Linux terminal

### Quick Setup
```bash
# 1. <PERSON><PERSON> and navigate
git clone <repository-url>
cd mcp-mem0

# 2. Make scripts executable
chmod +x setup-dev.sh dev.sh

# 3. Create environment configuration
cp .env.example .env
# Edit .env with your specific settings

# 4. Run setup
./setup-dev.sh

# 5. Start development server
./dev.sh start
```

### Environment Configuration

Critical environment variables:
```bash
# Core Settings
TRANSPORT=sse
HOST=0.0.0.0
PORT=8050

# BGE Embedding Server (IMPORTANT: Use actual IP)
BGE_SERVER_URL=http://************:8080

# Database (External Supabase instance)
DATABASE_URL=**************************************************************************/postgres

# LLM Provider
LLM_PROVIDER=openrouter
LLM_API_KEY=your-api-key-here
LLM_CHOICE=google/gemini-2.5-flash-lite

# User Context
MCP_USER_ID=dev-team
```

## Development Helper Script (./dev.sh)

### Core Commands
```bash
# Server Management
./dev.sh start          # Start development server with hot reload
./dev.sh start-d        # Start detached (background)
./dev.sh stop           # Stop all services
./dev.sh restart        # Restart services
./dev.sh logs           # View logs (follow mode)

# Development Tools
./dev.sh shell          # Interactive shell inside container
./dev.sh db-shell       # PostgreSQL shell access
./dev.sh format         # Format Python code
./dev.sh lint           # Lint Python code

# Testing
./dev.sh test           # Run all tests
./dev.sh test-unit      # Run unit tests only
./dev.sh test-integration # Run integration tests only
./dev.sh test-phase 1   # Run phase-specific tests (1-4)

# Utilities
./dev.sh clean          # Clean up Docker resources
./dev.sh build          # Rebuild development image
```

### Testing Workflow

#### Phase-Based Testing Strategy
The project uses a 4-phase testing approach:

**Phase 1: Critical Fixes**
```bash
./dev.sh test-phase 1
```
- ✅ Semantic search functionality
- ✅ P95 latency < 1 second
- ✅ Basic conflict resolution
- ✅ BGE server connectivity

**Phase 2: Memory Intelligence**
```bash
./dev.sh test-phase 2
```
- 🔄 Memory evolution and updates
- 🔄 Memory graph relationships
- 🔄 Enhanced context understanding
- 🔄 Intelligent conflict resolution

**Phase 3: User Experience**
```bash
./dev.sh test-phase 3
```
- 🔄 Advanced search capabilities
- 🔄 Memory management interface
- 🔄 Performance dashboard
- 🔄 User workflow optimization

**Phase 4: Polish & Reliability**
```bash
./dev.sh test-phase 4
```
- 🔄 Comprehensive error handling
- 🔄 Performance optimization
- 🔄 Production readiness
- 🔄 Monitoring and alerting

#### Test Types

**Unit Tests**
```bash
./dev.sh test-unit

# Run specific unit test
./dev.sh shell
pytest tests/unit/test_memory_extraction.py -v
```

**Integration Tests**
```bash
./dev.sh test-integration

# Test specific integration
./dev.sh shell
pytest tests/integration/test_end_to_end.py -v
```

**Performance Tests**
```bash
# Run performance benchmarks
./dev.sh shell
python tests/test_mcp_performance.py

# Cache performance testing
python tests/test_cache_performance.py
```

## Development Workflow

### 1. Feature Development Cycle

#### Planning Phase
1. **Identify requirements** from CLAUDE.md or user needs
2. **Search existing memory** for similar implementations
3. **Plan implementation** using TodoWrite tool
4. **Update documentation** if needed

#### Implementation Phase
1. **Create feature branch**
   ```bash
   git checkout -b feature/memory-graph-relationships
   ```

2. **Edit source files** in `src/` directory
   - Changes auto-reload without container rebuild
   - Monitor logs: `./dev.sh logs`

3. **Test incrementally**
   ```bash
   # Test specific functionality
   ./dev.sh test-unit
   
   # Test integration
   ./dev.sh test-integration
   ```

4. **Validate performance**
   ```bash
   # Check performance impact
   curl http://localhost:8050/tools/get_performance_stats
   ```

#### Quality Assurance
1. **Run full test suite**
   ```bash
   ./dev.sh test
   ```

2. **Performance validation**
   ```bash
   ./dev.sh test-phase 1  # Ensure no regressions
   ```

3. **Code quality**
   ```bash
   ./dev.sh format
   ./dev.sh lint
   ```

4. **Documentation update**
   - Update API_REFERENCE.md if tools changed
   - Update README.md if architecture changed
   - Update CLAUDE.md if development process changed

#### Deployment
1. **Commit changes**
   ```bash
   git add .
   git commit -m "✨ Add memory graph relationships

   - Implement bidirectional memory links
   - Add graph traversal for related memories
   - Update search to include related context
   - Add performance monitoring for graph operations

   🤖 Generated with [Claude Code](https://claude.ai/code)

   Co-Authored-By: Claude <<EMAIL>>"
   ```

2. **Test production build**
   ```bash
   docker-compose -f docker-compose.yml up --build
   ```

3. **Merge and deploy**
   ```bash
   git checkout main
   git merge feature/memory-graph-relationships
   git push origin main
   ```

### 2. Debugging Workflow

#### Log Analysis
```bash
# Real-time logs
./dev.sh logs

# Specific service logs
docker-compose -f docker-compose.dev.yml logs spark-mcp-dev

# Database logs
./dev.sh db-shell
# Check for connection issues or slow queries
```

#### Interactive Debugging
```bash
# Access container shell
./dev.sh shell

# Test components individually
python -c "from bge_embedding_client import BGEEmbeddingClient; import asyncio; asyncio.run(BGEEmbeddingClient().health_check())"

# Test database connectivity
python -c "from supabase_mcp_integration import RealSupabaseMCP; import asyncio; print(asyncio.run(RealSupabaseMCP().test_connection()))"
```

#### Performance Debugging
```bash
# Check BGE server
curl http://************:8080/health

# Check MCP server health
curl http://localhost:8050/tools/health_check

# Performance analysis
curl http://localhost:8050/tools/get_performance_stats | jq
```

### 3. Database Management

#### Development Database
```bash
# Access PostgreSQL shell
./dev.sh db-shell

# Common queries
SELECT COUNT(*) FROM memories;
SELECT * FROM memories WHERE user_id = 'test' LIMIT 10;
\d memories  # Show table structure

# Reset development database
docker-compose -f docker-compose.dev.yml down -v
./setup-dev.sh
```

#### Database Migrations
```bash
# Run database migration
./dev.sh shell
python scripts/migrate_database.py

# Setup enhanced database features
./scripts/setup_enhanced_db.sh
```

#### pgAdmin Access
- **URL**: http://localhost:5050
- **Login**: <EMAIL> / admin
- **Server**: postgres-dev (host: postgres-dev, port: 5432)

## Testing Best Practices

### Test Data Management
```bash
# Use consistent test user IDs
MCP_USER_ID="test-user"

# Clean test data between runs
curl -X POST http://localhost:8050/tools/delete_all_memories \
  -H "Content-Type: application/json" \
  -d '{"user_id": "test-user"}'
```

### Performance Testing
```bash
# Baseline performance measurement
python tests/test_mcp_performance.py

# Cache performance analysis
python tests/test_cache_performance.py

# Load testing
for i in {1..10}; do
  curl -X POST http://localhost:8050/tools/add_memories \
    -H "Content-Type: application/json" \
    -d "{\"text\": \"Test memory $i\", \"user_id\": \"load-test\"}" &
done
wait
```

### Integration Testing
```bash
# End-to-end pipeline test
python tests/test_two_phase_pipeline.py

# BGE integration test
python tests/test_enhanced_database_integration.py

# Production simulation
python tests/test_production_mcp.py
```

## Production Deployment

### Pre-deployment Checklist
- [ ] All phase tests passing
- [ ] Performance targets met (P95 < 1s)
- [ ] BGE server accessible
- [ ] Database connectivity verified
- [ ] Environment variables configured
- [ ] Documentation updated

### Deployment Process
```bash
# 1. Build production image
docker build -t spark-mcp:latest .

# 2. Test production configuration
docker run --env-file .env.production -p 8050:8050 spark-mcp:latest

# 3. Deploy with docker-compose
docker-compose -f docker-compose.yml up -d

# 4. Verify deployment
curl http://your-server:8050/tools/health_check
```

### Post-deployment Validation
```bash
# Health check
curl http://your-server:8050/tools/health_check

# Performance check
curl http://your-server:8050/tools/get_performance_stats

# BGE connectivity
curl http://your-server:8050/tools/get_bge_health_status

# End-to-end test
curl -X POST http://your-server:8050/tools/add_memories \
  -H "Content-Type: application/json" \
  -d '{"text": "Deployment test memory", "user_id": "deploy-test"}'
```

## Troubleshooting Common Issues

### BGE Server Connection Issues
```bash
# Check BGE server directly
curl http://************:8080/health

# Verify environment variable
echo $BGE_SERVER_URL

# Test from container
./dev.sh shell
curl $BGE_SERVER_URL/health
```

### Database Connection Issues
```bash
# Test database connectivity
./dev.sh db-shell

# Check environment variables
echo $DATABASE_URL

# Verify Supabase MCP access
./dev.sh shell
python -c "from supabase_mcp_integration import RealSupabaseMCP; import asyncio; print(asyncio.run(RealSupabaseMCP().test_connection()))"
```

### Performance Issues
```bash
# Check system resources
docker stats

# Analyze performance metrics
curl http://localhost:8050/tools/get_performance_stats | jq '.performance_stats'

# Cache analysis
curl http://localhost:8050/tools/get_cache_performance_report | jq
```

### Hot Reload Not Working
```bash
# Check volume mounts
docker-compose -f docker-compose.dev.yml config

# Restart development server
./dev.sh restart

# Check file permissions
ls -la src/
```

## Code Quality Standards

### Pre-commit Checklist
```bash
# Format code
./dev.sh format

# Lint code
./dev.sh lint

# Run tests
./dev.sh test

# Check performance
./dev.sh test-phase 1
```

### Python Style Guidelines
- Use type hints for all functions
- Add docstrings for all public functions
- Follow PEP 8 style guide
- Keep functions under 50 lines
- Use meaningful variable names
- Handle exceptions gracefully

### Git Commit Standards
```bash
# Commit message format
git commit -m "✨ Add feature description

- Detailed change 1
- Detailed change 2
- Performance impact or testing notes

🤖 Generated with [Claude Code](https://claude.ai/code)

Co-Authored-By: Claude <<EMAIL>>"
```

### Documentation Standards
- Update API_REFERENCE.md for tool changes
- Update README.md for architecture changes
- Add inline comments for complex logic
- Include usage examples
- Document environment variables

## Performance Monitoring

### Key Metrics to Monitor
```bash
# Pipeline performance (target: P95 < 1440ms)
curl http://localhost:8050/tools/get_performance_stats | jq '.performance_stats.pipeline_performance'

# BGE performance (target: < 100ms)
curl http://localhost:8050/tools/get_bge_health_status | jq '.bge_status.performance_metrics'

# Cache performance (target: > 80% hit rate)
curl http://localhost:8050/tools/get_cache_performance_report | jq '.cache_performance'
```

### Performance Optimization
```bash
# Warm cache for better performance
curl http://localhost:8050/tools/cache_warm_common_patterns

# Benchmark BGE performance
curl http://localhost:8050/tools/benchmark_bge_performance

# Clear cache for testing
curl http://localhost:8050/tools/invalidate_llm_cache
```

## Team Collaboration

### Knowledge Sharing
- Use memory system to capture development insights
- Document architecture decisions in memories
- Share troubleshooting solutions through memory system
- Update CLAUDE.md with new patterns and practices

### Code Review Process
1. **Self-review** using checklist above
2. **Performance validation** with phase tests
3. **Documentation update** as needed
4. **Team notification** of significant changes

### Environment Consistency
- Use Docker for consistent development environment
- Keep .env.example updated with latest variables
- Document any system-specific configurations
- Test on multiple development machines

---

**Last Updated**: August 2024  
**Development Environment**: Docker-based with hot reload  
**Testing Strategy**: 4-phase progressive testing  
**Deployment**: Docker Compose with production configuration