#!/usr/bin/env python3
"""Fix health endpoint in main_new.py"""

import re

def fix_health_endpoint():
    with open('src/main_new.py', 'r') as f:
        content = f.read()
    
    # Find the FastMCP initialization
    pattern = r'(mcp = FastMCP\(\n.*?\n\))\n\n# OpenMemory MCP Interface Implementation'
    
    replacement = r'''\1

# Add custom HTTP health endpoint for Docker health checks
@mcp.get("/health")
async def http_health_endpoint():
    """Simple HTTP health endpoint for Docker health checks."""
    try:
        readiness_status = readiness_manager.get_status()
        if readiness_status["ready"]:
            return {"status": "healthy", "ready": True}
        else:
            return {"status": "initializing", "ready": False}
    except Exception as e:
        return {"status": "error", "ready": False, "error": str(e)}

# OpenMemory MCP Interface Implementation'''
    
    new_content = re.sub(pattern, replacement, content, flags=re.DOTALL)
    
    with open('src/main_new.py', 'w') as f:
        f.write(new_content)
    
    print("Health endpoint added successfully!")

if __name__ == "__main__":
    fix_health_endpoint()
