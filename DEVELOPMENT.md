# Spark Memory MCP Development Guide

## Quick Start

1. **Initial Setup**
   ```bash
   chmod +x setup-dev.sh dev.sh
   ./setup-dev.sh
   ```

2. **Start Development Server**
   ```bash
   ./dev.sh start
   ```
   The server will start with hot reload enabled. Any changes to files in `src/` will automatically reload.

3. **Run Tests**
   ```bash
   # Run all tests
   ./dev.sh test

   # Run specific phase tests
   ./dev.sh test-phase 1
   ```

## Development Workflow

### Hot Reload
- Source code is mounted as a volume
- Changes to Python files trigger automatic reload
- No need to rebuild containers for code changes

### Database Access
```bash
# Access PostgreSQL shell
./dev.sh db-shell

# View database with pgAdmin
# Open http://localhost:5050
# Login: <EMAIL> / admin
```

### Logs
```bash
# View all logs
./dev.sh logs

# View specific service logs
./dev.sh logs spark-mcp-dev
```

### Testing
```bash
# Unit tests only
./dev.sh test-unit

# Integration tests
./dev.sh test-integration

# Phase-specific tests
./dev.sh test-phase 1  # Phase 1 tests
./dev.sh test-phase 2  # Phase 2 tests
```

## Environment Variables

Create `.env` file with:
```env
# LLM Configuration
LLM_PROVIDER=openrouter
LLM_API_KEY=your-api-key-here
LLM_CHOICE=google/gemini-2.5-flash-lite
LLM_BASE_URL=https://openrouter.ai/api/v1

# BGE Server (use real server or mock)
BGE_SERVER_URL=http://host.docker.internal:8080

# Database (development uses local PostgreSQL)
DATABASE_URL=******************************************************/spark_memory
```

## Phase Testing

Each phase has its own test directory:
- `tests/phase_1/` - Critical fixes
- `tests/phase_2/` - Memory intelligence  
- `tests/phase_3/` - User experience
- `tests/phase_4/` - Polish & reliability

Run phase tests after implementation:
```bash
./dev.sh test-phase 1
```

## Debugging

1. **Enter Development Shell**
   ```bash
   ./dev.sh shell
   ```

2. **Use IPython for Interactive Testing**
   ```python
   import asyncio
   from src.memory_database import MemoryDatabase
   
   db = MemoryDatabase("postgresql://...")
   asyncio.run(db.initialize())
   ```

3. **Check Performance Stats**
   ```bash
   curl http://localhost:8050/tools/get_performance_stats
   ```

## Code Quality

```bash
# Format code
./dev.sh format

# Lint code
./dev.sh lint
```

## Troubleshooting

1. **Port Conflicts**
   - Dev PostgreSQL: 5433 (not 5432)
   - Test PostgreSQL: 5434
   - pgAdmin: 5050
   - MCP Server: 8050

2. **Hot Reload Not Working**
   - Check volume mounts in docker-compose.dev.yml
   - Ensure file permissions are correct
   - Try restart: `./dev.sh restart`

3. **Database Connection Issues**
   - Wait for PostgreSQL to be ready
   - Check DATABASE_URL in .env
   - Verify pgvector extension is installed
