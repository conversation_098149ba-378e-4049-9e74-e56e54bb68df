# Memory Intelligence Enhancement - Project Requirements Plan (PRP)

## Context

### Current System Architecture
The Spark Memory MCP Server currently implements a two-phase memory pipeline based on Mem0 research:
- **Phase 1**: Memory extraction from conversation pairs using LLM
- **Phase 2**: Update decisions (ADD/UPDATE/DELETE/NOOP) based on similarity search
- **Storage**: PostgreSQL with pgvector for semantic search
- **Embeddings**: BGE server for vector generation
- **Interface**: MCP (Model Context Protocol) for AI integration

### Existing Infrastructure
- **Neo4j**: Already running locally at `bolt://localhost:7687`
- **RAG Knowledge Base**: Available via spark-rag MCP server with Mem0 documentation
- **Docker Environment**: All services containerized and accessible

### Key Files to Reference
- `src/memory_extraction.py`: Current extraction implementation
- `src/memory_update.py`: Update phase with operation decisions
- `src/memory_database.py`: Database operations with pgvector
- `src/config.py`: Configuration constants and thresholds
- `src/enhanced_database_service.py`: Connection pooling and optimization

## Objective
Enhance the memory system to leverage Mem0's built-in capabilities for automatic memory extraction, updates, relationship tracking, and self-evolution. Focus on maximizing the native features rather than recreating them.

## Mem0 Documentation & Resources

### Official Documentation
- **Overview**: https://docs.mem0.ai/open-source/graph_memory/overview
- **Features**: https://docs.mem0.ai/open-source/graph_memory/features
- **Python SDK**: https://docs.mem0.ai/open-source/python-quickstart
- **Custom Prompts**: https://docs.mem0.ai/open-source/features/custom-fact-extraction-prompt
- **Research Paper**: https://arxiv.org/html/2504.19413v1
- **Local RAG Access**: Available via `mcp__spark-rag__perform_rag_query` with source="docs.mem0.ai"

### Key Mem0 Capabilities
1. **Automatic Memory Inference**: `infer=True` for automatic extraction and updates
2. **Memory History**: Built-in versioning with `m.history(memory_id)`
3. **Graph Memory**: Neo4j integration for entity relationships
4. **Custom Prompts**: Control extraction and update behavior
5. **Conflict Resolution**: Built-in semantic similarity analysis

## Implementation Blueprint

### Phase 1: Custom Extraction Prompts (Week 1)

#### 1.1 Enhance Memory Extraction Module
**File**: `src/memory_extraction.py`

```python
# Add to _build_extraction_prompt() method
custom_fact_extraction_prompt = """
Extract key facts from the conversation, focusing on:
1. User preferences that may change over time
2. Current projects and their status
3. Relationships between people, projects, and concepts
4. Temporal context (when things happened or will happen)
5. Updates or corrections to previous information

Format extractions to show evolution:
- "User now prefers X (previously preferred Y)"
- "Project status changed from A to B"
- "New relationship discovered: X relates to Y"

CRITICAL: Extract only factual, actionable information valuable for future conversations.

INPUT:
Previous Message: {previous_msg}
Current Message: {current_msg}
Conversation Summary: {conversation_summary}
Recent Context: {recent_context}

Respond with JSON array of extracted memories:
"""
```

#### 1.2 Add Metadata Enhancement
**File**: `src/memory_handlers.py` (modify add_memories handler)

```python
async def add_memories_with_metadata(self, text: str, user_id: str, metadata: dict):
    """Enhanced memory addition with rich metadata"""
    metadata = {
        "conversation_id": metadata.get("conversation_id", str(uuid.uuid4())),
        "timestamp": datetime.utcnow().isoformat(),
        "context": metadata.get("context", "general"),
        "session_phase": metadata.get("session_phase", "active"),
        "source": "mcp_server",
        "version": "1.1"
    }
    
    # Process through existing pipeline with enhanced metadata
    result = await self.memory_pipeline.process_text(text, user_id, metadata)
    return result
```

#### 1.3 Implement Conversation Context Tracking
**File**: `src/rolling_summary.py` (enhance existing)

```python
class EnhancedRollingSummaryManager(RollingSummaryManager):
    """Enhanced summary manager with session continuity"""
    
    async def update_with_relationships(self, content: str, user_id: str):
        """Update summary with relationship extraction"""
        # Extract entities and relationships
        entities = await self._extract_entities(content)
        relationships = await self._infer_relationships(entities)
        
        # Update summary with relationship context
        self.current_summary = await self._merge_with_relationships(
            self.current_summary, entities, relationships
        )
```

### Phase 2: Graph Memory Integration (Week 2)

#### 2.1 Add Neo4j Configuration
**File**: `src/config.py`

```python
@dataclass
class GraphStoreConfig:
    """Configuration for Neo4j graph store integration"""
    
    # Neo4j connection (already running locally)
    NEO4J_URL: str = os.getenv("NEO4J_URL", "bolt://localhost:7687")
    NEO4J_USERNAME: str = os.getenv("NEO4J_USERNAME", "neo4j")
    NEO4J_PASSWORD: str = os.getenv("NEO4J_PASSWORD", "password")
    
    # Graph configuration
    ENTITY_EXTRACTION_BATCH_SIZE: int = 10
    RELATIONSHIP_INFERENCE_THRESHOLD: float = 0.6
    MAX_GRAPH_TRAVERSAL_DEPTH: int = 3
    
    # Custom prompts
    ENTITY_EXTRACTION_PROMPT: str = """
    Extract entities as nodes and relationships as edges.
    Focus on: people, projects, technologies, concepts.
    """
```

#### 2.2 Create Graph Memory Service
**File**: `src/graph_memory_service.py` (new file)

```python
from neo4j import GraphDatabase
import logging
from typing import List, Dict, Any
from config import get_config

logger = logging.getLogger(__name__)
config = get_config()

class GraphMemoryService:
    """Neo4j integration for relationship-based memory evolution"""
    
    def __init__(self):
        self.driver = GraphDatabase.driver(
            config.graph_store.NEO4J_URL,
            auth=(config.graph_store.NEO4J_USERNAME, config.graph_store.NEO4J_PASSWORD)
        )
    
    async def extract_entities(self, content: str, memory_id: str) -> List[Dict[str, Any]]:
        """Extract entities from memory content"""
        # Implementation using LLM for entity extraction
        pass
    
    async def create_relationships(self, entities: List[Dict], memory_id: str):
        """Create graph relationships between entities"""
        with self.driver.session() as session:
            for entity in entities:
                # Create node
                session.run("""
                    MERGE (e:Entity {name: $name, type: $type})
                    SET e.updated_at = timestamp()
                    WITH e
                    MERGE (m:Memory {id: $memory_id})
                    MERGE (e)-[:MENTIONED_IN]->(m)
                """, name=entity['name'], type=entity['type'], memory_id=memory_id)
    
    async def find_related_memories(self, memory_id: str, depth: int = 2) -> List[str]:
        """Find memories connected through graph relationships"""
        with self.driver.session() as session:
            result = session.run("""
                MATCH (m:Memory {id: $memory_id})<-[:MENTIONED_IN]-(e:Entity)
                MATCH (e)-[:MENTIONED_IN]->(related:Memory)
                WHERE related.id <> $memory_id
                RETURN DISTINCT related.id as related_id
                LIMIT 10
            """, memory_id=memory_id)
            return [record['related_id'] for record in result]
```

#### 2.3 Integrate Graph with Vector Search
**File**: `src/memory_database.py` (modify similarity_search)

```python
async def similarity_search_with_graph(
    self, 
    embedding: List[float], 
    user_id: str, 
    threshold: float = 0.7, 
    limit: int = 5,
    include_graph_relations: bool = True
) -> List[Dict[str, Any]]:
    """Enhanced search combining vector similarity and graph relationships"""
    # Get vector similarity results
    vector_results = await self.similarity_search(embedding, user_id, threshold, limit)
    
    if include_graph_relations and self.graph_service:
        # Enhance with graph relationships
        for result in vector_results:
            related_ids = await self.graph_service.find_related_memories(result['id'])
            result['related_memories'] = related_ids
            result['relationship_score'] = len(related_ids) * 0.1  # Boost score
    
    # Re-rank based on combined score
    return sorted(vector_results, 
                  key=lambda x: x['distance'] - x.get('relationship_score', 0))
```

### Phase 3: History-Based Learning (Week 3)

#### 3.1 Add History Tracking Schema
**File**: `scripts/add_history_tracking.sql` (new file)

```sql
-- Create memory history table
CREATE TABLE IF NOT EXISTS memory_history (
    id SERIAL PRIMARY KEY,
    memory_id INTEGER REFERENCES memories(id) ON DELETE CASCADE,
    version INTEGER NOT NULL,
    content TEXT NOT NULL,
    embedding vector(768),
    metadata JSONB,
    operation VARCHAR(20) NOT NULL, -- ADD, UPDATE, DELETE
    reason TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    valid_from TIMESTAMP NOT NULL,
    valid_to TIMESTAMP
);

-- Index for efficient history queries
CREATE INDEX idx_memory_history_memory_id ON memory_history(memory_id);
CREATE INDEX idx_memory_history_valid_time ON memory_history(valid_from, valid_to);

-- Function to automatically track history
CREATE OR REPLACE FUNCTION track_memory_history()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'UPDATE' THEN
        INSERT INTO memory_history (
            memory_id, version, content, embedding, metadata,
            operation, reason, valid_from, valid_to
        )
        VALUES (
            OLD.id, 
            COALESCE((SELECT MAX(version) FROM memory_history WHERE memory_id = OLD.id), 0) + 1,
            OLD.content, OLD.embedding, OLD.metadata,
            'UPDATE', 'Content updated', OLD.updated_at, NOW()
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Attach trigger to memories table
CREATE TRIGGER memory_history_trigger
AFTER UPDATE ON memories
FOR EACH ROW EXECUTE FUNCTION track_memory_history();
```

#### 3.2 Implement History Analysis
**File**: `src/memory_update.py` (enhance with history)

```python
async def analyze_memory_history(self, memory_id: str) -> Dict[str, Any]:
    """Analyze memory evolution patterns"""
    sql = """
        SELECT version, content, operation, reason, created_at
        FROM memory_history
        WHERE memory_id = $1
        ORDER BY version DESC
        LIMIT 10
    """
    
    history = await self.db._execute_sql(sql, [memory_id])
    
    # Analyze patterns
    update_frequency = len(history) / max(1, (history[0]['created_at'] - history[-1]['created_at']).days)
    contradiction_count = sum(1 for h in history if 'contradict' in h.get('reason', '').lower())
    
    return {
        'total_updates': len(history),
        'update_frequency': update_frequency,
        'contradiction_count': contradiction_count,
        'evolution_pattern': self._detect_evolution_pattern(history)
    }
```

#### 3.3 Smart Update Logic with History
**File**: `src/memory_update.py` (modify _decide_operation)

```python
async def _decide_operation_with_history(
    self, 
    candidate: str, 
    similar_memories: List[Dict[str, Any]]
) -> Dict[str, str]:
    """Enhanced operation decision using history context"""
    
    # Get history for similar memories
    history_context = []
    for mem in similar_memories[:3]:
        history = await self.analyze_memory_history(mem['id'])
        history_context.append({
            'memory_id': mem['id'],
            'content': mem['content'],
            'update_frequency': history['update_frequency'],
            'evolution_pattern': history['evolution_pattern']
        })
    
    # Enhanced prompt with history
    prompt = f"""
    Analyze this candidate memory considering the evolution history of similar memories.
    
    Candidate: {candidate}
    
    Similar Memories with History:
    {json.dumps(history_context, indent=2)}
    
    Decision Criteria:
    - Frequently updated memories may need UPDATE rather than ADD
    - Contradictory patterns suggest DELETE of outdated information
    - Stable memories with high similarity suggest NOOP
    
    Respond with: {{"operation": "ADD|UPDATE|DELETE|NOOP", "reasoning": "explanation"}}
    """
    
    return await self._get_llm_decision(prompt)
```

### Phase 4: Search Optimization (Week 4)

#### 4.1 Enhanced Search with Context
**File**: `src/memory_handlers.py` (modify search_memory)

```python
async def search_memory_enhanced(
    self, 
    query: str, 
    user_id: str, 
    limit: int = 5,
    filters: Dict[str, Any] = None
) -> List[Dict[str, Any]]:
    """Enhanced search with metadata filters and graph context"""
    
    # Generate query embedding
    embedding = await self.bge_client.embed_single(query)
    
    # Build metadata filters
    sql_filters = []
    params = [embedding, user_id]
    
    if filters:
        if 'date_range' in filters:
            sql_filters.append("created_at BETWEEN $3 AND $4")
            params.extend(filters['date_range'])
        
        if 'context' in filters:
            sql_filters.append("metadata->>'context' = $5")
            params.append(filters['context'])
    
    # Combined search query
    sql = f"""
        WITH vector_results AS (
            SELECT id, content, metadata, 
                   embedding <-> $1 as distance,
                   updated_at
            FROM memories
            WHERE user_id = $2
            {' AND ' + ' AND '.join(sql_filters) if sql_filters else ''}
            ORDER BY embedding <-> $1
            LIMIT {limit * 2}
        ),
        ranked_results AS (
            SELECT *,
                   -- Boost recently updated memories
                   CASE 
                     WHEN updated_at > NOW() - INTERVAL '1 day' THEN 0.9
                     WHEN updated_at > NOW() - INTERVAL '7 days' THEN 0.95
                     ELSE 1.0
                   END as recency_factor
            FROM vector_results
        )
        SELECT * FROM ranked_results
        ORDER BY distance * recency_factor
        LIMIT $3
    """
    
    results = await self.db._execute_sql(sql, params)
    
    # Enhance with graph relationships if available
    if self.graph_service:
        for result in results:
            related = await self.graph_service.find_related_memories(result['id'])
            result['related_memories'] = related
    
    return results
```

#### 4.2 Query Expansion
**File**: `src/memory_handlers.py` (add query expansion)

```python
async def expand_query_with_context(self, query: str, user_id: str) -> str:
    """Expand query using conversation context and related terms"""
    
    # Get recent conversation context
    recent_summary = await self.rolling_summary.get_summary(user_id)
    
    # Use LLM to expand query
    prompt = f"""
    Expand this search query with related terms and context.
    
    Original Query: {query}
    Recent Context: {recent_summary}
    
    Generate 2-3 alternative phrasings or related terms that might help find relevant memories.
    Return as JSON array of strings.
    """
    
    expanded_terms = await self.llm_client.generate(prompt)
    return json.loads(expanded_terms)
```

## Implementation Tasks

### Week 1: Custom Extraction Prompts
1. [ ] Update `memory_extraction.py` with enhanced prompts
2. [ ] Add metadata tracking to `memory_handlers.py`
3. [ ] Enhance `rolling_summary.py` with relationship awareness
4. [ ] Create unit tests for new extraction patterns
5. [ ] Update configuration with new prompt templates

### Week 2: Graph Memory Integration  
1. [ ] Add Neo4j configuration to `config.py`
2. [ ] Create `graph_memory_service.py` module
3. [ ] Configure connection to existing Neo4j container
4. [ ] Integrate graph service with memory database
5. [ ] Add graph relationship tests
6. [ ] Update MCP endpoints for graph queries

### Week 3: History-Based Learning
1. [ ] Create and run history tracking migration
2. [ ] Implement history analysis in `memory_update.py`
3. [ ] Enhance decision logic with history context
4. [ ] Add history tracking tests
5. [ ] Create history visualization endpoint

### Week 4: Search Optimization
1. [ ] Enhance search with metadata filters
2. [ ] Implement query expansion
3. [ ] Add recency boosting to search
4. [ ] Combine vector and graph search
5. [ ] Performance optimization and testing
6. [ ] Update documentation

## Validation Gates

### Phase 1 Validation
```bash
# Run extraction tests
pytest tests/test_enhanced_extraction.py -v

# Verify prompt effectiveness
python tests/validate_extraction_prompts.py

# Performance check
pytest tests/test_extraction_performance.py -v
# Target: < 2s per extraction
```

### Phase 2 Validation
```bash
# Neo4j integration tests
pytest tests/test_graph_integration.py -v

# Entity extraction accuracy
python tests/validate_entity_extraction.py
# Target: > 85% accuracy

# Graph traversal performance
pytest tests/test_graph_performance.py -v
# Target: < 500ms for 2-hop traversal
```

### Phase 3 Validation
```bash
# History tracking tests
pytest tests/test_history_tracking.py -v

# Conflict resolution accuracy
python tests/validate_conflict_resolution.py
# Target: > 90% correct decisions

# History query performance
pytest tests/test_history_performance.py -v
# Target: < 200ms for history retrieval
```

### Phase 4 Validation
```bash
# Search relevance tests
pytest tests/test_enhanced_search.py -v

# End-to-end pipeline test
./dev.sh test-integration

# Performance benchmarks
pytest tests/test_performance_benchmarks.py -v
# Targets:
# - P50 latency: < 708ms
# - P95 latency: < 1440ms
# - Search accuracy: 26% improvement
```

## Gotchas & Considerations

### Neo4j Setup
- Neo4j is already running locally at `bolt://localhost:7687`
- Ensure password is configured correctly in environment
- Initial setup needs manual index creation
- Graph queries can be expensive - implement caching
- Container already has persistent storage configured

### Performance Impact
- History tracking increases storage by ~2x
- Graph traversal adds 100-200ms to searches
- LLM calls for entity extraction add latency
- Implement aggressive caching for production

### Backward Compatibility
- Existing MCP endpoints must remain functional
- Database migrations must be reversible
- Add feature flags for gradual rollout
- Maintain separate graph service to allow disable

### Prompt Tuning
- Custom prompts need iteration for consistency
- Different LLM providers may need adjustments
- Monitor extraction quality metrics
- A/B test prompt variations

## Success Metrics

1. **Memory Quality**: 26% improvement in relevance (LOCOMO benchmark)
2. **Latency**: P95 < 1.5s for complete pipeline
3. **Relationships**: >80% of memories have graph connections
4. **Evolution**: >60% of updates show temporal context
5. **Search**: >90% user satisfaction with results

## Resources

- Mem0 GitHub: https://github.com/mem0ai/mem0
- Mem0 Documentation (in RAG): Available via spark-rag MCP server
- Neo4j Instance: Running locally at `bolt://localhost:7687`
- BGE Embeddings: https://github.com/FlagOpen/FlagEmbedding
- LOCOMO Benchmark: https://arxiv.org/html/2504.19413v1

---

**Confidence Level**: 8/10

This PRP provides comprehensive context for implementing Mem0's self-evolution capabilities. The phased approach allows incremental value delivery while building toward the full vision. Minor iterations may be needed for prompt tuning and Neo4j configuration optimization.