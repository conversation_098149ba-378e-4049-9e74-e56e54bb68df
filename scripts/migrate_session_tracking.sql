-- Session Tracking Migration for Spark Memory MCP
-- Adds session management capabilities to the existing database schema

-- Create sessions table for explicit session tracking
CREATE TABLE IF NOT EXISTS sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id TEXT NOT NULL,
    session_name TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'paused', 'ended', 'expired')),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP,
    ended_at TIMESTAMP
);

-- Add session_id to memories table
ALTER TABLE memories 
ADD COLUMN IF NOT EXISTS session_id UUID REFERENCES sessions(id) ON DELETE SET NULL;

-- Add session_id to conversation_summaries table
ALTER TABLE conversation_summaries 
ADD COLUMN IF NOT EXISTS session_id UUID REFERENCES sessions(id) ON DELETE SET NULL;

-- Create indexes for session-based queries
CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_sessions_status ON sessions(status);
CREATE INDEX IF NOT EXISTS idx_sessions_created_at ON sessions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_sessions_expires_at ON sessions(expires_at) WHERE expires_at IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_memories_session_id ON memories(session_id) WHERE session_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_conversation_summaries_session_id ON conversation_summaries(session_id) WHERE session_id IS NOT NULL;

-- Composite indexes for efficient session-based memory retrieval
CREATE INDEX IF NOT EXISTS idx_memories_user_session ON memories(user_id, session_id);
CREATE INDEX IF NOT EXISTS idx_memories_session_created ON memories(session_id, created_at DESC) WHERE session_id IS NOT NULL;

-- Create a view for active sessions with memory counts
CREATE OR REPLACE VIEW active_sessions_with_stats AS
SELECT 
    s.id,
    s.user_id,
    s.session_name,
    s.status,
    s.created_at,
    s.updated_at,
    s.expires_at,
    COUNT(m.id) as memory_count,
    MAX(m.created_at) as last_memory_at,
    COUNT(cs.user_id) as summary_count
FROM sessions s
LEFT JOIN memories m ON s.id = m.session_id
LEFT JOIN conversation_summaries cs ON s.id = cs.session_id
WHERE s.status = 'active'
GROUP BY s.id, s.user_id, s.session_name, s.status, s.created_at, s.updated_at, s.expires_at
ORDER BY s.updated_at DESC;

-- Function to automatically expire sessions
CREATE OR REPLACE FUNCTION expire_old_sessions()
RETURNS INTEGER AS $$
DECLARE
    expired_count INTEGER;
BEGIN
    UPDATE sessions 
    SET status = 'expired', 
        updated_at = NOW(),
        ended_at = NOW()
    WHERE status = 'active' 
      AND expires_at IS NOT NULL 
      AND expires_at < NOW();
    
    GET DIAGNOSTICS expired_count = ROW_COUNT;
    RETURN expired_count;
END;
$$ LANGUAGE plpgsql;

-- Function to create a new session
CREATE OR REPLACE FUNCTION create_session(
    p_user_id TEXT,
    p_session_name TEXT DEFAULT NULL,
    p_expires_in_hours INTEGER DEFAULT 24,
    p_metadata JSONB DEFAULT '{}'
)
RETURNS UUID AS $$
DECLARE
    new_session_id UUID;
    expires_timestamp TIMESTAMP;
BEGIN
    -- Calculate expiration time
    IF p_expires_in_hours > 0 THEN
        expires_timestamp := NOW() + (p_expires_in_hours || ' hours')::INTERVAL;
    ELSE
        expires_timestamp := NULL;
    END IF;
    
    -- Insert new session
    INSERT INTO sessions (user_id, session_name, expires_at, metadata)
    VALUES (p_user_id, p_session_name, expires_timestamp, p_metadata)
    RETURNING id INTO new_session_id;
    
    RETURN new_session_id;
END;
$$ LANGUAGE plpgsql;

-- Function to end a session
CREATE OR REPLACE FUNCTION end_session(p_session_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    updated_count INTEGER;
BEGIN
    UPDATE sessions
    SET status = 'ended',
        ended_at = NOW(),
        updated_at = NOW()
    WHERE id = p_session_id
      AND status = 'active';

    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RETURN updated_count > 0;
END;
$$ LANGUAGE plpgsql;

-- Add comments for documentation
COMMENT ON TABLE sessions IS 'Session tracking for conversation and memory management';
COMMENT ON COLUMN sessions.id IS 'Unique session identifier';
COMMENT ON COLUMN sessions.user_id IS 'User who owns this session';
COMMENT ON COLUMN sessions.session_name IS 'Optional human-readable session name';
COMMENT ON COLUMN sessions.status IS 'Session status: active, paused, ended, expired';
COMMENT ON COLUMN sessions.metadata IS 'Additional session metadata (JSON)';
COMMENT ON COLUMN sessions.expires_at IS 'When session expires (NULL for no expiration)';
COMMENT ON COLUMN sessions.ended_at IS 'When session was explicitly ended';

COMMENT ON COLUMN memories.session_id IS 'Session this memory belongs to (NULL for legacy memories)';
COMMENT ON COLUMN conversation_summaries.session_id IS 'Session this summary belongs to (NULL for legacy summaries)';

-- Grant permissions
GRANT ALL ON sessions TO postgres;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO postgres;

-- Display migration results
SELECT 'Session tracking migration completed successfully' AS status;
