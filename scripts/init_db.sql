-- PostgreSQL Initialization Script for Spark Memory System
-- Creates the database schema with pgvector support for BGE embeddings

-- Enable pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Create memories table with BGE embeddings (768 dimensions)
CREATE TABLE IF NOT EXISTS memories (
    id SERIAL PRIMARY KEY,
    user_id TEXT NOT NULL,
    content TEXT NOT NULL,
    embedding vector(768),  -- BGE base model uses 768 dimensions
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create conversation summaries table
CREATE TABLE IF NOT EXISTS conversation_summaries (
    user_id TEXT PRIMARY KEY,
    summary TEXT NOT NULL,
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_memories_user_id ON memories(user_id);
CREATE INDEX IF NOT EXISTS idx_memories_created_at ON memories(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_conversation_summaries_updated_at ON conversation_summaries(updated_at DESC);

-- Create HNSW index for efficient vector similarity search
-- This index is optimized for L2 distance (used by BGE embeddings)
CREATE INDEX IF NOT EXISTS idx_memories_embedding_hnsw 
ON memories USING hnsw (embedding vector_l2_ops) 
WITH (m = 16, ef_construction = 64);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_memories_updated_at BEFORE UPDATE ON memories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample data for testing (optional)
-- Uncomment the lines below if you want sample data for testing

-- INSERT INTO memories (user_id, content, embedding, metadata) VALUES
--     ('test_user', 'The team prefers PostgreSQL for database operations', 
--      '[0.1,0.2,0.3]'::vector || array_fill(0.0, ARRAY[765])::vector, 
--      '{"tags": ["database", "preference"], "importance": "high"}'::jsonb),
--     ('test_user', 'BGE embeddings are used for semantic search', 
--      '[0.2,0.3,0.4]'::vector || array_fill(0.0, ARRAY[765])::vector, 
--      '{"tags": ["embeddings", "search"], "importance": "medium"}'::jsonb);

-- Insert sample conversation summary
-- INSERT INTO conversation_summaries (user_id, summary) VALUES
--     ('test_user', 'User is interested in database technologies and AI systems. Prefers PostgreSQL and is learning about embedding systems.');

-- Grant necessary permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO postgres;

-- Display table information
\d+ memories;
\d+ conversation_summaries;

-- Show vector extension version
SELECT * FROM pg_extension WHERE extname = 'vector';

NOTICE 'Spark Memory Database initialized successfully with pgvector support';