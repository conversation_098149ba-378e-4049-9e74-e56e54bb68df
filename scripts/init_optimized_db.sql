-- Optimized Database Schema for Spark Memory MCP
-- Production-ready schema with vector similarity indexes

-- Enable pgvector extension (must be run as superuser)
CREATE EXTENSION IF NOT EXISTS vector;

-- Create memories table with optimized structure
CREATE TABLE IF NOT EXISTS memories (
    id SERIAL PRIMARY KEY,
    user_id TEXT NOT NULL,
    content TEXT NOT NULL,
    embedding vector(768) NOT NULL,  -- BGE base embeddings are 768-dimensional
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create conversation summaries table
CREATE TABLE IF NOT EXISTS conversation_summaries (
    user_id TEXT PRIMARY KEY,
    summary TEXT NOT NULL,
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Performance Indexes
-- ====================

-- 1. HNSW index for vector similarity (L2 distance for BGE)
-- This dramatically improves similarity search performance
-- m=16: number of bi-directional links for each node (good balance)
-- ef_construction=64: size of dynamic candidate list (good for accuracy)
CREATE INDEX IF NOT EXISTS memories_embedding_hnsw_idx 
ON memories USING hnsw (embedding vector_l2_ops) 
WITH (m = 16, ef_construction = 64);

-- 2. User-based queries (most common filter)
CREATE INDEX IF NOT EXISTS memories_user_id_idx ON memories (user_id);

-- 3. Temporal queries (recent memories, chronological listing)
CREATE INDEX IF NOT EXISTS memories_created_at_idx ON memories (created_at DESC);

-- 4. Combined user+time index for efficient pagination
CREATE INDEX IF NOT EXISTS memories_user_created_idx ON memories (user_id, created_at DESC);

-- 5. Metadata search support (if needed for filtering)
CREATE INDEX IF NOT EXISTS memories_metadata_gin_idx ON memories USING gin (metadata);

-- Optimization Settings
-- ====================

-- Enable parallel query execution for large similarity searches
SET max_parallel_workers_per_gather = 2;

-- Increase work_mem for vector operations (adjust based on available RAM)
-- This helps with vector index builds and large similarity searches
SET work_mem = '256MB';

-- Set effective_cache_size to ~75% of available RAM (adjust for your system)
-- This helps the query planner make better decisions
SET effective_cache_size = '4GB';

-- Vector-specific optimizations
-- Increase maintenance_work_mem for index building
SET maintenance_work_mem = '512MB';

-- Comments and Documentation
-- ==========================

COMMENT ON TABLE memories IS 'Semantic memory storage with BGE vector embeddings';
COMMENT ON COLUMN memories.embedding IS 'BGE base embeddings (768-dim) using CLS pooling';
COMMENT ON COLUMN memories.metadata IS 'Flexible metadata storage for memory attributes';

COMMENT ON INDEX memories_embedding_hnsw_idx IS 'HNSW index for fast L2 distance similarity search';
COMMENT ON INDEX memories_user_id_idx IS 'Primary filter index for user-scoped queries';
COMMENT ON INDEX memories_created_at_idx IS 'Temporal index for chronological access';

-- Performance Analysis Queries
-- ============================

-- View to check index usage
CREATE OR REPLACE VIEW memory_index_stats AS
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan as times_used,
    idx_tup_read as tuples_read,
    idx_tup_fetch as tuples_fetched
FROM pg_stat_user_indexes 
WHERE tablename = 'memories'
ORDER BY idx_scan DESC;

-- View to monitor vector search performance
CREATE OR REPLACE VIEW memory_table_stats AS
SELECT 
    schemaname,
    tablename,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes,
    seq_scan as sequential_scans,
    seq_tup_read as sequential_tuples_read,
    idx_scan as index_scans,
    idx_tup_fetch as index_tuples_fetched
FROM pg_stat_user_tables 
WHERE tablename IN ('memories', 'conversation_summaries');

-- Example Queries for Testing
-- ============================

-- Test vector similarity (replace with actual embedding)
-- SELECT id, content, (embedding <-> '[0.1,0.2,...]'::vector) as distance 
-- FROM memories 
-- WHERE user_id = 'test_user' 
-- ORDER BY embedding <-> '[0.1,0.2,...]'::vector 
-- LIMIT 5;

-- Check vector index is being used
-- EXPLAIN (ANALYZE, BUFFERS) 
-- SELECT id, content, (embedding <-> '[0.1,0.2,...]'::vector) as distance 
-- FROM memories 
-- WHERE user_id = 'test_user' 
-- ORDER BY embedding <-> '[0.1,0.2,...]'::vector 
-- LIMIT 5;

-- Vacuum and analyze for optimal performance
VACUUM ANALYZE memories;
VACUUM ANALYZE conversation_summaries;

-- Success message
SELECT 'Optimized Spark Memory database schema initialized successfully!' as status;