-- Memory Intelligence Enhancement: History Tracking Migration
-- Phase 3: History-Based Learning with Version Tracking
-- 
-- This migration adds comprehensive memory history tracking for evolution analysis
-- and temporal memory management as specified in the Mem0 research.

-- Create memory history table for version tracking
CREATE TABLE IF NOT EXISTS memory_history (
    id SERIAL PRIMARY KEY,
    memory_id INTEGER REFERENCES memories(id) ON DELETE CASCADE,
    version INTEGER NOT NULL,
    content TEXT NOT NULL,
    embedding vector(768), -- <PERSON><PERSON> embedding dimension
    metadata JSONB,
    operation VARCHAR(20) NOT NULL CHECK (operation IN ('ADD', 'UPDATE', 'DELETE', 'NOOP', 'MERGE')),
    reason TEXT,
    similarity_score FLOAT,
    evolution_indicators JSONB, -- Stores detected evolution patterns
    relationship_changes JSONB, -- Stores relationship evolution data
    created_at TIMESTAMP DEFAULT NOW(),
    valid_from TIMESTAMP NOT NULL,
    valid_to TIMESTAMP,
    user_id VARCHAR(100) NOT NULL,
    source_operation VARCHAR(50) DEFAULT 'system_update',
    conflict_resolution VARCHAR(100),
    
    -- Constraints
    CONSTRAINT valid_time_range CHECK (valid_to IS NULL OR valid_to >= valid_from),
    CONSTRAINT version_positive CHECK (version > 0),
    CONSTRAINT similarity_score_range CHECK (similarity_score IS NULL OR (similarity_score >= 0 AND similarity_score <= 1))
);

-- Create indexes for efficient history queries
CREATE INDEX IF NOT EXISTS idx_memory_history_memory_id ON memory_history(memory_id);
CREATE INDEX IF NOT EXISTS idx_memory_history_user_id ON memory_history(user_id);
CREATE INDEX IF NOT EXISTS idx_memory_history_valid_time ON memory_history(valid_from, valid_to);
CREATE INDEX IF NOT EXISTS idx_memory_history_operation ON memory_history(operation);
CREATE INDEX IF NOT EXISTS idx_memory_history_created_at ON memory_history(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_memory_history_version ON memory_history(memory_id, version);

-- Create GIN index for JSONB columns for efficient querying
CREATE INDEX IF NOT EXISTS idx_memory_history_metadata_gin ON memory_history USING GIN(metadata);
CREATE INDEX IF NOT EXISTS idx_memory_history_evolution_gin ON memory_history USING GIN(evolution_indicators);
CREATE INDEX IF NOT EXISTS idx_memory_history_relationships_gin ON memory_history USING GIN(relationship_changes);

-- Create composite index for performance queries
CREATE INDEX IF NOT EXISTS idx_memory_history_composite ON memory_history(user_id, memory_id, created_at DESC);

-- Function to automatically track memory history on updates
CREATE OR REPLACE FUNCTION track_memory_history()
RETURNS TRIGGER AS $$
DECLARE
    version_num INTEGER;
    evolution_data JSONB;
    relationship_data JSONB;
BEGIN
    -- Get next version number for this memory
    SELECT COALESCE(MAX(version), 0) + 1 
    INTO version_num
    FROM memory_history 
    WHERE memory_id = COALESCE(NEW.id, OLD.id);
    
    -- Detect evolution patterns in content changes
    evolution_data := jsonb_build_object(
        'content_length_change', 
        CASE 
            WHEN TG_OP = 'UPDATE' THEN LENGTH(NEW.content) - LENGTH(OLD.content)
            ELSE NULL
        END,
        'has_temporal_markers', 
        CASE 
            WHEN TG_OP = 'INSERT' THEN NEW.content ~* '\b(now|currently|recently|today|this week|changed|updated|learned|discovered)\b'
            WHEN TG_OP = 'UPDATE' THEN NEW.content ~* '\b(now|currently|recently|today|this week|changed|updated|learned|discovered)\b'
            ELSE FALSE
        END,
        'has_evolution_keywords',
        CASE 
            WHEN TG_OP = 'INSERT' THEN NEW.content ~* '\b(prefers|previously|instead of|no longer|switched to|evolved to)\b'
            WHEN TG_OP = 'UPDATE' THEN NEW.content ~* '\b(prefers|previously|instead of|no longer|switched to|evolved to)\b'
            ELSE FALSE
        END,
        'operation_timestamp', NOW()
    );
    
    -- Detect relationship patterns
    relationship_data := jsonb_build_object(
        'has_relationships',
        CASE 
            WHEN TG_OP = 'INSERT' THEN NEW.content ~* '\b(collaborates with|works with|uses|depends on|relates to|member of)\b'
            WHEN TG_OP = 'UPDATE' THEN NEW.content ~* '\b(collaborates with|works with|uses|depends on|relates to|member of)\b'
            ELSE FALSE
        END,
        'relationship_keywords_found', 
        CASE 
            WHEN TG_OP = 'INSERT' THEN array_agg(match[1]) FROM regexp_matches(NEW.content, '\b(collaborates with|works with|uses|depends on|relates to|member of)\b', 'gi') AS match
            WHEN TG_OP = 'UPDATE' THEN array_agg(match[1]) FROM regexp_matches(NEW.content, '\b(collaborates with|works with|uses|depends on|relates to|member of)\b', 'gi') AS match
            ELSE NULL
        END
    );
    
    -- Handle different trigger operations
    IF TG_OP = 'INSERT' THEN
        -- Record initial memory creation
        INSERT INTO memory_history (
            memory_id, version, content, embedding, metadata,
            operation, reason, evolution_indicators, relationship_changes,
            valid_from, user_id, source_operation
        )
        VALUES (
            NEW.id, version_num, NEW.content, NEW.embedding, NEW.metadata,
            'ADD', 'Initial memory creation', evolution_data, relationship_data,
            NEW.created_at, NEW.user_id, 'memory_creation'
        );
        
        RETURN NEW;
        
    ELSIF TG_OP = 'UPDATE' THEN
        -- Close previous version
        UPDATE memory_history 
        SET valid_to = NOW()
        WHERE memory_id = OLD.id AND valid_to IS NULL;
        
        -- Record memory update
        INSERT INTO memory_history (
            memory_id, version, content, embedding, metadata,
            operation, reason, evolution_indicators, relationship_changes,
            valid_from, user_id, source_operation,
            similarity_score
        )
        VALUES (
            NEW.id, version_num, NEW.content, NEW.embedding, NEW.metadata,
            'UPDATE', 'Memory content updated', evolution_data, relationship_data,
            NOW(), NEW.user_id, 'memory_update',
            -- Calculate simple similarity score based on content length difference
            1.0 - LEAST(1.0, ABS(LENGTH(NEW.content) - LENGTH(OLD.content))::FLOAT / GREATEST(LENGTH(NEW.content), LENGTH(OLD.content)))
        );
        
        RETURN NEW;
        
    ELSIF TG_OP = 'DELETE' THEN
        -- Record memory deletion
        INSERT INTO memory_history (
            memory_id, version, content, embedding, metadata,
            operation, reason, evolution_indicators, relationship_changes,
            valid_from, valid_to, user_id, source_operation
        )
        VALUES (
            OLD.id, version_num, OLD.content, OLD.embedding, OLD.metadata,
            'DELETE', 'Memory deleted', evolution_data, relationship_data,
            OLD.updated_at, NOW(), OLD.user_id, 'memory_deletion'
        );
        
        RETURN OLD;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Attach trigger to memories table for automatic history tracking
DROP TRIGGER IF EXISTS memory_history_trigger ON memories;
CREATE TRIGGER memory_history_trigger
    AFTER INSERT OR UPDATE OR DELETE ON memories
    FOR EACH ROW EXECUTE FUNCTION track_memory_history();

-- Create view for active memories with history summary
CREATE OR REPLACE VIEW memory_evolution_summary AS
SELECT 
    m.id,
    m.user_id,
    m.content,
    m.created_at,
    m.updated_at,
    COUNT(mh.id) as version_count,
    MAX(mh.version) as current_version,
    COUNT(CASE WHEN mh.operation = 'UPDATE' THEN 1 END) as update_count,
    AVG(mh.similarity_score) as avg_similarity_score,
    BOOL_OR(mh.evolution_indicators->>'has_temporal_markers' = 'true') as has_evolution_patterns,
    BOOL_OR(mh.relationship_changes->>'has_relationships' = 'true') as has_relationships,
    MAX(mh.created_at) as last_history_update
FROM memories m
LEFT JOIN memory_history mh ON m.id = mh.memory_id
GROUP BY m.id, m.user_id, m.content, m.created_at, m.updated_at;

-- Create function to get memory evolution analysis
CREATE OR REPLACE FUNCTION analyze_memory_evolution(
    p_memory_id INTEGER,
    p_limit INTEGER DEFAULT 10
)
RETURNS TABLE (
    version INTEGER,
    operation VARCHAR(20),
    content_preview TEXT,
    evolution_detected BOOLEAN,
    relationship_detected BOOLEAN,
    similarity_score FLOAT,
    days_since_creation INTEGER,
    reason TEXT,
    created_at TIMESTAMP
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        mh.version,
        mh.operation,
        LEFT(mh.content, 100) || CASE WHEN LENGTH(mh.content) > 100 THEN '...' ELSE '' END as content_preview,
        (mh.evolution_indicators->>'has_evolution_keywords')::BOOLEAN as evolution_detected,
        (mh.relationship_changes->>'has_relationships')::BOOLEAN as relationship_detected,
        mh.similarity_score,
        EXTRACT(DAY FROM mh.created_at - (SELECT MIN(created_at) FROM memory_history WHERE memory_id = p_memory_id))::INTEGER as days_since_creation,
        mh.reason,
        mh.created_at
    FROM memory_history mh
    WHERE mh.memory_id = p_memory_id
    ORDER BY mh.version DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- Create function to find memory conflicts and resolution suggestions
CREATE OR REPLACE FUNCTION detect_memory_conflicts(
    p_user_id VARCHAR(100),
    p_similarity_threshold FLOAT DEFAULT 0.8
)
RETURNS TABLE (
    memory_id INTEGER,
    conflicting_memory_id INTEGER,
    similarity_score FLOAT,
    conflict_type VARCHAR(50),
    resolution_suggestion TEXT,
    last_update TIMESTAMP
) AS $$
BEGIN
    RETURN QUERY
    WITH memory_pairs AS (
        SELECT 
            m1.id as mem1_id,
            m2.id as mem2_id,
            m1.content as content1,
            m2.content as content2,
            m1.embedding <-> m2.embedding as distance,
            1.0 - (m1.embedding <-> m2.embedding) as similarity,
            GREATEST(m1.updated_at, m2.updated_at) as latest_update
        FROM memories m1
        JOIN memories m2 ON m1.id < m2.id AND m1.user_id = m2.user_id
        WHERE m1.user_id = p_user_id
        AND (m1.embedding <-> m2.embedding) < (1.0 - p_similarity_threshold)
    )
    SELECT 
        mp.mem1_id as memory_id,
        mp.mem2_id as conflicting_memory_id,
        mp.similarity as similarity_score,
        CASE 
            WHEN mp.content1 ~* '\b(no longer|instead of|changed)\b' OR mp.content2 ~* '\b(no longer|instead of|changed)\b' 
            THEN 'evolution_conflict'
            WHEN LENGTH(mp.content1) = LENGTH(mp.content2) AND mp.similarity > 0.95 
            THEN 'duplicate'
            ELSE 'semantic_overlap'
        END as conflict_type,
        CASE 
            WHEN mp.content1 ~* '\b(no longer|instead of|changed)\b' OR mp.content2 ~* '\b(no longer|instead of|changed)\b' 
            THEN 'Consider UPDATE operation to merge evolution information'
            WHEN LENGTH(mp.content1) = LENGTH(mp.content2) AND mp.similarity > 0.95 
            THEN 'Consider DELETE operation to remove duplicate'
            ELSE 'Review for potential consolidation or disambiguation'
        END as resolution_suggestion,
        mp.latest_update as last_update
    FROM memory_pairs mp
    ORDER BY mp.similarity DESC;
END;
$$ LANGUAGE plpgsql;

-- Create indexes for the new functions
CREATE INDEX IF NOT EXISTS idx_memories_embedding_ops ON memories USING ivfflat (embedding vector_l2_ops);

-- Add comments for documentation
COMMENT ON TABLE memory_history IS 'Tracks complete evolution history of memories for temporal analysis and conflict resolution';
COMMENT ON FUNCTION track_memory_history() IS 'Automatically tracks memory changes with evolution pattern detection';
COMMENT ON FUNCTION analyze_memory_evolution(INTEGER, INTEGER) IS 'Analyzes memory evolution patterns and temporal changes';
COMMENT ON FUNCTION detect_memory_conflicts(VARCHAR, FLOAT) IS 'Detects potential memory conflicts based on semantic similarity and evolution patterns';
COMMENT ON VIEW memory_evolution_summary IS 'Provides summary statistics for memory evolution analysis';

-- Example queries for testing the history tracking

-- Get evolution summary for a user
-- SELECT * FROM memory_evolution_summary WHERE user_id = 'test_user' ORDER BY update_count DESC;

-- Analyze specific memory evolution
-- SELECT * FROM analyze_memory_evolution(123);

-- Find potential conflicts
-- SELECT * FROM detect_memory_conflicts('test_user', 0.8);

-- Get memories with high evolution activity
-- SELECT * FROM memory_evolution_summary WHERE update_count > 2 AND has_evolution_patterns = true;

-- Migration completed successfully
SELECT 'Memory History Tracking Migration completed successfully' as status;