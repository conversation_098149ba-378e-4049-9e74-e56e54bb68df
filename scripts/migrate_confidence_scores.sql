-- ==========================================
-- Confidence Scores Migration
-- Phase 2 Feature 2/3: Memory Quality Assessment
-- ==========================================

-- Add confidence scoring columns to memories table
ALTER TABLE memories 
ADD COLUMN IF NOT EXISTS confidence_score FLOAT DEFAULT 0.5 CHECK (confidence_score >= 0.0 AND confidence_score <= 1.0),
ADD COLUMN IF NOT EXISTS confidence_factors JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS confidence_updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Create index for efficient confidence-based queries
CREATE INDEX IF NOT EXISTS idx_memories_confidence_score ON memories(confidence_score DESC);
CREATE INDEX IF NOT EXISTS idx_memories_confidence_user ON memories(user_id, confidence_score DESC);
CREATE INDEX IF NOT EXISTS idx_memories_confidence_session ON memories(session_id, confidence_score DESC) WHERE session_id IS NOT NULL;

-- Create composite index for confidence + similarity searches
CREATE INDEX IF NOT EXISTS idx_memories_confidence_embedding ON memories USING ivfflat (embedding vector_l2_ops) 
WHERE confidence_score >= 0.5;

-- Function to update memory confidence score
CREATE OR REPLACE FUNCTION update_memory_confidence(
    p_memory_id UUID,
    p_new_confidence FLOAT,
    p_confidence_factors JSONB DEFAULT NULL,
    p_reason TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    updated_count INTEGER;
    current_factors JSONB;
BEGIN
    -- Validate confidence score range
    IF p_new_confidence < 0.0 OR p_new_confidence > 1.0 THEN
        RAISE EXCEPTION 'Confidence score must be between 0.0 and 1.0, got: %', p_new_confidence;
    END IF;
    
    -- Get current confidence factors if updating
    IF p_confidence_factors IS NULL THEN
        SELECT confidence_factors INTO current_factors 
        FROM memories 
        WHERE id = p_memory_id;
        
        -- Add update reason to existing factors
        IF p_reason IS NOT NULL THEN
            current_factors = COALESCE(current_factors, '{}'::jsonb) || 
                jsonb_build_object('last_update_reason', p_reason, 'last_updated', NOW());
        END IF;
    ELSE
        current_factors = p_confidence_factors;
    END IF;
    
    -- Update the memory confidence
    UPDATE memories 
    SET confidence_score = p_new_confidence,
        confidence_factors = current_factors,
        confidence_updated_at = NOW(),
        updated_at = NOW()
    WHERE id = p_memory_id;
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RETURN updated_count > 0;
END;
$$ LANGUAGE plpgsql;

-- Function to boost confidence for accessed memories
CREATE OR REPLACE FUNCTION boost_memory_confidence(
    p_memory_id UUID,
    p_boost_amount FLOAT DEFAULT 0.05
)
RETURNS FLOAT AS $$
DECLARE
    current_confidence FLOAT;
    new_confidence FLOAT;
    updated_count INTEGER;
BEGIN
    -- Get current confidence
    SELECT confidence_score INTO current_confidence 
    FROM memories 
    WHERE id = p_memory_id;
    
    IF current_confidence IS NULL THEN
        RETURN NULL; -- Memory not found
    END IF;
    
    -- Calculate new confidence (capped at 1.0)
    new_confidence = LEAST(current_confidence + p_boost_amount, 1.0);
    
    -- Update if there's a meaningful change
    IF new_confidence > current_confidence THEN
        UPDATE memories 
        SET confidence_score = new_confidence,
            confidence_factors = COALESCE(confidence_factors, '{}'::jsonb) || 
                jsonb_build_object('access_boost', p_boost_amount, 'boosted_at', NOW()),
            confidence_updated_at = NOW()
        WHERE id = p_memory_id;
        
        GET DIAGNOSTICS updated_count = ROW_COUNT;
        
        IF updated_count > 0 THEN
            RETURN new_confidence;
        END IF;
    END IF;
    
    RETURN current_confidence;
END;
$$ LANGUAGE plpgsql;

-- Function to decay confidence over time
CREATE OR REPLACE FUNCTION decay_old_memory_confidence(
    p_days_threshold INTEGER DEFAULT 30,
    p_decay_amount FLOAT DEFAULT 0.1
)
RETURNS INTEGER AS $$
DECLARE
    updated_count INTEGER;
BEGIN
    -- Decay confidence for memories older than threshold
    UPDATE memories 
    SET confidence_score = GREATEST(confidence_score - p_decay_amount, 0.0),
        confidence_factors = COALESCE(confidence_factors, '{}'::jsonb) || 
            jsonb_build_object('time_decay', p_decay_amount, 'decayed_at', NOW()),
        confidence_updated_at = NOW()
    WHERE created_at < NOW() - INTERVAL '1 day' * p_days_threshold
      AND confidence_score > 0.0;
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RETURN updated_count;
END;
$$ LANGUAGE plpgsql;

-- View for high-confidence memories
CREATE OR REPLACE VIEW high_confidence_memories AS
SELECT 
    id,
    user_id,
    session_id,
    content,
    confidence_score,
    confidence_factors,
    confidence_updated_at,
    created_at,
    updated_at
FROM memories 
WHERE confidence_score >= 0.7
ORDER BY confidence_score DESC, created_at DESC;

-- View for confidence statistics by user
CREATE OR REPLACE VIEW user_confidence_stats AS
SELECT 
    user_id,
    COUNT(*) as total_memories,
    AVG(confidence_score) as avg_confidence,
    MIN(confidence_score) as min_confidence,
    MAX(confidence_score) as max_confidence,
    COUNT(*) FILTER (WHERE confidence_score >= 0.7) as high_confidence_count,
    COUNT(*) FILTER (WHERE confidence_score >= 0.4 AND confidence_score < 0.7) as medium_confidence_count,
    COUNT(*) FILTER (WHERE confidence_score < 0.4) as low_confidence_count
FROM memories 
GROUP BY user_id;

-- Update existing memories with default confidence scores based on content length and recency
UPDATE memories 
SET confidence_score = CASE 
    WHEN LENGTH(content) > 100 AND created_at > NOW() - INTERVAL '7 days' THEN 0.7
    WHEN LENGTH(content) > 50 AND created_at > NOW() - INTERVAL '30 days' THEN 0.6
    WHEN created_at > NOW() - INTERVAL '90 days' THEN 0.5
    ELSE 0.4
END,
confidence_factors = jsonb_build_object(
    'initial_assignment', true,
    'based_on', 'content_length_and_recency',
    'assigned_at', NOW()
),
confidence_updated_at = NOW()
WHERE confidence_score = 0.5; -- Only update default values

-- Grant permissions
GRANT SELECT ON high_confidence_memories TO PUBLIC;
GRANT SELECT ON user_confidence_stats TO PUBLIC;

-- Migration completion status
SELECT 'Confidence scores migration completed successfully' as status;
