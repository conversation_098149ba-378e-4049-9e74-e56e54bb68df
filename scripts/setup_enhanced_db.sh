#!/bin/bash

# Enhanced Database Setup Script for Spark Memory MCP
# Sets up the optimized database with vector indexes and connection pooling

set -e  # Exit on any error

echo "🚀 Setting up Enhanced Database Integration for Spark Memory MCP"
echo "=============================================================="

# Check for required environment variables
if [ -z "$DATABASE_URL" ]; then
    echo "❌ ERROR: DATABASE_URL environment variable is required"
    echo "Please set DATABASE_URL to your PostgreSQL connection string"
    exit 1
fi

# Ensure we're in the correct directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
cd "$PROJECT_DIR"

echo "📍 Working directory: $PROJECT_DIR"

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ ERROR: Python 3 is required but not installed"
    exit 1
fi

# Check if we have the necessary Python modules
echo "🔍 Checking Python dependencies..."
python3 -c "import asyncpg, numpy" 2>/dev/null || {
    echo "❌ ERROR: Required Python packages not found"
    echo "Please install: pip install asyncpg numpy"
    exit 1
}

# Add src to Python path for imports
export PYTHONPATH="$PROJECT_DIR/src:$PYTHONPATH"

echo "✅ Environment checks passed"

# Run database migration
echo ""
echo "🔧 Running database migration..."
echo "This will:"
echo "  - Create optimized database schema"
echo "  - Install pgvector extension"
echo "  - Create HNSW vector indexes"
echo "  - Apply performance optimizations"
echo ""

# Confirm before proceeding
read -p "Continue with database migration? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Migration cancelled."
    exit 0
fi

# Run the migration
echo "Starting migration..."
python3 scripts/migrate_database.py --confirm

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Database migration completed successfully!"
else
    echo ""
    echo "❌ Database migration failed!"
    exit 1
fi

# Test the enhanced database service
echo ""
echo "🧪 Testing enhanced database integration..."
python3 -c "
import asyncio
import sys
import os
sys.path.append('src')

async def test_enhanced_db():
    try:
        from enhanced_database_service import EnhancedDatabaseService
        
        # Initialize service
        service = EnhancedDatabaseService()
        await service.initialize()
        
        # Test health check
        health = await service.health_check()
        print(f'Database health: {health[\"status\"]}')
        print(f'Response time: {health[\"response_time_ms\"]}ms')
        
        # Test metrics
        metrics = await service.get_metrics()
        print(f'Pool size: {metrics.get(\"pool_size\", \"N/A\")}')
        
        await service.close()
        print('✅ Enhanced database service test passed!')
        return True
        
    except Exception as e:
        print(f'❌ Enhanced database service test failed: {e}')
        return False

result = asyncio.run(test_enhanced_db())
exit(0 if result else 1)
"

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 Enhanced Database Integration Setup Complete!"
    echo ""
    echo "Your Spark Memory MCP server now has:"
    echo "  ✅ Production-ready connection pooling"
    echo "  ✅ Optimized HNSW vector indexes"
    echo "  ✅ Real database operations (no more mocks)"
    echo "  ✅ Performance monitoring and metrics"
    echo ""
    echo "Next steps:"
    echo "  1. Restart your MCP server: ./dev.sh restart"
    echo "  2. Run tests: ./dev.sh test-phase 1"
    echo "  3. Monitor performance: curl http://localhost:8050/tools/get_performance_stats"
    echo ""
else
    echo ""
    echo "❌ Enhanced database service test failed!"
    echo "Please check the logs and configuration."
    exit 1
fi