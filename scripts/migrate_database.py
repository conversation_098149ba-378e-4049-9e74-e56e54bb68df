#!/usr/bin/env python3
"""
Database Migration Script for Enhanced Database Integration

Migrates existing Spark Memory database to use optimized schema with
vector indexes and connection pooling support.
"""

import asyncio
import os
import sys
import logging
from pathlib import Path

# Add src directory to path
sys.path.append(str(Path(__file__).parent.parent / 'src'))

from enhanced_database_service import EnhancedDatabaseService

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DatabaseMigrator:
    """Database migration manager for enhanced integration."""
    
    def __init__(self, database_url: str):
        """Initialize migrator with database URL."""
        self.database_url = database_url
        self.db_service = None
        
    async def initialize(self):
        """Initialize database service."""
        self.db_service = EnhancedDatabaseService(
            database_url=self.database_url,
            min_connections=2,
            max_connections=5
        )
        await self.db_service.initialize()
        logger.info("Database service initialized for migration")
    
    async def close(self):
        """Close database connections."""
        if self.db_service:
            await self.db_service.close()
    
    async def check_current_schema(self):
        """Check current database schema state."""
        logger.info("Checking current database schema...")
        
        # Check for tables
        tables_check = await self.db_service.execute_sql("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('memories', 'conversation_summaries')
        """)
        
        existing_tables = [row['table_name'] for row in tables_check]
        logger.info(f"Existing tables: {existing_tables}")
        
        # Check for pgvector extension
        vector_check = await self.db_service.execute_sql("""
            SELECT EXISTS(SELECT 1 FROM pg_extension WHERE extname = 'vector') as has_vector
        """)
        
        has_vector = vector_check[0]['has_vector'] if vector_check else False
        logger.info(f"pgvector extension installed: {has_vector}")
        
        # Check for indexes
        indexes_check = await self.db_service.execute_sql("""
            SELECT indexname 
            FROM pg_indexes 
            WHERE tablename = 'memories' 
            AND indexname LIKE '%embedding%'
        """)
        
        vector_indexes = [row['indexname'] for row in indexes_check]
        logger.info(f"Vector indexes: {vector_indexes}")
        
        return {
            'tables': existing_tables,
            'has_vector': has_vector,
            'vector_indexes': vector_indexes
        }
    
    async def backup_existing_data(self):
        """Create backup of existing data."""
        logger.info("Creating backup of existing data...")
        
        try:
            # Count existing memories
            count_result = await self.db_service.execute_sql("""
                SELECT COUNT(*) as count FROM memories
            """)
            
            memory_count = count_result[0]['count'] if count_result else 0
            logger.info(f"Found {memory_count} existing memories to preserve")
            
            # Count conversation summaries
            summary_count_result = await self.db_service.execute_sql("""
                SELECT COUNT(*) as count FROM conversation_summaries
            """)
            
            summary_count = summary_count_result[0]['count'] if summary_count_result else 0
            logger.info(f"Found {summary_count} conversation summaries to preserve")
            
            return {
                'memory_count': memory_count,
                'summary_count': summary_count
            }
            
        except Exception as e:
            logger.warning(f"Could not backup existing data: {e}")
            return {'memory_count': 0, 'summary_count': 0}
    
    async def create_optimized_schema(self):
        """Create optimized database schema."""
        logger.info("Creating optimized database schema...")
        
        # Enable pgvector extension
        try:
            await self.db_service.execute_sql("CREATE EXTENSION IF NOT EXISTS vector")
            logger.info("✅ pgvector extension enabled")
        except Exception as e:
            logger.error(f"Failed to enable pgvector extension: {e}")
            raise
        
        # Create memories table with optimal structure
        await self.db_service.execute_sql("""
            CREATE TABLE IF NOT EXISTS memories (
                id SERIAL PRIMARY KEY,
                user_id TEXT NOT NULL,
                content TEXT NOT NULL,
                embedding vector(768) NOT NULL,
                metadata JSONB DEFAULT '{}',
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            )
        """)
        logger.info("✅ memories table created/verified")
        
        # Create conversation_summaries table
        await self.db_service.execute_sql("""
            CREATE TABLE IF NOT EXISTS conversation_summaries (
                user_id TEXT PRIMARY KEY,
                summary TEXT NOT NULL,
                updated_at TIMESTAMP DEFAULT NOW()
            )
        """)
        logger.info("✅ conversation_summaries table created/verified")
    
    async def create_performance_indexes(self):
        """Create performance indexes including HNSW vector index."""
        logger.info("Creating performance indexes...")
        
        try:
            # Create HNSW index for vector similarity (with CONCURRENTLY for production safety)
            await self.db_service.execute_sql("""
                CREATE INDEX IF NOT EXISTS memories_embedding_hnsw_idx 
                ON memories USING hnsw (embedding vector_l2_ops) 
                WITH (m = 16, ef_construction = 64)
            """)
            logger.info("✅ HNSW vector index created")
            
        except Exception as e:
            logger.warning(f"Could not create HNSW index (will use slower sequential search): {e}")
            # Try creating a basic index as fallback
            try:
                await self.db_service.execute_sql("""
                    CREATE INDEX IF NOT EXISTS memories_embedding_basic_idx 
                    ON memories (embedding)
                """)
                logger.info("✅ Basic vector index created as fallback")
            except Exception as e2:
                logger.warning(f"Could not create any vector index: {e2}")
        
        # Create other performance indexes
        indexes_to_create = [
            ("memories_user_id_idx", "CREATE INDEX IF NOT EXISTS memories_user_id_idx ON memories (user_id)"),
            ("memories_created_at_idx", "CREATE INDEX IF NOT EXISTS memories_created_at_idx ON memories (created_at DESC)"),
            ("memories_user_created_idx", "CREATE INDEX IF NOT EXISTS memories_user_created_idx ON memories (user_id, created_at DESC)"),
            ("memories_metadata_gin_idx", "CREATE INDEX IF NOT EXISTS memories_metadata_gin_idx ON memories USING gin (metadata)")
        ]
        
        for index_name, sql in indexes_to_create:
            try:
                await self.db_service.execute_sql(sql)
                logger.info(f"✅ {index_name} created")
            except Exception as e:
                logger.warning(f"Could not create {index_name}: {e}")
    
    async def optimize_database_settings(self):
        """Apply database optimization settings."""
        logger.info("Applying database optimizations...")
        
        optimizations = [
            ("work_mem", "SET work_mem = '256MB'"),
            ("effective_cache_size", "SET effective_cache_size = '4GB'"),
            ("maintenance_work_mem", "SET maintenance_work_mem = '512MB'"),
            ("max_parallel_workers_per_gather", "SET max_parallel_workers_per_gather = 2")
        ]
        
        for setting_name, sql in optimizations:
            try:
                await self.db_service.execute_sql(sql)
                logger.info(f"✅ Applied {setting_name} optimization")
            except Exception as e:
                logger.warning(f"Could not apply {setting_name}: {e}")
    
    async def verify_migration(self):
        """Verify migration was successful."""
        logger.info("Verifying migration results...")
        
        # Check schema
        schema_state = await self.check_current_schema()
        
        # Verify tables exist
        required_tables = ['memories', 'conversation_summaries']
        missing_tables = [table for table in required_tables if table not in schema_state['tables']]
        
        if missing_tables:
            logger.error(f"Migration incomplete - missing tables: {missing_tables}")
            return False
        
        # Verify pgvector extension
        if not schema_state['has_vector']:
            logger.error("Migration incomplete - pgvector extension not installed")
            return False
        
        # Check if we can perform basic operations
        try:
            # Test vector operations
            test_result = await self.db_service.execute_sql("""
                SELECT '[1,2,3]'::vector <-> '[1,2,4]'::vector as distance
            """)
            
            if test_result and len(test_result) > 0:
                distance = test_result[0]['distance']
                logger.info(f"✅ Vector operations working (test distance: {distance})")
            else:
                logger.error("Vector operations test failed")
                return False
                
        except Exception as e:
            logger.error(f"Vector operations test failed: {e}")
            return False
        
        logger.info("🎉 Migration verification completed successfully!")
        return True
    
    async def run_migration(self):
        """Run complete database migration."""
        logger.info("Starting database migration for enhanced integration...")
        
        try:
            # Initialize
            await self.initialize()
            
            # Check current state
            schema_state = await self.check_current_schema()
            
            # Backup existing data
            backup_info = await self.backup_existing_data()
            
            # Create optimized schema
            await self.create_optimized_schema()
            
            # Create performance indexes
            await self.create_performance_indexes()
            
            # Apply optimizations
            await self.optimize_database_settings()
            
            # Verify migration
            success = await self.verify_migration()
            
            if success:
                logger.info("🎉 Database migration completed successfully!")
                logger.info(f"Preserved {backup_info['memory_count']} memories and {backup_info['summary_count']} summaries")
                return True
            else:
                logger.error("❌ Database migration failed verification")
                return False
            
        except Exception as e:
            logger.error(f"❌ Database migration failed: {e}")
            return False
        
        finally:
            await self.close()

async def main():
    """Main migration script entry point."""
    # Check for DATABASE_URL
    database_url = os.getenv('DATABASE_URL')
    if not database_url:
        logger.error("DATABASE_URL environment variable is required")
        logger.info("Please set DATABASE_URL to your PostgreSQL connection string")
        sys.exit(1)
    
    logger.info(f"Starting migration for database: {database_url.split('@')[1] if '@' in database_url else 'localhost'}")
    
    # Confirm with user
    if len(sys.argv) < 2 or sys.argv[1] != '--confirm':
        logger.warning("This will modify your database schema and create indexes.")
        logger.warning("Please backup your database before proceeding.")
        logger.info("To run the migration, use: python migrate_database.py --confirm")
        sys.exit(1)
    
    # Run migration
    migrator = DatabaseMigrator(database_url)
    success = await migrator.run_migration()
    
    if success:
        logger.info("Migration completed. Your database is now optimized for the enhanced integration!")
        sys.exit(0)
    else:
        logger.error("Migration failed. Please check the logs and try again.")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())