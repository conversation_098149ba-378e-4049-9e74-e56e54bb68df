-- Enhanced Rolling Summary Features Migration
-- Phase 2 Feature 3/3: Enhanced Rolling Summary Features
-- 
-- This migration adds advanced capabilities to conversation summaries:
-- 1. Temporal context tracking for evolution over time
-- 2. Topic continuity for cross-session context
-- 3. Summary quality scoring and adaptive length

BEGIN;

-- Add enhanced columns to conversation_summaries table
ALTER TABLE conversation_summaries 
ADD COLUMN IF NOT EXISTS temporal_markers JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS quality_score FLOAT DEFAULT 0.5 CHECK (quality_score >= 0.0 AND quality_score <= 1.0),
ADD COLUMN IF NOT EXISTS quality_factors JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS summary_length INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS cross_session_topics JSONB DEFAULT '[]',
ADD COLUMN IF NOT EXISTS quality_updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS topic_continuity_score FLOAT DEFAULT 0.5 CHECK (topic_continuity_score >= 0.0 AND topic_continuity_score <= 1.0);

-- <PERSON>reate indexes for enhanced summary queries
CREATE INDEX IF NOT EXISTS idx_conversation_summaries_quality_score 
ON conversation_summaries (user_id, quality_score DESC);

CREATE INDEX IF NOT EXISTS idx_conversation_summaries_session_topics 
ON conversation_summaries USING GIN (cross_session_topics);

CREATE INDEX IF NOT EXISTS idx_conversation_summaries_temporal_markers 
ON conversation_summaries USING GIN (temporal_markers);

CREATE INDEX IF NOT EXISTS idx_conversation_summaries_quality_updated 
ON conversation_summaries (quality_updated_at DESC);

-- Function to update summary quality score
CREATE OR REPLACE FUNCTION update_summary_quality(
    p_user_id TEXT,
    p_quality_score FLOAT,
    p_quality_factors JSONB DEFAULT NULL,
    p_reason TEXT DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
    current_factors JSONB;
    updated_factors JSONB;
BEGIN
    -- Validate quality score range
    IF p_quality_score < 0.0 OR p_quality_score > 1.0 THEN
        RAISE EXCEPTION 'Quality score must be between 0.0 and 1.0';
    END IF;
    
    -- Get current factors if updating
    SELECT quality_factors INTO current_factors 
    FROM conversation_summaries 
    WHERE user_id = p_user_id;
    
    -- Merge factors if provided
    IF p_quality_factors IS NOT NULL THEN
        updated_factors = COALESCE(current_factors, '{}'::jsonb) || p_quality_factors;
    ELSE
        updated_factors = current_factors;
    END IF;
    
    -- Add update reason if provided
    IF p_reason IS NOT NULL THEN
        updated_factors = updated_factors || jsonb_build_object('last_update_reason', p_reason);
    END IF;
    
    -- Update the summary quality
    UPDATE conversation_summaries 
    SET 
        quality_score = p_quality_score,
        quality_factors = updated_factors,
        quality_updated_at = NOW()
    WHERE user_id = p_user_id;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- Function to boost summary quality (for good usage patterns)
CREATE OR REPLACE FUNCTION boost_summary_quality(
    p_user_id TEXT,
    p_boost_amount FLOAT DEFAULT 0.05
) RETURNS FLOAT AS $$
DECLARE
    new_quality FLOAT;
BEGIN
    UPDATE conversation_summaries 
    SET 
        quality_score = LEAST(quality_score + p_boost_amount, 1.0),
        quality_updated_at = NOW(),
        quality_factors = quality_factors || jsonb_build_object(
            'last_boost', NOW()::text,
            'boost_amount', p_boost_amount
        )
    WHERE user_id = p_user_id
    RETURNING quality_score INTO new_quality;
    
    RETURN new_quality;
END;
$$ LANGUAGE plpgsql;

-- Function to add temporal markers to summary
CREATE OR REPLACE FUNCTION add_temporal_marker(
    p_user_id TEXT,
    p_marker_type TEXT,
    p_marker_data JSONB
) RETURNS BOOLEAN AS $$
DECLARE
    current_markers JSONB;
    updated_markers JSONB;
BEGIN
    -- Get current temporal markers
    SELECT temporal_markers INTO current_markers 
    FROM conversation_summaries 
    WHERE user_id = p_user_id;
    
    -- Initialize if null
    IF current_markers IS NULL THEN
        current_markers = '{}'::jsonb;
    END IF;
    
    -- Add timestamp to marker data
    p_marker_data = p_marker_data || jsonb_build_object('timestamp', NOW()::text);
    
    -- Update markers
    updated_markers = current_markers || jsonb_build_object(p_marker_type, p_marker_data);
    
    -- Update the summary
    UPDATE conversation_summaries 
    SET 
        temporal_markers = updated_markers,
        updated_at = NOW()
    WHERE user_id = p_user_id;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- Function to update cross-session topics
CREATE OR REPLACE FUNCTION update_cross_session_topics(
    p_user_id TEXT,
    p_topics JSONB
) RETURNS BOOLEAN AS $$
BEGIN
    UPDATE conversation_summaries 
    SET 
        cross_session_topics = p_topics,
        updated_at = NOW()
    WHERE user_id = p_user_id;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- View for high-quality summaries
CREATE OR REPLACE VIEW high_quality_summaries AS
SELECT 
    user_id,
    summary,
    quality_score,
    quality_factors,
    temporal_markers,
    cross_session_topics,
    summary_length,
    updated_at,
    quality_updated_at
FROM conversation_summaries 
WHERE quality_score >= 0.7
ORDER BY quality_score DESC, updated_at DESC;

-- View for summary quality statistics by user
CREATE OR REPLACE VIEW user_summary_stats AS
SELECT 
    user_id,
    COUNT(*) as total_summaries,
    AVG(quality_score) as avg_quality,
    MIN(quality_score) as min_quality,
    MAX(quality_score) as max_quality,
    AVG(summary_length) as avg_length,
    MAX(summary_length) as max_length,
    COUNT(CASE WHEN quality_score >= 0.7 THEN 1 END) as high_quality_count,
    COUNT(CASE WHEN quality_score BETWEEN 0.4 AND 0.69 THEN 1 END) as medium_quality_count,
    COUNT(CASE WHEN quality_score < 0.4 THEN 1 END) as low_quality_count,
    MAX(updated_at) as last_updated,
    jsonb_array_length(COALESCE(cross_session_topics, '[]'::jsonb)) as topic_count
FROM conversation_summaries 
GROUP BY user_id;

-- Update existing summaries with default values
UPDATE conversation_summaries 
SET 
    summary_length = LENGTH(summary),
    quality_score = 0.5,
    quality_factors = jsonb_build_object(
        'initialized', NOW()::text,
        'default_assigned', true
    ),
    temporal_markers = '{}'::jsonb,
    cross_session_topics = '[]'::jsonb,
    quality_updated_at = NOW(),
    topic_continuity_score = 0.5
WHERE temporal_markers IS NULL;

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON conversation_summaries TO postgres;
GRANT SELECT ON high_quality_summaries TO postgres;
GRANT SELECT ON user_summary_stats TO postgres;
GRANT EXECUTE ON FUNCTION update_summary_quality(TEXT, FLOAT, JSONB, TEXT) TO postgres;
GRANT EXECUTE ON FUNCTION boost_summary_quality(TEXT, FLOAT) TO postgres;
GRANT EXECUTE ON FUNCTION add_temporal_marker(TEXT, TEXT, JSONB) TO postgres;
GRANT EXECUTE ON FUNCTION update_cross_session_topics(TEXT, JSONB) TO postgres;

COMMIT;

-- Status check
SELECT 'Enhanced summaries migration completed successfully' as status;
