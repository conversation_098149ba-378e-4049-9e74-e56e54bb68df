#!/bin/bash
# Development helper script

set -e

COMPOSE_FILE="docker-compose.dev.yml"
PROJECT_NAME="spark-memory"

function start() {
    echo "🚀 Starting development server with hot reload..."
    docker-compose -f $COMPOSE_FILE up -d postgres-dev
    sleep 5
    docker-compose -f $COMPOSE_FILE up spark-mcp-dev
}

function start_detached() {
    echo "🚀 Starting all development services..."
    docker-compose -f $COMPOSE_FILE up -d
    echo "✅ Services started. Run './dev.sh logs' to view logs."
}

function test() {
    echo "🧪 Running test suite..."
    # Start test database
    docker-compose -f $COMPOSE_FILE --profile test up -d postgres-test
    sleep 5
    
    # Run tests
    docker-compose -f $COMPOSE_FILE --profile test run --rm test-runner
}

function test_unit() {
    echo "🧪 Running unit tests..."
    docker-compose -f $COMPOSE_FILE --profile test run --rm test-runner pytest tests/unit -v
}

function test_integration() {
    echo "🧪 Running integration tests..."
    docker-compose -f $COMPOSE_FILE --profile test up -d postgres-test
    sleep 5
    docker-compose -f $COMPOSE_FILE --profile test run --rm test-runner pytest tests/integration -v
}

function test_phase() {
    PHASE=$1
    echo "🧪 Running Phase $PHASE tests..."
    docker-compose -f $COMPOSE_FILE --profile test run --rm test-runner pytest tests/phase_$PHASE -v
}

function logs() {
    SERVICE=$1
    if [ -z "$SERVICE" ]; then
        docker-compose -f $COMPOSE_FILE logs -f
    else
        docker-compose -f $COMPOSE_FILE logs -f $SERVICE
    fi
}

function shell() {
    echo "🐚 Entering development shell..."
    docker-compose -f $COMPOSE_FILE exec spark-mcp-dev /bin/bash
}

function db_shell() {
    echo "🗄️ Entering database shell..."
    docker-compose -f $COMPOSE_FILE exec postgres-dev psql -U postgres -d spark_memory
}

function stop() {
    echo "🛑 Stopping all services..."
    docker-compose -f $COMPOSE_FILE down
}

function clean() {
    echo "🧹 Cleaning up everything..."
    docker-compose -f $COMPOSE_FILE down -v --remove-orphans
    docker system prune -f
}

function restart() {
    stop
    start
}

function format() {
    echo "🎨 Formatting code..."
    docker-compose -f $COMPOSE_FILE run --rm spark-mcp-dev black src tests
    docker-compose -f $COMPOSE_FILE run --rm spark-mcp-dev ruff check --fix src tests
}

function lint() {
    echo "🔍 Linting code..."
    docker-compose -f $COMPOSE_FILE run --rm spark-mcp-dev ruff check src tests
    docker-compose -f $COMPOSE_FILE run --rm spark-mcp-dev mypy src
}

# Main command handler
case "$1" in
    start)
        start
        ;;
    start-d)
        start_detached
        ;;
    test)
        test
        ;;
    test-unit)
        test_unit
        ;;
    test-integration)
        test_integration
        ;;
    test-phase)
        test_phase $2
        ;;
    logs)
        logs $2
        ;;
    shell)
        shell
        ;;
    db-shell)
        db_shell
        ;;
    stop)
        stop
        ;;
    clean)
        clean
        ;;
    restart)
        restart
        ;;
    format)
        format
        ;;
    lint)
        lint
        ;;
    *)
        echo "Usage: $0 {start|start-d|test|test-unit|test-integration|test-phase|logs|shell|db-shell|stop|clean|restart|format|lint}"
        echo ""
        echo "Commands:"
        echo "  start             - Start dev server (attached)"
        echo "  start-d           - Start dev server (detached)"
        echo "  test              - Run full test suite"
        echo "  test-unit         - Run unit tests only"
        echo "  test-integration  - Run integration tests only"  
        echo "  test-phase N      - Run phase N tests"
        echo "  logs [service]    - View logs (all or specific service)"
        echo "  shell             - Enter development shell"
        echo "  db-shell          - Enter database shell"
        echo "  stop              - Stop all services"
        echo "  clean             - Clean up everything"
        echo "  restart           - Restart all services"
        echo "  format            - Format code with black/ruff"
        echo "  lint              - Lint code"
        exit 1
        ;;
esac
