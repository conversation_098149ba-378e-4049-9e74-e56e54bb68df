# Spark Memory MCP Development Makefile

.PHONY: help setup start test clean

help:
	@echo "Available commands:"
	@echo "  make setup     - Initial development setup"
	@echo "  make start     - Start development server"
	@echo "  make test      - Run all tests"
	@echo "  make test-p1   - Run Phase 1 tests"
	@echo "  make logs      - View logs"
	@echo "  make shell     - Enter dev shell"
	@echo "  make clean     - Clean everything"

setup:
	./setup-dev.sh

start:
	./dev.sh start

start-d:
	./dev.sh start-d

test:
	./dev.sh test

test-p1:
	./dev.sh test-phase 1

test-p2:
	./dev.sh test-phase 2

test-p3:
	./dev.sh test-phase 3

test-p4:
	./dev.sh test-phase 4

logs:
	./dev.sh logs

shell:
	./dev.sh shell

db:
	./dev.sh db-shell

stop:
	./dev.sh stop

clean:
	./dev.sh clean

format:
	./dev.sh format

lint:
	./dev.sh lint
