"""
Centralized Configuration for Spark Memory MCP Server

This module contains all configuration constants and settings for the memory system,
eliminating hardcoded values throughout the codebase.
"""

import os
from typing import Optional
from dataclasses import dataclass
from pathlib import Path


@dataclass
class MemoryConfig:
    """Configuration for memory extraction and update operations."""
    
    # Memory processing thresholds
    SIMILARITY_THRESHOLD: float = 0.7
    MIN_MEMORY_LENGTH: int = 10
    MAX_MEMORY_LENGTH: int = 500
    MAX_MEMORIES_PER_EXTRACTION: int = 5
    
    # Memory decision thresholds for distance-based operations
    VERY_SIMILAR_THRESHOLD: float = 0.3  # < 0.3: Very similar, likely NOOP or UPDATE
    MODERATELY_SIMILAR_THRESHOLD: float = 0.5  # 0.3-0.5: Moderately similar, consider UPDATE or ADD
    SOMEWHAT_SIMILAR_THRESHOLD: float = 0.7  # 0.5-0.7: Somewhat similar, likely ADD unless contradictory
    UPDATE_OPERATION_THRESHOLD: float = 0.5  # Lower threshold for update operations
    DELETE_OPERATION_THRESHOLD: float = 0.8  # High threshold for deletion operations
    
    # Content processing limits
    MIN_MEANINGFUL_CONTENT_LENGTH: int = 20
    SUMMARY_PREVIEW_LENGTH: int = 100
    CONTENT_PREVIEW_LENGTH: int = 60
    
    # Memory filtering patterns
    GENERIC_RESPONSE_PATTERNS = [
        "i understand", "got it", "makes sense", "okay", "alright",
        "thank you", "thanks", "you're welcome", "no problem"
    ]


@dataclass
class PerformanceConfig:
    """Configuration for performance monitoring and optimization."""
    
    # LOCOMO benchmark targets (in milliseconds)
    LATENCY_P50_TARGET: int = 708    # 0.708s
    LATENCY_P95_TARGET: int = 1440   # 1.440s
    TOKENS_PER_CONVERSATION_TARGET: int = 7000  # ~7K tokens
    ACCURACY_IMPROVEMENT_TARGET: float = 0.26  # 26% improvement
    
    # Operation timeouts (in seconds)
    OPERATION_TIMEOUT: float = 5.0
    EMBEDDING_GENERATION_TIMEOUT: float = 10.0
    DATABASE_QUERY_TIMEOUT: float = 15.0
    HEALTH_CHECK_TIMEOUT: float = 5.0
    
    # Retry configuration
    MAX_RETRIES: int = 5
    RETRY_BASE_DELAY: float = 1.0  # seconds
    EXPONENTIAL_BACKOFF_MULTIPLIER: float = 2.0
    
    # Batch processing
    DEFAULT_BATCH_SIZE: int = 16
    MAX_CONCURRENT_OPERATIONS: int = 10


@dataclass
class DatabaseConfig:
    """Configuration for database operations and connections."""
    
    # Connection settings
    CONNECTION_POOL_SIZE: int = 10
    CONNECTION_POOL_MAX_OVERFLOW: int = 20
    CONNECTION_TIMEOUT: int = 30
    QUERY_TIMEOUT: int = 15
    
    # Vector search configuration
    VECTOR_SEARCH_LIMIT: int = 5
    VECTOR_SEARCH_THRESHOLD: float = 2.0  # L2 distance for pgvector
    
    # Memory management
    RECENT_MEMORIES_LIMIT: int = 5
    DEFAULT_MEMORY_LIMIT: int = 50
    MAX_MEMORY_LIMIT: int = 1000
    
    # Enhanced search features
    ENABLE_ENHANCED_SEARCH: bool = True


@dataclass
class ServerConfig:
    """Configuration for server lifecycle and readiness management."""
    
    # Initialization delays (in seconds)
    MCP_PROTOCOL_READY_DELAY: float = 0.2
    COMPONENT_INITIALIZATION_TIMEOUT: float = 30.0
    
    # Readiness file configuration
    READINESS_FILE_PATH: str = "/tmp/spark_mcp_ready"
    
    # Service configuration
    DEFAULT_HOST: str = "0.0.0.0"
    DEFAULT_PORT: int = 8050
    DEFAULT_TRANSPORT: str = "http"
    
    # Health check intervals
    HEALTH_CHECK_INTERVAL: int = 30  # seconds
    COMPONENT_HEALTH_TIMEOUT: float = 5.0


@dataclass
class CacheConfig:
    """Configuration for LLM response caching."""
    
    # Cache TTL (time to live) in seconds
    EXTRACTION_CACHE_TTL: int = 3600  # 1 hour
    DECISION_CACHE_TTL: int = 1800    # 30 minutes
    SUMMARY_CACHE_TTL: int = 7200     # 2 hours
    
    # Cache size limits
    MAX_CACHE_ENTRIES: int = 1000
    CACHE_CLEANUP_BATCH_SIZE: int = 100
    
    # Cache warming
    COMMON_PATTERNS_CACHE_SIZE: int = 50


@dataclass
class BGEConfig:
    """Configuration for BGE embedding service."""
    
    # Server connection
    DEFAULT_BGE_URL: str = "http://************:8080"
    BGE_HEALTH_CHECK_ENDPOINT: str = "/health"
    BGE_EMBED_ENDPOINT: str = "/embed"
    
    # Embedding configuration
    EMBEDDING_BATCH_SIZE: int = 32
    EMBEDDING_TIMEOUT: float = 30.0
    ADD_INSTRUCTION_DEFAULT: bool = True
    
    # Performance thresholds
    EMBEDDING_DIMENSION: int = 768  # BGE base model dimension
    MAX_TEXT_LENGTH: int = 512  # BGE model max input length


@dataclass
class GraphStoreConfig:
    """Configuration for Neo4j graph store integration."""
    
    # Neo4j connection (already running locally)
    NEO4J_URL: str = os.getenv("NEO4J_URL", "bolt://localhost:7687")
    NEO4J_USERNAME: str = os.getenv("NEO4J_USERNAME", "neo4j")
    NEO4J_PASSWORD: str = os.getenv("NEO4J_PASSWORD", "password")
    
    # Graph configuration
    ENTITY_EXTRACTION_BATCH_SIZE: int = 10
    RELATIONSHIP_INFERENCE_THRESHOLD: float = 0.6
    MAX_GRAPH_TRAVERSAL_DEPTH: int = 3
    
    # Entity types
    SUPPORTED_ENTITY_TYPES = [
        "Person", "Project", "Technology", "Concept", "Organization", "Skill"
    ]
    
    # Relationship types
    SUPPORTED_RELATIONSHIP_TYPES = [
        "COLLABORATES_WITH", "WORKS_ON", "USES", "DEPENDS_ON", "LEADS",
        "MEMBER_OF", "SKILLED_IN", "PREFERS", "LEARNED", "REPLACED_BY"
    ]
    
    # Performance settings
    CONNECTION_TIMEOUT: int = 30
    MAX_TRANSACTION_RETRY_TIME: int = 30
    BATCH_SIZE_LIMIT: int = 100
    
    # Custom prompts for graph operations
    ENTITY_EXTRACTION_PROMPT: str = """
    Extract entities as nodes and relationships as edges from memory content.
    Focus on: people, projects, technologies, concepts, organizations, skills.
    
    Format as JSON:
    {
      "entities": [{"name": "EntityName", "type": "EntityType", "properties": {}}],
      "relationships": [{"from": "EntityA", "to": "EntityB", "type": "RELATIONSHIP_TYPE"}]
    }
    """
    
    RELATIONSHIP_INFERENCE_PROMPT: str = """
    Infer implicit relationships between entities based on context and existing knowledge.
    Consider temporal evolution and dependency chains.
    """


class SparkConfig:
    """
    Main configuration class that aggregates all configuration settings.
    
    Provides centralized access to all configuration with environment variable
    overrides and validation.
    """
    
    def __init__(self):
        """Initialize configuration with environment variable overrides."""
        self.memory = MemoryConfig()
        self.performance = PerformanceConfig()
        self.database = DatabaseConfig()
        self.server = ServerConfig()
        self.cache = CacheConfig()
        self.bge = BGEConfig()
        self.graph_store = GraphStoreConfig()
        
        # Apply environment variable overrides
        self._apply_env_overrides()
        
        # Validate configuration
        self._validate_config()
    
    def _apply_env_overrides(self):
        """Apply environment variable overrides to configuration."""
        
        # Server configuration overrides
        if os.getenv("HOST"):
            self.server.DEFAULT_HOST = os.getenv("HOST")
        if os.getenv("PORT"):
            self.server.DEFAULT_PORT = int(os.getenv("PORT"))
        if os.getenv("TRANSPORT"):
            self.server.DEFAULT_TRANSPORT = os.getenv("TRANSPORT")
        
        # BGE service overrides
        if os.getenv("BGE_SERVER_URL"):
            self.bge.DEFAULT_BGE_URL = os.getenv("BGE_SERVER_URL")
        
        # Performance overrides
        if os.getenv("OPERATION_TIMEOUT"):
            self.performance.OPERATION_TIMEOUT = float(os.getenv("OPERATION_TIMEOUT"))
        if os.getenv("MAX_RETRIES"):
            self.performance.MAX_RETRIES = int(os.getenv("MAX_RETRIES"))
        
        # Memory configuration overrides
        if os.getenv("SIMILARITY_THRESHOLD"):
            self.memory.SIMILARITY_THRESHOLD = float(os.getenv("SIMILARITY_THRESHOLD"))
        
        # Graph store configuration overrides
        if os.getenv("NEO4J_URL"):
            self.graph_store.NEO4J_URL = os.getenv("NEO4J_URL")
        if os.getenv("NEO4J_USERNAME"):
            self.graph_store.NEO4J_USERNAME = os.getenv("NEO4J_USERNAME")
        if os.getenv("NEO4J_PASSWORD"):
            self.graph_store.NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD")
    
    def _validate_config(self):
        """Validate configuration values."""
        
        # Validate thresholds
        if not 0.0 <= self.memory.SIMILARITY_THRESHOLD <= 1.0:
            raise ValueError(f"Similarity threshold must be between 0.0 and 1.0, got {self.memory.SIMILARITY_THRESHOLD}")
        
        if self.memory.MIN_MEMORY_LENGTH >= self.memory.MAX_MEMORY_LENGTH:
            raise ValueError("MIN_MEMORY_LENGTH must be less than MAX_MEMORY_LENGTH")
        
        # Validate timeouts
        if self.performance.OPERATION_TIMEOUT <= 0:
            raise ValueError("OPERATION_TIMEOUT must be positive")
        
        if self.performance.MAX_RETRIES < 0:
            raise ValueError("MAX_RETRIES must be non-negative")
        
        # Validate ports
        if not 1 <= self.server.DEFAULT_PORT <= 65535:
            raise ValueError(f"Port must be between 1 and 65535, got {self.server.DEFAULT_PORT}")
    
    def get_user_id_sources(self) -> dict:
        """Get user ID resolution configuration."""
        return {
            "primary": os.getenv("MCP_USER_ID"),
            "project_prefix": "project:",
            "team_prefix": "team:",
            "default": "user"
        }
    
    def get_database_url(self) -> Optional[str]:
        """Get database URL from environment."""
        return os.getenv('DATABASE_URL')
    
    def get_llm_config(self) -> dict:
        """Get LLM configuration from environment."""
        return {
            "provider": os.getenv("LLM_PROVIDER"),
            "choice": os.getenv("LLM_CHOICE"),
            "api_key": os.getenv("LLM_API_KEY")
        }
    
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return os.getenv("ENVIRONMENT", "development").lower() == "production"
    
    def get_log_level(self) -> str:
        """Get logging level from environment."""
        return os.getenv("LOG_LEVEL", "INFO").upper()


# Global configuration instance
config = SparkConfig()


def get_config() -> SparkConfig:
    """Get the global configuration instance."""
    return config


def reload_config() -> SparkConfig:
    """Reload configuration (useful for testing)."""
    global config
    config = SparkConfig()
    return config


# Convenience functions for commonly used values
def get_similarity_threshold() -> float:
    """Get the memory similarity threshold."""
    return config.memory.SIMILARITY_THRESHOLD


def get_operation_timeout() -> float:
    """Get the default operation timeout."""
    return config.performance.OPERATION_TIMEOUT


def get_bge_server_url() -> str:
    """Get the BGE server URL."""
    return config.bge.DEFAULT_BGE_URL


def get_max_retries() -> int:
    """Get the maximum retry attempts."""
    return config.performance.MAX_RETRIES


def get_neo4j_config() -> dict:
    """Get Neo4j connection configuration."""
    return {
        "uri": config.graph_store.NEO4J_URL,
        "username": config.graph_store.NEO4J_USERNAME,
        "password": config.graph_store.NEO4J_PASSWORD
    }


def get_graph_store_config() -> GraphStoreConfig:
    """Get the graph store configuration."""
    return config.graph_store