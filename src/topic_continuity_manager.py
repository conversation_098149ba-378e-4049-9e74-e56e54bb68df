"""
Topic Continuity Manager for Enhanced Rolling Summaries

Manages topic continuity across sessions, tracking ongoing topics,
projects, and conversation threads for better context preservation.

Phase 2 Feature 3/3: Enhanced Rolling Summary Features
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Set
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class Topic:
    """Represents a conversation topic with continuity tracking."""
    name: str
    keywords: List[str]
    first_mentioned: datetime
    last_mentioned: datetime
    mention_count: int = 1
    importance_score: float = 0.5
    related_topics: List[str] = None
    session_ids: List[str] = None
    status: str = "active"  # active, dormant, resolved

class TopicContinuityManager:
    """
    Manages topic continuity across conversation sessions.
    
    Tracks ongoing topics, identifies topic evolution, and maintains
    context across different conversation sessions for the same user.
    """
    
    def __init__(self, llm_client, db_client):
        """
        Initialize topic continuity manager.
        
        Args:
            llm_client: LLM client for topic analysis
            db_client: Database client for topic storage
        """
        self.llm_client = llm_client
        self.db_client = db_client
        
        # Topic tracking configuration
        self.max_topics_per_user = 20
        self.topic_dormancy_days = 7
        self.importance_threshold = 0.3
        
        # Common topic categories
        self.topic_categories = {
            'work': ['job', 'work', 'project', 'task', 'meeting', 'deadline', 'team'],
            'technology': ['python', 'javascript', 'react', 'api', 'database', 'code', 'programming'],
            'personal': ['family', 'hobby', 'interest', 'goal', 'plan', 'vacation', 'health'],
            'learning': ['course', 'tutorial', 'book', 'skill', 'certification', 'study', 'practice'],
            'tools': ['software', 'app', 'tool', 'platform', 'service', 'system']
        }
        
        logger.info("Initialized TopicContinuityManager")
    
    async def extract_topics_from_content(
        self, 
        content: str, 
        session_id: Optional[str] = None,
        existing_topics: List[Topic] = None
    ) -> List[Topic]:
        """
        Extract topics from conversation content.
        
        Args:
            content: Conversation content to analyze
            session_id: Current session identifier
            existing_topics: Previously identified topics for context
            
        Returns:
            List of identified topics
        """
        try:
            # 1. Pattern-based topic extraction
            pattern_topics = self._extract_pattern_topics(content, session_id)
            
            # 2. LLM-based topic identification
            llm_topics = await self._extract_llm_topics(content, existing_topics)
            
            # 3. Merge and deduplicate topics
            all_topics = pattern_topics + llm_topics
            merged_topics = self._merge_topics(all_topics, existing_topics or [])
            
            # 4. Calculate importance scores
            for topic in merged_topics:
                topic.importance_score = self._calculate_topic_importance(topic, content)
            
            # Filter by importance and limit count
            important_topics = [t for t in merged_topics if t.importance_score >= self.importance_threshold]
            important_topics.sort(key=lambda t: t.importance_score, reverse=True)
            
            logger.debug(f"Extracted {len(important_topics)} important topics from content")
            return important_topics[:self.max_topics_per_user]
            
        except Exception as e:
            logger.error(f"Error extracting topics: {e}")
            return []
    
    def _extract_pattern_topics(self, content: str, session_id: Optional[str]) -> List[Topic]:
        """Extract topics using pattern matching."""
        topics = []
        content_lower = content.lower()
        now = datetime.now()
        
        for category, keywords in self.topic_categories.items():
            for keyword in keywords:
                if keyword in content_lower:
                    # Count occurrences
                    count = content_lower.count(keyword)
                    
                    topic = Topic(
                        name=f"{category}_{keyword}",
                        keywords=[keyword],
                        first_mentioned=now,
                        last_mentioned=now,
                        mention_count=count,
                        importance_score=min(count * 0.1, 1.0),
                        session_ids=[session_id] if session_id else []
                    )
                    topics.append(topic)
        
        return topics
    
    async def _extract_llm_topics(
        self, 
        content: str, 
        existing_topics: Optional[List[Topic]] = None
    ) -> List[Topic]:
        """Use LLM to identify conversation topics."""
        try:
            existing_context = ""
            if existing_topics:
                topic_names = [t.name for t in existing_topics[:5]]
                existing_context = f"\nExisting topics: {', '.join(topic_names)}"
            
            prompt = f"""Identify the main conversation topics from this content.

Content:
{content}{existing_context}

Identify 3-5 main topics being discussed. For each topic, provide:
1. A clear, descriptive name
2. Key keywords related to the topic
3. Importance level (0.0-1.0)

Respond with JSON:
{{
  "topics": [
    {{
      "name": "descriptive_topic_name",
      "keywords": ["keyword1", "keyword2", "keyword3"],
      "importance": 0.0-1.0
    }}
  ]
}}

Focus on substantive topics that would be valuable for future conversation context."""

            response = await self.llm_client.generate(prompt)
            
            try:
                data = json.loads(response)
                topics = []
                now = datetime.now()
                
                for topic_data in data.get('topics', []):
                    topic = Topic(
                        name=topic_data.get('name', '').replace(' ', '_').lower(),
                        keywords=topic_data.get('keywords', []),
                        first_mentioned=now,
                        last_mentioned=now,
                        importance_score=topic_data.get('importance', 0.5),
                        session_ids=[]
                    )
                    topics.append(topic)
                
                return topics
                
            except json.JSONDecodeError:
                logger.warning("Failed to parse LLM topic extraction response")
                return []
                
        except Exception as e:
            logger.warning(f"LLM topic extraction failed: {e}")
            return []
    
    def _merge_topics(self, new_topics: List[Topic], existing_topics: List[Topic]) -> List[Topic]:
        """Merge new topics with existing topics."""
        merged = {}
        
        # Add existing topics
        for topic in existing_topics:
            merged[topic.name] = topic
        
        # Merge new topics
        for new_topic in new_topics:
            if new_topic.name in merged:
                # Update existing topic
                existing = merged[new_topic.name]
                existing.last_mentioned = new_topic.last_mentioned
                existing.mention_count += new_topic.mention_count
                existing.importance_score = max(existing.importance_score, new_topic.importance_score)
                
                # Merge keywords
                existing.keywords = list(set(existing.keywords + new_topic.keywords))
                
                # Merge session IDs
                if new_topic.session_ids:
                    existing.session_ids = list(set((existing.session_ids or []) + new_topic.session_ids))
            else:
                # Add new topic
                merged[new_topic.name] = new_topic
        
        return list(merged.values())
    
    def _calculate_topic_importance(self, topic: Topic, content: str) -> float:
        """Calculate importance score for a topic."""
        score = topic.importance_score
        
        # Boost score based on keyword frequency
        content_lower = content.lower()
        keyword_mentions = sum(content_lower.count(kw.lower()) for kw in topic.keywords)
        frequency_boost = min(keyword_mentions * 0.1, 0.3)
        
        # Boost score based on recency
        days_since_mention = (datetime.now() - topic.last_mentioned).days
        recency_boost = max(0, 0.2 - (days_since_mention * 0.02))
        
        # Boost score based on mention count
        mention_boost = min(topic.mention_count * 0.05, 0.2)
        
        final_score = min(score + frequency_boost + recency_boost + mention_boost, 1.0)
        return final_score
    
    async def get_cross_session_topics(self, user_id: str, limit: int = 10) -> List[Topic]:
        """Get topics that span across multiple sessions."""
        try:
            # This would query the database for stored topics
            # For now, return empty list as placeholder
            logger.debug(f"Retrieved cross-session topics for user {user_id}")
            return []
            
        except Exception as e:
            logger.error(f"Error getting cross-session topics: {e}")
            return []
    
    def update_topic_status(self, topics: List[Topic]) -> List[Topic]:
        """Update topic status based on activity."""
        now = datetime.now()
        
        for topic in topics:
            days_since_mention = (now - topic.last_mentioned).days
            
            if days_since_mention > self.topic_dormancy_days:
                topic.status = "dormant"
            elif topic.importance_score < self.importance_threshold:
                topic.status = "low_priority"
            else:
                topic.status = "active"
        
        return topics
    
    def get_topic_continuity_score(self, topics: List[Topic]) -> float:
        """Calculate overall topic continuity score."""
        if not topics:
            return 0.0
        
        # Score based on active topics, cross-session presence, and importance
        active_topics = [t for t in topics if t.status == "active"]
        cross_session_topics = [t for t in topics if len(t.session_ids or []) > 1]
        
        active_score = len(active_topics) / len(topics)
        cross_session_score = len(cross_session_topics) / len(topics) if topics else 0
        importance_score = sum(t.importance_score for t in topics) / len(topics)
        
        # Weighted combination
        continuity_score = (active_score * 0.4 + cross_session_score * 0.3 + importance_score * 0.3)
        return min(continuity_score, 1.0)
    
    def to_dict(self, topic: Topic) -> Dict[str, Any]:
        """Convert topic to dictionary for storage."""
        return {
            'name': topic.name,
            'keywords': topic.keywords,
            'first_mentioned': topic.first_mentioned.isoformat(),
            'last_mentioned': topic.last_mentioned.isoformat(),
            'mention_count': topic.mention_count,
            'importance_score': topic.importance_score,
            'related_topics': topic.related_topics or [],
            'session_ids': topic.session_ids or [],
            'status': topic.status
        }
    
    def from_dict(self, data: Dict[str, Any]) -> Topic:
        """Create topic from dictionary."""
        return Topic(
            name=data.get('name', ''),
            keywords=data.get('keywords', []),
            first_mentioned=datetime.fromisoformat(data.get('first_mentioned', datetime.now().isoformat())),
            last_mentioned=datetime.fromisoformat(data.get('last_mentioned', datetime.now().isoformat())),
            mention_count=data.get('mention_count', 1),
            importance_score=data.get('importance_score', 0.5),
            related_topics=data.get('related_topics', []),
            session_ids=data.get('session_ids', []),
            status=data.get('status', 'active')
        )
