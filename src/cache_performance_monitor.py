"""
Cache Performance Monitor Extension

Extends the existing performance monitor to track LLM cache performance
and provide detailed insights for optimization.
"""

import time
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from llm_cache_service import get_llm_cache_service

logger = logging.getLogger(__name__)

class CachePerformanceMonitor:
    """
    Specialized performance monitor for LLM cache effectiveness.
    
    Integrates with the main PerformanceMonitor to provide comprehensive
    caching metrics and optimization insights.
    """
    
    def __init__(self):
        """Initialize cache performance monitor."""
        self.cache_service = get_llm_cache_service()
        self.baseline_metrics = None
        self.session_start = time.time()
        
        logger.info("Initialized CachePerformanceMonitor")
    
    async def measure_cache_impact(self, operation_name: str) -> Dict[str, Any]:
        """
        Measure the performance impact of caching for a specific operation.
        
        Args:
            operation_name: Name of the operation being measured
            
        Returns:
            Dictionary with cache impact metrics
        """
        # Get current cache metrics
        cache_metrics = self.cache_service.get_metrics()
        cache_stats = cache_metrics.get("cache_metrics", {})
        
        # Calculate performance improvements
        hit_rate = cache_stats.get("hit_rate", 0.0)
        time_saved_ms = cache_stats.get("total_time_saved_ms", 0.0)
        
        # Estimate latency reduction
        if hit_rate > 0:
            # Assuming average LLM call takes 1-3 seconds
            avg_llm_latency_ms = cache_stats.get("avg_llm_time_ms", 1500)
            estimated_reduction_percent = hit_rate * 70  # Conservative estimate
        else:
            estimated_reduction_percent = 0
        
        return {
            "operation": operation_name,
            "timestamp": datetime.now().isoformat(),
            "cache_performance": {
                "hit_rate_percent": hit_rate * 100,
                "total_time_saved_seconds": time_saved_ms / 1000,
                "estimated_latency_reduction_percent": estimated_reduction_percent,
                "cache_enabled": True
            },
            "cache_stats": cache_stats,
            "recommendations": self._generate_recommendations(cache_stats)
        }
    
    def _generate_recommendations(self, cache_stats: Dict[str, Any]) -> List[str]:
        """Generate optimization recommendations based on cache performance."""
        recommendations = []
        
        hit_rate = cache_stats.get("hit_rate", 0.0)
        total_requests = cache_stats.get("total_requests", 0)
        
        if total_requests < 10:
            recommendations.append("Insufficient data for meaningful analysis. Continue monitoring.")
        elif hit_rate < 0.3:
            recommendations.append("Low cache hit rate. Consider increasing TTL or reviewing cache key strategy.")
        elif hit_rate < 0.5:
            recommendations.append("Moderate cache hit rate. Monitor patterns and consider cache warming.")
        elif hit_rate > 0.8:
            recommendations.append("Excellent cache performance! Consider expanding caching to other operations.")
        
        if cache_stats.get("cache_size", 0) > 8000:
            recommendations.append("Large cache size detected. Consider implementing cache eviction policies.")
        
        return recommendations
    
    async def generate_performance_report(self) -> Dict[str, Any]:
        """
        Generate comprehensive cache performance report.
        
        Returns:
            Detailed performance report with insights and recommendations
        """
        cache_metrics = self.cache_service.get_metrics()
        
        # Calculate session metrics
        session_duration = time.time() - self.session_start
        cache_stats = cache_metrics.get("cache_metrics", {})
        
        # Performance analysis
        hit_rate = cache_stats.get("hit_rate", 0.0)
        time_saved = cache_stats.get("total_time_saved_ms", 0.0) / 1000
        
        # Estimate P95 improvement
        estimated_p95_improvement = self._estimate_p95_improvement(hit_rate)
        
        report = {
            "report_metadata": {
                "generated_at": datetime.now().isoformat(),
                "session_duration_minutes": session_duration / 60,
                "report_type": "cache_performance_analysis"
            },
            "cache_performance_summary": {
                "overall_hit_rate_percent": hit_rate * 100,
                "total_requests": cache_stats.get("total_requests", 0),
                "cache_hits": cache_stats.get("cache_hits", 0),
                "cache_misses": cache_stats.get("cache_misses", 0),
                "time_saved_seconds": time_saved,
                "estimated_p95_improvement_percent": estimated_p95_improvement
            },
            "backend_performance": cache_metrics.get("backend_status", {}),
            "optimization_insights": {
                "target_achievement": {
                    "p95_target_ms": 1000,
                    "estimated_current_p95_reduction_percent": estimated_p95_improvement,
                    "target_likely_achieved": estimated_p95_improvement >= 50
                },
                "recommendations": self._generate_recommendations(cache_stats),
                "next_steps": self._generate_next_steps(hit_rate, cache_stats.get("total_requests", 0))
            }
        }
        
        return report
    
    def _estimate_p95_improvement(self, hit_rate: float) -> float:
        """
        Estimate P95 latency improvement based on cache hit rate.
        
        Args:
            hit_rate: Current cache hit rate (0.0 to 1.0)
            
        Returns:
            Estimated P95 improvement percentage
        """
        if hit_rate <= 0:
            return 0.0
        
        # Conservative estimation model:
        # - Cache hit saves 1-3 seconds on average
        # - P95 improvement scales with hit rate but with diminishing returns
        # - Account for overhead and variance
        
        base_improvement = hit_rate * 70  # Base 70% improvement at 100% hit rate
        diminishing_factor = 0.8 + (0.2 * hit_rate)  # Diminishing returns
        
        estimated_improvement = base_improvement * diminishing_factor
        
        # Cap at realistic maximum
        return min(estimated_improvement, 85.0)
    
    def _generate_next_steps(self, hit_rate: float, total_requests: int) -> List[str]:
        """Generate actionable next steps based on current performance."""
        next_steps = []
        
        if total_requests < 50:
            next_steps.append("Continue using the system to generate more cache data for analysis")
        
        if hit_rate < 0.4:
            next_steps.append("Review and optimize cache key generation strategy")
            next_steps.append("Consider implementing cache warming for common patterns")
        elif hit_rate > 0.6:
            next_steps.append("Expand caching to additional LLM operations")
            next_steps.append("Consider increasing cache TTL for stable patterns")
        
        next_steps.append("Monitor P95 latency metrics to validate improvement")
        next_steps.append("Consider Redis deployment for production persistence")
        
        return next_steps
    
    async def log_cache_event(self, event_type: str, details: Dict[str, Any]):
        """
        Log significant cache events for analysis.
        
        Args:
            event_type: Type of event (hit, miss, eviction, etc.)
            details: Event details
        """
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "event_type": event_type,
            "details": details
        }
        
        # Log based on event type
        if event_type == "cache_hit":
            logger.debug(f"Cache HIT: {details.get('operation', 'unknown')}")
        elif event_type == "cache_miss":
            logger.info(f"Cache MISS: {details.get('operation', 'unknown')}")
        elif event_type == "performance_milestone":
            logger.info(f"Performance milestone: {details}")
        else:
            logger.info(f"Cache event [{event_type}]: {details}")

# Global cache performance monitor instance
_cache_perf_monitor: Optional[CachePerformanceMonitor] = None

def get_cache_performance_monitor() -> CachePerformanceMonitor:
    """Get or create global cache performance monitor instance."""
    global _cache_perf_monitor
    if _cache_perf_monitor is None:
        _cache_perf_monitor = CachePerformanceMonitor()
    return _cache_perf_monitor