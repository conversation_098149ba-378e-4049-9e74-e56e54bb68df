"""
Memory Update Module for Two-Phase Pipeline

Processes candidate memories through LLM-based operation decisions
with vector similarity search for conflict resolution.
"""

import asyncio
import json
import logging
from typing import List, Dict, Any, Optional
from llm_cache_service import get_llm_cache_service, CacheType
from config import get_config
from exceptions import (
    MemoryUpdateError, EmbeddingGenerationError, DatabaseQueryError,
    LLMResponseError, VectorSearchError, handle_known_exception
)

logger = logging.getLogger(__name__)
config = get_config()

class MemoryUpdateModule:
    """
    Update phase of the two-phase memory pipeline.
    
    Uses LLM to decide on ADD/UPDATE/DELETE/NOOP operations based on
    vector similarity with existing memories.
    """
    
    def __init__(self, llm_client, db_client, bge_client, supabase_mcp=None):
        """
        Initialize update module.
        
        Args:
            llm_client: LLM client for operation decisions
            db_client: Database client for memory storage
            bge_client: BGE embedding client
            supabase_mcp: Supabase MCP context for database operations
        """
        self.llm_client = llm_client
        self.db = db_client
        self.bge = bge_client
        self.supabase_mcp = supabase_mcp
        self.cache_service = get_llm_cache_service()
        
        # Similarity threshold for conflict detection (from research)
        self.similarity_threshold = config.memory.SIMILARITY_THRESHOLD
        
        logger.info("Initialized MemoryUpdateModule with LLM caching")
    
    async def process_candidate_memory_with_embedding(
        self, 
        candidate: str, 
        embedding: List[float], 
        user_id: str
    ) -> Dict[str, Any]:
        """
        Process candidate memory with pre-computed embedding for performance.
        
        Args:
            candidate: Candidate memory string to process
            embedding: Pre-computed embedding vector
            user_id: User identifier
            
        Returns:
            Dictionary with operation result and metadata
        """
        try:
            # Find similar existing memories using provided embedding
            similar_memories = await self._find_similar_memories(
                embedding, user_id, self.similarity_threshold
            )
            
            # LLM decides operation based on similarity
            operation_result = await self._decide_operation(candidate, similar_memories)
            
            # Execute the decided operation
            execution_result = await self._execute_operation(
                operation_result, candidate, embedding, user_id
            )
            
            result = {
                'candidate': candidate,
                'operation': operation_result['operation'],
                'reasoning': operation_result['reasoning'],
                'similar_count': len(similar_memories),
                'execution_result': execution_result,
                'user_id': user_id
            }
            
            logger.debug(f"Processed candidate memory with operation: {operation_result['operation']}")
            return result
            
        except (MemoryUpdateError, EmbeddingGenerationError, DatabaseQueryError, VectorSearchError) as e:
            logger.error(f"Known error processing candidate memory: {e}")
            return {
                'candidate': candidate,
                'operation': 'ERROR',
                'reasoning': str(e),
                'similar_count': 0,
                'execution_result': None,
                'user_id': user_id
            }
        except Exception as e:
            logger.error(f"Unexpected error processing candidate memory: {e}")
            # Convert to appropriate exception
            known_exception = handle_known_exception(e)
            return {
                'candidate': candidate,
                'operation': 'ERROR',
                'reasoning': f"Unexpected error: {str(known_exception)}",
                'similar_count': 0,
                'execution_result': None,
                'user_id': user_id
            }
    
    async def process_candidate_memories_batch(
        self, 
        candidates: List[str], 
        user_id: str,
        batch_size: int = None
    ) -> List[Dict[str, Any]]:
        """
        Process multiple candidate memories efficiently using batch operations.
        
        This leverages the enhanced BGE client's batch capabilities for better performance.
        
        Args:
            candidates: List of candidate memory strings to process
            user_id: User identifier
            batch_size: Batch size for embedding generation
            
        Returns:
            List of operation results for each candidate
        """
        if not candidates:
            return []
        
        if batch_size is None:
            batch_size = config.performance.DEFAULT_BATCH_SIZE
        
        try:
            # Generate embeddings for all candidates at once (much faster)
            logger.debug(f"Generating embeddings for {len(candidates)} candidates")
            embeddings = await self.bge.embed_documents(candidates)  # Use documents (no instruction)
            
            # Process each candidate with its pre-computed embedding
            results = []
            for candidate, embedding in zip(candidates, embeddings):
                result = await self.process_candidate_memory_with_embedding(
                    candidate, embedding, user_id
                )
                results.append(result)
            
            logger.info(f"Batch processed {len(candidates)} candidate memories")
            return results
            
        except Exception as e:
            logger.error(f"Error in batch processing: {e}")
            # Fallback to individual processing
            results = []
            for candidate in candidates:
                result = await self.process_candidate_memory(candidate, user_id)
                results.append(result)
            return results
    
    async def process_candidate_memory(self, candidate: str, user_id: str) -> Dict[str, Any]:
        """
        Process candidate memory through update pipeline.
        
        Args:
            candidate: Candidate memory string to process
            user_id: User identifier
            
        Returns:
            Dictionary with operation result and metadata
        """
        try:
            # Generate embedding for similarity search
            embedding = await self.bge.embed_single(candidate, add_instruction=False)
            
            # Use the optimized method with pre-computed embedding
            return await self.process_candidate_memory_with_embedding(
                candidate, embedding, user_id
            )
            
        except Exception as e:
            logger.error(f"Error processing candidate memory: {e}")
            return {
                'candidate': candidate,
                'operation': 'ERROR', 
                'reasoning': str(e),
                'similar_count': 0,
                'execution_result': None,
                'user_id': user_id
            }
    
    async def _find_similar_memories(
        self, 
        embedding: List[float], 
        user_id: str, 
        threshold: float
    ) -> List[Dict[str, Any]]:
        """
        Find similar existing memories using optimized vector similarity search.
        
        Args:
            embedding: Query embedding vector
            user_id: User identifier
            threshold: Similarity threshold
            
        Returns:
            List of similar memories with distance scores
        """
        try:
            # Use database client's optimized similarity search
            results = await self.db.similarity_search(embedding, user_id, threshold, 5)
            return results
            
        except Exception as e:
            logger.error(f"Error finding similar memories: {e}")
            return []
    
    async def _decide_operation(
        self, 
        candidate: str, 
        similar_memories: List[Dict[str, Any]]
    ) -> Dict[str, str]:
        """
        LLM decides ADD/UPDATE/DELETE/NOOP operation.
        
        Args:
            candidate: Candidate memory string
            similar_memories: List of similar existing memories
            
        Returns:
            Dictionary with operation decision and reasoning
        """
        try:
            # Build decision prompt
            prompt = self._build_decision_prompt(candidate, similar_memories)
            
            # Create cache context for operation decision (ensure JSON serializable)
            cache_context = {
                "candidate_length": len(candidate),
                "similar_count": len(similar_memories),
                "similarity_scores": [
                    float(mem.get('distance', 1.0)) if not asyncio.iscoroutine(mem.get('distance', 1.0)) 
                    else 1.0 for mem in similar_memories[:config.database.RECENT_MEMORIES_LIMIT]
                ]
            }
            
            # Check cache first for performance optimization
            cached_response = await self.cache_service.get_cached_response(
                CacheType.DECISION, prompt, cache_context
            )
            
            if cached_response:
                logger.debug("Using cached decision response")
                response = cached_response
            else:
                # Generate decision using LLM
                logger.debug("Generating new decision response")
                response = await self.llm_client.generate(prompt)
                
                # Cache the response for future use
                if response and response.strip():
                    await self.cache_service.cache_response(
                        CacheType.DECISION, prompt, response, cache_context, 
                        custom_ttl_hours=config.cache.DECISION_CACHE_TTL // 3600
                    )
            
            # Parse response
            try:
                result = json.loads(response)
                operation = result.get('operation', 'ADD').upper()
                reasoning = result.get('reasoning', 'No reasoning provided')
            except json.JSONDecodeError:
                # Fallback parsing
                response_upper = response.strip().upper()
                if 'UPDATE' in response_upper:
                    operation = 'UPDATE'
                elif 'DELETE' in response_upper:
                    operation = 'DELETE'
                elif 'NOOP' in response_upper:
                    operation = 'NOOP'
                else:
                    operation = 'ADD'
                reasoning = response.strip()
            
            # Validate operation
            if operation not in ['ADD', 'UPDATE', 'DELETE', 'NOOP']:
                logger.warning(f"Invalid operation '{operation}', defaulting to ADD")
                operation = 'ADD'
                reasoning = f"Invalid operation, defaulting to ADD. Original: {reasoning}"
            
            return {
                'operation': operation,
                'reasoning': reasoning
            }
            
        except Exception as e:
            logger.error(f"Error in operation decision: {e}")
            return {
                'operation': 'ADD',
                'reasoning': f"Error in decision process: {e}"
            }
    
    def _build_decision_prompt(
        self, 
        candidate: str, 
        similar_memories: List[Dict[str, Any]]
    ) -> str:
        """
        Build prompt for LLM operation decision.
        
        Args:
            candidate: Candidate memory string
            similar_memories: List of similar existing memories
            
        Returns:
            Formatted decision prompt
        """
        similar_content = []
        for mem in similar_memories[:3]:  # Limit to top 3 for context
            distance = mem.get('distance', 0)
            content = mem.get('content', '')
            similar_content.append(f"Distance {distance:.3f}: {content}")
        
        similar_text = "\\n".join(similar_content) if similar_content else "No similar memories found"
        
        prompt = f"""
Analyze this candidate memory and decide what operation to perform based on existing similar memories.

Candidate Memory: {candidate}

Similar Existing Memories:
{similar_text}

Operations:
- ADD: Create new memory (no conflicts, new information, or complementary to existing)
- UPDATE: Enhance existing memory (similar memory exists that can be improved/expanded)
- DELETE: Remove contradicted memory (candidate contradicts or makes existing memory obsolete)
- NOOP: No action needed (duplicate, irrelevant, or already well-covered)

Decision Criteria:
- If distance < {config.memory.VERY_SIMILAR_THRESHOLD}: Very similar, likely NOOP or UPDATE
- If distance {config.memory.VERY_SIMILAR_THRESHOLD}-{config.memory.MODERATELY_SIMILAR_THRESHOLD}: Moderately similar, consider UPDATE or ADD
- If distance {config.memory.MODERATELY_SIMILAR_THRESHOLD}-{config.memory.SOMEWHAT_SIMILAR_THRESHOLD}: Somewhat similar, likely ADD unless contradictory
- If no similar memories: ADD

Respond with JSON format:
{{"operation": "ADD|UPDATE|DELETE|NOOP", "reasoning": "Brief explanation of decision"}}
        """.strip()
        
        return prompt
    
    async def _execute_operation(
        self, 
        operation_result: Dict[str, str], 
        candidate: str, 
        embedding: List[float], 
        user_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Execute the decided operation.
        
        Args:
            operation_result: Operation decision result
            candidate: Candidate memory string
            embedding: Memory embedding
            user_id: User identifier
            
        Returns:
            Execution result or None for NOOP
        """
        operation = operation_result['operation']
        
        try:
            if operation == 'ADD':
                return await self._execute_add(candidate, embedding, user_id)
                
            elif operation == 'UPDATE':
                return await self._execute_update(candidate, embedding, user_id)
                
            elif operation == 'DELETE':
                return await self._execute_delete(candidate, user_id)
                
            elif operation == 'NOOP':
                logger.debug("NOOP operation - no action taken")
                return None
                
        except Exception as e:
            logger.error(f"Error executing {operation} operation: {e}")
            return {'error': str(e)}
    
    async def _execute_add(self, content: str, embedding: List[float], user_id: str) -> Dict[str, Any]:
        """Execute ADD operation."""
        try:
            # Store new memory via real Supabase MCP
            if self.supabase_mcp:
                memory_id = await self.supabase_mcp.store_memory(user_id, content, embedding)
            else:
                memory_id = await self.db.store_memory(user_id, content, embedding)
            
            logger.info(f"Added new memory {memory_id} for user {user_id}")
            return {
                'action': 'added',
                'memory_id': memory_id,
                'content': content
            }
            
        except Exception as e:
            logger.error(f"Error in ADD execution: {e}")
            raise
    
    async def _execute_update(self, content: str, embedding: List[float], user_id: str) -> Dict[str, Any]:
        """Execute UPDATE operation."""
        try:
            # Find the most similar memory to update
            similar_memories = await self._find_similar_memories(
                embedding, user_id, config.memory.UPDATE_OPERATION_THRESHOLD  # Lower threshold for update
            )
            
            if not similar_memories:
                # No memory to update, fall back to ADD
                return await self._execute_add(content, embedding, user_id)
            
            # Update the most similar memory
            target_memory = similar_memories[0]
            memory_id = target_memory['id']
            
            # Merge content (simple approach - could be enhanced with LLM)
            updated_content = f"{target_memory['content']}. Updated: {content}"
            
            # Update via Supabase MCP
            sql = """
                UPDATE memories 
                SET content = $2, embedding = $3, updated_at = NOW()
                WHERE id = $1
            """
            
            # Execute via Supabase MCP (placeholder for now)
            
            logger.info(f"Updated memory {memory_id} for user {user_id}")
            return {
                'action': 'updated',
                'memory_id': memory_id,
                'old_content': target_memory['content'],
                'new_content': updated_content
            }
            
        except Exception as e:
            logger.error(f"Error in UPDATE execution: {e}")
            raise
    
    async def _execute_delete(self, content: str, user_id: str) -> Dict[str, Any]:
        """Execute DELETE operation."""
        try:
            # Generate embedding to find memory to delete
            embedding = await self.bge.embed_single(content, add_instruction=False)
            
            # Find similar memories
            similar_memories = await self._find_similar_memories(
                embedding, user_id, config.memory.DELETE_OPERATION_THRESHOLD  # High threshold for deletion
            )
            
            if not similar_memories:
                logger.warning("No similar memory found for deletion")
                return {'action': 'no_deletion', 'reason': 'No similar memory found'}
            
            # Delete the most similar memory
            target_memory = similar_memories[0]
            memory_id = target_memory['id']
            
            # Delete via Supabase MCP
            sql = "DELETE FROM memories WHERE id = $1"
            
            # Execute via Supabase MCP (placeholder for now)
            
            logger.info(f"Deleted memory {memory_id} for user {user_id}")
            return {
                'action': 'deleted',
                'memory_id': memory_id,
                'deleted_content': target_memory['content']
            }
            
        except Exception as e:
            logger.error(f"Error in DELETE execution: {e}")
            raise
    
    # History-Based Learning Methods (Phase 3)
    
    async def analyze_memory_history(self, memory_id: str, user_id: str) -> Dict[str, Any]:
        """
        Analyze memory evolution patterns for enhanced decision making.
        
        Args:
            memory_id: Memory identifier
            user_id: User identifier
            
        Returns:
            Dictionary with evolution analysis and patterns
        """
        try:
            # Query memory history using database function
            sql = """
                SELECT * FROM analyze_memory_evolution($1::INTEGER, 10)
            """
            
            history_records = await self.db._execute_sql(sql, [int(memory_id)])
            
            if not history_records:
                return {
                    'total_updates': 0,
                    'update_frequency': 0.0,
                    'evolution_detected': False,
                    'relationship_changes': False,
                    'conflict_indicators': [],
                    'stability_score': 1.0,
                    'evolution_pattern': 'stable'
                }
            
            # Analyze patterns from history
            total_updates = len([r for r in history_records if r.get('operation') == 'UPDATE'])
            evolution_count = sum(1 for r in history_records if r.get('evolution_detected', False))
            relationship_count = sum(1 for r in history_records if r.get('relationship_detected', False))
            
            # Calculate update frequency (updates per day)
            if history_records:
                days_span = max(1, history_records[0].get('days_since_creation', 1))
                update_frequency = total_updates / days_span
            else:
                update_frequency = 0.0
            
            # Calculate average similarity between versions
            similarity_scores = [r.get('similarity_score') for r in history_records if r.get('similarity_score') is not None]
            avg_similarity = sum(similarity_scores) / len(similarity_scores) if similarity_scores else 1.0
            
            # Detect evolution patterns
            evolution_pattern = self._detect_evolution_pattern(history_records)
            
            # Calculate stability score (lower means more volatile)
            stability_score = avg_similarity * (1.0 - min(0.5, update_frequency))
            
            # Detect conflict indicators
            conflict_indicators = []
            if update_frequency > 0.5:  # More than one update every 2 days
                conflict_indicators.append('high_volatility')
            if evolution_count > total_updates * 0.6:
                conflict_indicators.append('frequent_evolution')
            if avg_similarity < 0.7:
                conflict_indicators.append('low_consistency')
            
            return {
                'total_updates': total_updates,
                'update_frequency': update_frequency,
                'evolution_detected': evolution_count > 0,
                'relationship_changes': relationship_count > 0,
                'conflict_indicators': conflict_indicators,
                'stability_score': stability_score,
                'evolution_pattern': evolution_pattern,
                'avg_similarity': avg_similarity,
                'history_depth': len(history_records)
            }
            
        except Exception as e:
            logger.error(f"Failed to analyze memory history: {e}")
            return {
                'total_updates': 0,
                'update_frequency': 0.0,
                'evolution_detected': False,
                'relationship_changes': False,
                'conflict_indicators': ['analysis_failed'],
                'stability_score': 0.5,  # Neutral score when analysis fails
                'evolution_pattern': 'unknown'
            }
    
    def _detect_evolution_pattern(self, history_records: List[Dict[str, Any]]) -> str:
        """Detect evolution patterns from history records."""
        if not history_records or len(history_records) < 2:
            return 'stable'
        
        # Sort by version to analyze chronologically
        sorted_records = sorted(history_records, key=lambda x: x.get('version', 0))
        
        evolution_indicators = [r.get('evolution_detected', False) for r in sorted_records]
        update_operations = [r for r in sorted_records if r.get('operation') == 'UPDATE']
        
        if not update_operations:
            return 'stable'
        
        # Pattern detection
        if len(update_operations) == 1:
            return 'single_update'
        elif len(update_operations) >= 3 and all(evolution_indicators[-3:]):
            return 'rapid_evolution'
        elif sum(evolution_indicators) > len(evolution_indicators) * 0.6:
            return 'evolutionary'
        elif len(update_operations) > 3:
            return 'iterative_refinement'
        else:
            return 'moderate_change'
    
    async def _decide_operation_with_history(
        self, 
        candidate: str, 
        similar_memories: List[Dict[str, Any]],
        user_id: str
    ) -> Dict[str, str]:
        """
        Enhanced operation decision using history context and evolution patterns.
        
        Args:
            candidate: Candidate memory string
            similar_memories: List of similar existing memories
            user_id: User identifier
            
        Returns:
            Dictionary with operation decision and reasoning
        """
        try:
            # Get history analysis for similar memories
            history_context = []
            for mem in similar_memories[:3]:
                history = await self.analyze_memory_history(str(mem['id']), user_id)
                history_context.append({
                    'memory_id': mem['id'],
                    'content': mem['content'],
                    'distance': mem.get('distance', 1.0),
                    'update_frequency': history['update_frequency'],
                    'evolution_pattern': history['evolution_pattern'],
                    'stability_score': history['stability_score'],
                    'conflict_indicators': history['conflict_indicators']
                })
            
            # Enhanced decision prompt with history context
            prompt = self._build_history_aware_decision_prompt(candidate, history_context)
            
            # Create cache context for history-aware decisions
            cache_context = {
                "candidate_length": len(candidate),
                "similar_count": len(similar_memories),
                "similarity_scores": [mem.get('distance', 1.0) for mem in similar_memories[:3]],
                "history_patterns": [ctx['evolution_pattern'] for ctx in history_context],
                "stability_scores": [ctx['stability_score'] for ctx in history_context]
            }
            
            # Check cache first
            cached_response = await self.cache_service.get_cached_response(
                CacheType.DECISION, prompt, cache_context
            )
            
            if cached_response:
                logger.debug("Using cached history-aware decision response")
                response = cached_response
            else:
                # Generate decision using LLM
                logger.debug("Generating new history-aware decision response")
                response = await self.llm_client.generate(prompt)
                
                # Cache the response
                if response and response.strip():
                    await self.cache_service.cache_response(
                        CacheType.DECISION, prompt, response, cache_context,
                        custom_ttl_hours=config.cache.DECISION_CACHE_TTL // 3600
                    )
            
            # Parse response with enhanced fallback
            return self._parse_history_aware_decision(response, history_context)
            
        except Exception as e:
            logger.error(f"Error in history-aware operation decision: {e}")
            # Fallback to basic decision without history
            return await self._decide_operation(candidate, similar_memories)
    
    def _build_history_aware_decision_prompt(
        self, 
        candidate: str, 
        history_context: List[Dict[str, Any]]
    ) -> str:
        """Build enhanced decision prompt with history context."""
        
        # Build history context summary
        history_summary = []
        for ctx in history_context:
            summary = f"""Memory ID {ctx['memory_id']} (Distance: {ctx['distance']:.3f}):
Content: {ctx['content'][:100]}...
Evolution Pattern: {ctx['evolution_pattern']}
Update Frequency: {ctx['update_frequency']:.2f} updates/day
Stability Score: {ctx['stability_score']:.2f}
Conflict Indicators: {', '.join(ctx['conflict_indicators']) if ctx['conflict_indicators'] else 'None'}"""
            history_summary.append(summary)
        
        history_text = "\n\n".join(history_summary) if history_summary else "No similar memories with history found"
        
        prompt = f"""
Analyze this candidate memory and decide what operation to perform based on existing similar memories and their evolution history.

Candidate Memory: {candidate}

Similar Memories with Evolution History:
{history_text}

Enhanced Decision Criteria:
- Consider evolution patterns: 'rapid_evolution' suggests frequent changes, 'stable' suggests consistency
- High update frequency (>0.3/day) indicates volatile information that may need careful handling
- Low stability scores (<0.7) suggest the memory area is still evolving
- Conflict indicators provide warnings about potential issues

Operation Guidelines with History Context:
- ADD: Use when no similar memories exist, or when similar memories are stable and candidate adds new information
- UPDATE: Use when similar memory exists with evolution pattern 'evolutionary' or 'iterative_refinement', and candidate improves/corrects it
- DELETE: Use when candidate contradicts a stable memory (stability_score > 0.8) or when similar memory has 'rapid_evolution' pattern with contradictions
- NOOP: Use when candidate is redundant with stable memories (stability_score > 0.8 and low update_frequency)

History-Based Reasoning:
- Memories with 'stable' pattern and high stability score should be preserved
- Memories with 'rapid_evolution' pattern may benefit from consolidation
- High conflict indicators suggest careful UPDATE rather than ADD
- Recent evolution activity (high update_frequency) indicates active knowledge area

Respond with JSON format:
{{"operation": "ADD|UPDATE|DELETE|NOOP", "reasoning": "Brief explanation including history context", "confidence": 0.0-1.0}}
        """.strip()
        
        return prompt
    
    def _parse_history_aware_decision(
        self, 
        response: str, 
        history_context: List[Dict[str, Any]]
    ) -> Dict[str, str]:
        """Parse LLM response for history-aware decisions with enhanced fallback."""
        try:
            result = json.loads(response.strip())
            operation = result.get('operation', 'ADD').upper()
            reasoning = result.get('reasoning', 'No reasoning provided')
            confidence = result.get('confidence', 0.5)
            
            # Validate operation
            if operation not in ['ADD', 'UPDATE', 'DELETE', 'NOOP']:
                logger.warning(f"Invalid operation '{operation}', defaulting to ADD")
                operation = 'ADD'
                reasoning = f"Invalid operation, defaulting to ADD. Original: {reasoning}"
            
            # Enhance reasoning with history insights
            if history_context:
                patterns = [ctx['evolution_pattern'] for ctx in history_context]
                avg_stability = sum(ctx['stability_score'] for ctx in history_context) / len(history_context)
                
                reasoning += f" [History: patterns={patterns}, avg_stability={avg_stability:.2f}]"
            
            return {
                'operation': operation,
                'reasoning': reasoning,
                'confidence': confidence,
                'history_enhanced': True
            }
            
        except json.JSONDecodeError:
            # Fallback parsing with history hints
            response_upper = response.strip().upper()
            if 'UPDATE' in response_upper:
                operation = 'UPDATE'
            elif 'DELETE' in response_upper:
                operation = 'DELETE'
            elif 'NOOP' in response_upper:
                operation = 'NOOP'
            else:
                operation = 'ADD'
            
            reasoning = response.strip()
            if history_context:
                reasoning += f" [History context available for {len(history_context)} similar memories]"
            
            return {
                'operation': operation,
                'reasoning': reasoning,
                'confidence': 0.4,  # Lower confidence for fallback parsing
                'history_enhanced': False
            }
    
    async def process_candidate_with_history_analysis(
        self, 
        candidate: str, 
        user_id: str
    ) -> Dict[str, Any]:
        """
        Process candidate memory with comprehensive history analysis.
        
        Args:
            candidate: Candidate memory string
            user_id: User identifier
            
        Returns:
            Enhanced processing result with history context
        """
        try:
            # Generate embedding for similarity search
            embedding = await self.bge.embed_single(candidate, add_instruction=False)
            
            # Find similar existing memories
            similar_memories = await self._find_similar_memories(
                embedding, user_id, self.similarity_threshold
            )
            
            # Enhanced decision with history analysis
            operation_result = await self._decide_operation_with_history(
                candidate, similar_memories, user_id
            )
            
            # Execute the operation
            execution_result = await self._execute_operation(
                operation_result, candidate, embedding, user_id
            )
            
            # Return enhanced result
            result = {
                'candidate': candidate,
                'operation': operation_result['operation'],
                'reasoning': operation_result['reasoning'],
                'confidence': operation_result.get('confidence', 0.5),
                'history_enhanced': operation_result.get('history_enhanced', False),
                'similar_count': len(similar_memories),
                'execution_result': execution_result,
                'user_id': user_id,
                'processing_metadata': {
                    'similarity_threshold': self.similarity_threshold,
                    'history_analysis_enabled': True,
                    'embedding_dimension': len(embedding)
                }
            }
            
            logger.debug(f"Processed candidate with history analysis: {operation_result['operation']}")
            return result
            
        except Exception as e:
            logger.error(f"Error in history-enhanced processing: {e}")
            # Fallback to basic processing
            return await self.process_candidate_memory(candidate, user_id)