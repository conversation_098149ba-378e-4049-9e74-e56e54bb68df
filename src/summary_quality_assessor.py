"""
Summary Quality Assessor for Enhanced Rolling Summaries

Assesses and scores the quality of conversation summaries,
providing quality metrics and adaptive length recommendations.

Phase 2 Feature 3/3: Enhanced Rolling Summary Features
"""

import json
import logging
import re
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class QualityFactors:
    """Quality assessment factors for summaries."""
    completeness_score: float = 0.5
    coherence_score: float = 0.5
    relevance_score: float = 0.5
    conciseness_score: float = 0.5
    temporal_awareness_score: float = 0.5
    entity_coverage_score: float = 0.5
    actionability_score: float = 0.5

class SummaryQualityAssessor:
    """
    Assesses the quality of conversation summaries.
    
    Provides comprehensive quality scoring based on multiple factors
    and recommends adaptive summary lengths based on conversation complexity.
    """
    
    def __init__(self, llm_client):
        """
        Initialize summary quality assessor.
        
        Args:
            llm_client: LLM client for quality analysis
        """
        self.llm_client = llm_client
        
        # Quality assessment configuration
        self.min_summary_length = 100
        self.max_summary_length = 3000
        self.target_summary_length = 1500
        
        # Quality indicators
        self.quality_indicators = {
            'completeness': [
                'covers main topics', 'includes key information', 'comprehensive',
                'addresses all points', 'thorough coverage'
            ],
            'coherence': [
                'logical flow', 'well-structured', 'clear connections',
                'organized', 'coherent narrative'
            ],
            'relevance': [
                'relevant information', 'important details', 'significant points',
                'valuable context', 'pertinent facts'
            ],
            'conciseness': [
                'concise', 'brief', 'to the point', 'efficient',
                'no redundancy', 'well-summarized'
            ]
        }
        
        # Negative quality indicators
        self.quality_issues = {
            'incompleteness': [
                'missing information', 'incomplete', 'gaps in coverage',
                'overlooked details', 'insufficient detail'
            ],
            'incoherence': [
                'confusing', 'disorganized', 'unclear connections',
                'jumbled', 'poor structure'
            ],
            'irrelevance': [
                'irrelevant', 'off-topic', 'unnecessary details',
                'not important', 'tangential'
            ],
            'verbosity': [
                'too long', 'verbose', 'repetitive', 'redundant',
                'unnecessarily detailed'
            ]
        }
        
        logger.info("Initialized SummaryQualityAssessor")
    
    async def assess_summary_quality(
        self, 
        summary: str, 
        original_content: str = "",
        context: Dict[str, Any] = None
    ) -> Tuple[float, QualityFactors]:
        """
        Assess the quality of a conversation summary.
        
        Args:
            summary: The summary to assess
            original_content: Original conversation content (for comparison)
            context: Additional context for assessment
            
        Returns:
            Tuple of (overall_quality_score, quality_factors)
        """
        try:
            # 1. Heuristic-based assessment
            heuristic_factors = self._assess_heuristic_quality(summary, original_content)
            
            # 2. LLM-based assessment
            llm_factors = await self._assess_llm_quality(summary, original_content)
            
            # 3. Combine assessments
            combined_factors = self._combine_quality_factors(heuristic_factors, llm_factors)
            
            # 4. Calculate overall score
            overall_score = self._calculate_overall_quality_score(combined_factors)
            
            logger.debug(f"Summary quality assessed: {overall_score:.3f}")
            return overall_score, combined_factors
            
        except Exception as e:
            logger.error(f"Error assessing summary quality: {e}")
            return 0.5, QualityFactors()
    
    def _assess_heuristic_quality(self, summary: str, original_content: str = "") -> QualityFactors:
        """Assess quality using heuristic rules."""
        factors = QualityFactors()
        
        if not summary:
            return factors
        
        summary_lower = summary.lower()
        summary_length = len(summary)
        
        # Completeness score (based on length and content coverage)
        if original_content:
            content_ratio = min(summary_length / len(original_content), 1.0)
            factors.completeness_score = min(content_ratio * 2, 1.0)  # Boost for good compression
        else:
            # Length-based completeness
            if summary_length < 50:
                factors.completeness_score = 0.2
            elif summary_length < 200:
                factors.completeness_score = 0.6
            else:
                factors.completeness_score = 0.8
        
        # Coherence score (based on structure indicators)
        coherence_indicators = sum(1 for indicator in self.quality_indicators['coherence'] 
                                 if indicator in summary_lower)
        factors.coherence_score = min(coherence_indicators * 0.2 + 0.3, 1.0)
        
        # Conciseness score (based on length efficiency)
        if summary_length <= self.target_summary_length:
            factors.conciseness_score = 0.8
        else:
            excess_ratio = (summary_length - self.target_summary_length) / self.target_summary_length
            factors.conciseness_score = max(0.8 - excess_ratio, 0.2)
        
        # Temporal awareness (look for temporal markers)
        temporal_markers = ['recently', 'now', 'currently', 'changed', 'updated', 'evolved']
        temporal_count = sum(1 for marker in temporal_markers if marker in summary_lower)
        factors.temporal_awareness_score = min(temporal_count * 0.15 + 0.3, 1.0)
        
        # Entity coverage (count proper nouns and technical terms)
        entities = re.findall(r'\b[A-Z][a-z]+\b', summary)
        tech_terms = re.findall(r'\b(Python|JavaScript|React|API|database|code)\b', summary, re.IGNORECASE)
        entity_score = min((len(entities) + len(tech_terms)) * 0.1, 1.0)
        factors.entity_coverage_score = max(entity_score, 0.3)
        
        # Actionability (look for actionable information)
        actionable_terms = ['goal', 'plan', 'task', 'project', 'working on', 'need to', 'will']
        actionable_count = sum(1 for term in actionable_terms if term in summary_lower)
        factors.actionability_score = min(actionable_count * 0.15 + 0.3, 1.0)
        
        return factors
    
    async def _assess_llm_quality(self, summary: str, original_content: str = "") -> QualityFactors:
        """Use LLM to assess summary quality."""
        try:
            content_context = ""
            if original_content:
                content_context = f"\nOriginal Content (first 500 chars):\n{original_content[:500]}..."
            
            prompt = f"""Assess the quality of this conversation summary across multiple dimensions.

Summary to Assess:
{summary}{content_context}

Rate each dimension from 0.0 to 1.0:

1. Completeness: Does it cover all important information?
2. Coherence: Is it well-structured and logically organized?
3. Relevance: Does it focus on important, relevant information?
4. Conciseness: Is it appropriately brief without losing key details?
5. Temporal Awareness: Does it capture changes and evolution over time?
6. Entity Coverage: Does it mention key people, projects, technologies?
7. Actionability: Does it include actionable information for future conversations?

Respond with JSON:
{{
  "completeness_score": 0.0-1.0,
  "coherence_score": 0.0-1.0,
  "relevance_score": 0.0-1.0,
  "conciseness_score": 0.0-1.0,
  "temporal_awareness_score": 0.0-1.0,
  "entity_coverage_score": 0.0-1.0,
  "actionability_score": 0.0-1.0,
  "reasoning": "brief explanation of the assessment"
}}"""

            response = await self.llm_client.generate(prompt)
            
            try:
                data = json.loads(response)
                
                factors = QualityFactors(
                    completeness_score=data.get('completeness_score', 0.5),
                    coherence_score=data.get('coherence_score', 0.5),
                    relevance_score=data.get('relevance_score', 0.5),
                    conciseness_score=data.get('conciseness_score', 0.5),
                    temporal_awareness_score=data.get('temporal_awareness_score', 0.5),
                    entity_coverage_score=data.get('entity_coverage_score', 0.5),
                    actionability_score=data.get('actionability_score', 0.5)
                )
                
                return factors
                
            except json.JSONDecodeError:
                logger.warning("Failed to parse LLM quality assessment response")
                return QualityFactors()
                
        except Exception as e:
            logger.warning(f"LLM quality assessment failed: {e}")
            return QualityFactors()
    
    def _combine_quality_factors(self, heuristic: QualityFactors, llm: QualityFactors) -> QualityFactors:
        """Combine heuristic and LLM quality assessments."""
        # Weight: 60% LLM, 40% heuristic (LLM is more nuanced)
        return QualityFactors(
            completeness_score=llm.completeness_score * 0.6 + heuristic.completeness_score * 0.4,
            coherence_score=llm.coherence_score * 0.6 + heuristic.coherence_score * 0.4,
            relevance_score=llm.relevance_score * 0.6 + heuristic.relevance_score * 0.4,
            conciseness_score=llm.conciseness_score * 0.6 + heuristic.conciseness_score * 0.4,
            temporal_awareness_score=llm.temporal_awareness_score * 0.6 + heuristic.temporal_awareness_score * 0.4,
            entity_coverage_score=llm.entity_coverage_score * 0.6 + heuristic.entity_coverage_score * 0.4,
            actionability_score=llm.actionability_score * 0.6 + heuristic.actionability_score * 0.4
        )
    
    def _calculate_overall_quality_score(self, factors: QualityFactors) -> float:
        """Calculate overall quality score from individual factors."""
        # Weighted combination of factors
        weights = {
            'completeness': 0.20,
            'coherence': 0.15,
            'relevance': 0.20,
            'conciseness': 0.15,
            'temporal_awareness': 0.10,
            'entity_coverage': 0.10,
            'actionability': 0.10
        }
        
        score = (
            factors.completeness_score * weights['completeness'] +
            factors.coherence_score * weights['coherence'] +
            factors.relevance_score * weights['relevance'] +
            factors.conciseness_score * weights['conciseness'] +
            factors.temporal_awareness_score * weights['temporal_awareness'] +
            factors.entity_coverage_score * weights['entity_coverage'] +
            factors.actionability_score * weights['actionability']
        )
        
        return min(max(score, 0.0), 1.0)
    
    def calculate_adaptive_length(
        self, 
        conversation_complexity: float, 
        content_length: int,
        quality_score: float = 0.5
    ) -> int:
        """Calculate adaptive summary length based on conversation complexity."""
        # Base length calculation
        base_length = self.target_summary_length
        
        # Adjust for complexity (0.0 = simple, 1.0 = very complex)
        complexity_factor = 0.5 + (conversation_complexity * 0.5)  # 0.5 to 1.0
        
        # Adjust for content length
        content_factor = min(content_length / 5000, 2.0)  # Cap at 2x for very long content
        
        # Adjust for quality (higher quality allows for more concise summaries)
        quality_factor = 1.2 - (quality_score * 0.4)  # 0.8 to 1.2
        
        # Calculate adaptive length
        adaptive_length = int(base_length * complexity_factor * content_factor * quality_factor)
        
        # Apply bounds
        adaptive_length = max(self.min_summary_length, min(adaptive_length, self.max_summary_length))
        
        return adaptive_length
    
    def to_dict(self, factors: QualityFactors) -> Dict[str, Any]:
        """Convert quality factors to dictionary for storage."""
        return {
            'completeness_score': factors.completeness_score,
            'coherence_score': factors.coherence_score,
            'relevance_score': factors.relevance_score,
            'conciseness_score': factors.conciseness_score,
            'temporal_awareness_score': factors.temporal_awareness_score,
            'entity_coverage_score': factors.entity_coverage_score,
            'actionability_score': factors.actionability_score,
            'assessment_timestamp': datetime.now().isoformat()
        }
