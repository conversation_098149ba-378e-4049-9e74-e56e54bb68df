"""
Enhanced Search Module for Memory Intelligence Enhancement Phase 4

Implements advanced search capabilities with metadata filtering, recency boosting,
query expansion, and graph relationship integration.
"""

import json
import logging
import time
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import asyncio

from config import get_config
from exceptions import DatabaseQueryError, EmbeddingGenerationError

logger = logging.getLogger(__name__)
config = get_config()


class EnhancedMemorySearch:
    """
    Enhanced search capabilities for Phase 4 implementation.
    
    Features:
    - Metadata filtering (date ranges, context, topics)
    - Recency boosting for recent memories
    - Query expansion with conversation context
    - Combined vector and graph search
    - Performance optimization with caching
    """
    
    def __init__(self, db_client, llm_client, bge_client, graph_service=None, summary_manager=None):
        """
        Initialize enhanced search module.
        
        Args:
            db_client: Database client with vector search
            llm_client: LLM client for query expansion
            bge_client: BGE embedding client
            graph_service: Optional graph service for relationship search
            summary_manager: Optional summary manager for context
        """
        self.db = db_client
        self.llm_client = llm_client
        self.bge_client = bge_client
        self.graph_service = graph_service
        self.summary_manager = summary_manager
        
        # Query expansion cache
        self._expansion_cache = {}
        self._cache_ttl = 300  # 5 minutes
        
        logger.info("Initialized EnhancedMemorySearch")
    
    async def search_with_filters(
        self, 
        query: str, 
        user_id: str, 
        limit: int = 5,
        filters: Optional[Dict[str, Any]] = None,
        expand_query: bool = True,
        include_graph: bool = True
    ) -> Dict[str, Any]:
        """
        Enhanced search with metadata filters and advanced features.
        
        Args:
            query: Search query string
            user_id: User identifier
            limit: Maximum results to return
            filters: Optional filters dict with:
                - date_range: Tuple of (start_date, end_date)
                - context: Specific context to filter by
                - topics: List of topics to filter by
                - recency_boost: Whether to boost recent memories
            expand_query: Whether to expand query with context
            include_graph: Whether to include graph relationships
            
        Returns:
            Dictionary with search results and metadata
        """
        search_start = time.time()
        
        try:
            # Step 1: Query expansion (if enabled)
            expanded_queries = [query]
            if expand_query and self.llm_client:
                expansion_result = await self._expand_query_with_context(query, user_id)
                expanded_queries.extend(expansion_result.get("expansions", []))
            
            # Step 2: Generate embeddings for all queries
            embeddings = await self._generate_query_embeddings(expanded_queries)
            
            # Step 3: Perform enhanced vector search with filters
            all_results = []
            for i, (q, embedding) in enumerate(zip(expanded_queries, embeddings)):
                results = await self._vector_search_with_filters(
                    embedding, user_id, limit * 2, filters  # Get more results for re-ranking
                )
                
                # Tag results with query source
                for result in results:
                    result["query_source"] = "original" if i == 0 else "expanded"
                    result["matched_query"] = q
                
                all_results.extend(results)
            
            # Step 4: Enhance with graph relationships (if enabled)
            if include_graph and self.graph_service:
                all_results = await self._enhance_with_graph_relationships(all_results, user_id)
            
            # Step 5: Apply recency boosting (if enabled)
            if filters and filters.get("recency_boost", True):
                all_results = self._apply_recency_boost(all_results)
            
            # Step 6: De-duplicate and re-rank results
            final_results = self._deduplicate_and_rank(all_results, limit)
            
            # Calculate metadata
            search_time = (time.time() - search_start) * 1000
            
            return {
                "success": True,
                "results": final_results,
                "metadata": {
                    "query": query,
                    "expanded_queries": expanded_queries[1:] if expand_query else [],
                    "filters_applied": filters or {},
                    "total_candidates": len(all_results),
                    "results_returned": len(final_results),
                    "search_time_ms": search_time,
                    "features_used": {
                        "query_expansion": expand_query,
                        "graph_enhancement": include_graph and self.graph_service is not None,
                        "recency_boost": filters.get("recency_boost", True) if filters else True
                    }
                }
            }
            
        except Exception as e:
            logger.error(f"Enhanced search failed: {e}")
            raise DatabaseQueryError(f"Enhanced search failed: {str(e)}")
    
    async def _expand_query_with_context(self, query: str, user_id: str) -> Dict[str, Any]:
        """
        Expand query using conversation context and related terms.
        
        Args:
            query: Original search query
            user_id: User identifier
            
        Returns:
            Dictionary with expanded queries and metadata
        """
        # Check cache first
        cache_key = f"{user_id}:{query}"
        if cache_key in self._expansion_cache:
            cached_result, cached_time = self._expansion_cache[cache_key]
            if time.time() - cached_time < self._cache_ttl:
                logger.debug(f"Using cached query expansion for: {query}")
                return cached_result
        
        try:
            # Get conversation context
            context = ""
            if self.summary_manager:
                context = await self.summary_manager.get_conversation_summary(user_id)
            
            # Build expansion prompt
            prompt = self._build_query_expansion_prompt(query, context)
            
            # Generate expansions
            response = await self.llm_client.generate(prompt)
            
            # Parse response
            expansions = self._parse_expansion_response(response)
            
            result = {
                "original_query": query,
                "expansions": expansions,
                "context_used": bool(context)
            }
            
            # Cache result
            self._expansion_cache[cache_key] = (result, time.time())
            
            logger.info(f"Expanded query '{query}' to {len(expansions)} variations")
            return result
            
        except Exception as e:
            logger.error(f"Query expansion failed: {e}")
            # Return empty expansions on failure
            return {"original_query": query, "expansions": [], "context_used": False}
    
    def _build_query_expansion_prompt(self, query: str, context: str) -> str:
        """Build prompt for query expansion."""
        return f"""
Expand this search query with related terms and alternative phrasings to improve memory search.

Original Query: {query}

Recent Conversation Context:
{context[:500] if context else "No recent context available"}

Generate 2-3 alternative phrasings or related terms that might help find relevant memories.
Consider:
- Synonyms and related concepts
- Different ways to express the same idea
- Context-specific terms from the conversation

Respond with a JSON array of expanded queries. Keep each expansion concise and relevant.

Example response format:
["alternative phrasing 1", "related concept search", "synonym-based query"]

Expanded Queries:
"""
    
    def _parse_expansion_response(self, response: str) -> List[str]:
        """Parse LLM response for query expansions."""
        try:
            # Clean response and parse JSON
            response = response.strip()
            if response.startswith("```json"):
                response = response[7:]
            if response.endswith("```"):
                response = response[:-3]
            
            expansions = json.loads(response.strip())
            
            # Validate and clean expansions
            valid_expansions = []
            for exp in expansions[:3]:  # Limit to 3 expansions
                if isinstance(exp, str) and 3 < len(exp) < 200:
                    valid_expansions.append(exp.strip())
            
            return valid_expansions
            
        except Exception as e:
            logger.error(f"Failed to parse query expansions: {e}")
            return []
    
    async def _generate_query_embeddings(self, queries: List[str]) -> List[List[float]]:
        """Generate embeddings for multiple queries."""
        try:
            # Use batch embedding for efficiency
            embeddings = await self.bge_client.embed_texts(queries, add_instruction=True)
            return embeddings
        except Exception as e:
            logger.error(f"Failed to generate query embeddings: {e}")
            raise EmbeddingGenerationError(f"Query embedding failed: {str(e)}")
    
    async def _vector_search_with_filters(
        self, 
        embedding: List[float], 
        user_id: str, 
        limit: int,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Perform vector search with metadata filters.
        
        Args:
            embedding: Query embedding vector
            user_id: User identifier
            limit: Maximum results
            filters: Optional metadata filters
            
        Returns:
            List of search results with scores
        """
        # Build SQL query with filters
        sql_conditions = ["user_id = $2"]
        params = [embedding, user_id]
        param_count = 2
        
        if filters:
            # Date range filter
            if "date_range" in filters and filters["date_range"]:
                start_date, end_date = filters["date_range"]
                param_count += 2
                sql_conditions.append(f"created_at BETWEEN ${param_count-1} AND ${param_count}")
                params.extend([start_date, end_date])
            
            # Context filter
            if "context" in filters and filters["context"]:
                param_count += 1
                sql_conditions.append(f"metadata->>'context' = ${param_count}")
                params.append(filters["context"])
            
            # Topics filter (array contains)
            if "topics" in filters and filters["topics"]:
                param_count += 1
                sql_conditions.append(f"metadata->'topics' ?| ${param_count}")
                params.append(filters["topics"])
        
        # Build the complete query
        where_clause = " AND ".join(sql_conditions)
        
        sql = f"""
        WITH vector_results AS (
            SELECT 
                id,
                content,
                metadata,
                embedding <-> $1 as distance,
                1 - (embedding <-> $1) as similarity_score,
                created_at,
                updated_at
            FROM memories
            WHERE {where_clause}
            ORDER BY embedding <-> $1
            LIMIT {limit}
        )
        SELECT 
            id,
            content,
            metadata,
            distance,
            similarity_score,
            created_at,
            updated_at,
            EXTRACT(EPOCH FROM (NOW() - updated_at)) / 86400.0 as days_old
        FROM vector_results
        WHERE distance < {1 - config.database.SIMILARITY_THRESHOLD}
        """
        
        try:
            results = await self.db._execute_sql(sql, params)
            
            # Convert to list of dicts
            formatted_results = []
            for row in results:
                formatted_results.append({
                    "id": str(row["id"]),
                    "content": row["content"],
                    "metadata": row["metadata"] or {},
                    "distance": float(row["distance"]),
                    "similarity_score": float(row["similarity_score"]),
                    "created_at": row["created_at"].isoformat() if row["created_at"] else None,
                    "updated_at": row["updated_at"].isoformat() if row["updated_at"] else None,
                    "days_old": float(row["days_old"])
                })
            
            logger.debug(f"Vector search with filters returned {len(formatted_results)} results")
            return formatted_results
            
        except Exception as e:
            logger.error(f"Vector search with filters failed: {e}")
            raise DatabaseQueryError(f"Filtered search failed: {str(e)}")
    
    async def _enhance_with_graph_relationships(
        self, 
        results: List[Dict[str, Any]], 
        user_id: str
    ) -> List[Dict[str, Any]]:
        """
        Enhance search results with graph relationship data.
        
        Args:
            results: Initial search results
            user_id: User identifier
            
        Returns:
            Enhanced results with relationship scores
        """
        if not self.graph_service:
            return results
        
        try:
            # Process each result to find related memories
            for result in results:
                memory_id = result["id"]
                
                # Find related memories through graph
                related_memories = await self.graph_service.find_related_memories(
                    memory_id, user_id, depth=2
                )
                
                # Add relationship data
                result["related_memories"] = related_memories
                result["relationship_count"] = len(related_memories)
                
                # Boost score based on relationships
                # Each relationship adds a small boost to relevance
                relationship_boost = min(len(related_memories) * 0.05, 0.2)  # Max 20% boost
                result["relationship_boost"] = relationship_boost
                result["enhanced_score"] = result["similarity_score"] + relationship_boost
            
            logger.debug(f"Enhanced {len(results)} results with graph relationships")
            return results
            
        except Exception as e:
            logger.error(f"Graph enhancement failed: {e}")
            # Return unenhanced results on failure
            return results
    
    def _apply_recency_boost(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Apply recency boosting to search results.
        
        Recent memories get a score boost:
        - Last 24 hours: 15% boost
        - Last 7 days: 10% boost  
        - Last 30 days: 5% boost
        
        Args:
            results: Search results to boost
            
        Returns:
            Results with recency scores applied
        """
        for result in results:
            days_old = result.get("days_old", float('inf'))
            
            if days_old <= 1:  # Last 24 hours
                recency_boost = 0.15
            elif days_old <= 7:  # Last week
                recency_boost = 0.10
            elif days_old <= 30:  # Last month
                recency_boost = 0.05
            else:
                recency_boost = 0.0
            
            result["recency_boost"] = recency_boost
            
            # Apply boost to final score
            base_score = result.get("enhanced_score", result["similarity_score"])
            result["final_score"] = base_score + recency_boost
        
        return results
    
    def _deduplicate_and_rank(
        self, 
        results: List[Dict[str, Any]], 
        limit: int
    ) -> List[Dict[str, Any]]:
        """
        De-duplicate and re-rank results based on final scores.
        
        Args:
            results: All search results from various sources
            limit: Maximum results to return
            
        Returns:
            Final ranked and de-duplicated results
        """
        # De-duplicate by memory ID, keeping highest scoring version
        unique_results = {}
        for result in results:
            memory_id = result["id"]
            if memory_id not in unique_results or result.get("final_score", result["similarity_score"]) > unique_results[memory_id].get("final_score", unique_results[memory_id]["similarity_score"]):
                unique_results[memory_id] = result
        
        # Sort by final score
        sorted_results = sorted(
            unique_results.values(),
            key=lambda x: x.get("final_score", x["similarity_score"]),
            reverse=True
        )
        
        # Return top N results
        return sorted_results[:limit]
    
    async def search_by_entity(
        self, 
        entity_name: str, 
        user_id: str, 
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Search memories by entity name using graph relationships.
        
        Args:
            entity_name: Entity to search for
            user_id: User identifier
            limit: Maximum results
            
        Returns:
            Memories associated with the entity
        """
        if not self.graph_service:
            logger.warning("Graph service not available for entity search")
            return []
        
        try:
            # Get entity relationships
            entity_data = await self.graph_service.get_entity_relationships(entity_name, user_id)
            
            if not entity_data:
                return []
            
            # Find memories mentioning this entity
            # This would be implemented in graph_memory_service
            # For now, return empty list as placeholder
            return []
            
        except Exception as e:
            logger.error(f"Entity search failed: {e}")
            return []
    
    def clear_expansion_cache(self):
        """Clear the query expansion cache."""
        self._expansion_cache.clear()
        logger.debug("Cleared query expansion cache")