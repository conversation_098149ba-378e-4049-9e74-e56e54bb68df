"""
Enhanced BGE Embedding Client for Spark MCP Server

Hybrid implementation supporting both direct GPU acceleration and HTTP fallback.
Integrates with existing BGE server (BAAI/bge-base-en-v1.5) with CLS pooling
and FP16 optimization on RTX4060 ti 16GB.
"""

import httpx
import os
import time
import logging
import threading
import async<PERSON>
from typing import List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)

# Configuration
USE_DIRECT_EMBEDDING = os.getenv("USE_DIRECT_EMBEDDING", "false").lower() == "true"
BGE_SERVER_URL = os.getenv("BGE_SERVER_URL", "http://192.168.1.84:8080")
BGE_TIMEOUT = int(os.getenv("BGE_TIMEOUT", "30"))
EMBEDDING_MODEL_NAME = os.getenv("EMBEDDING_MODEL_NAME", "BAAI/bge-base-en-v1.5")
EMBEDDING_BATCH_SIZE = int(os.getenv("EMBEDDING_BATCH_SIZE", "32"))
EMBEDDING_DEVICE = os.getenv("EMBEDDING_DEVICE", "auto")

class DirectEmbeddingManager:
    """Direct GPU embedding generation with memory optimization"""
    
    def __init__(self):
        self.model = None
        self.device = None
        self._lock = threading.Lock()
        self._load_model()
    
    def _detect_device(self) -> str:
        """Detect optimal device for embedding generation"""
        if EMBEDDING_DEVICE.lower() != "auto":
            return EMBEDDING_DEVICE
        
        try:
            import torch
            if torch.cuda.is_available():
                return "cuda"
        except ImportError:
            pass
        
        return "cpu"
    
    def _load_model(self):
        """Load BGE model with GPU optimization"""
        try:
            from FlagEmbedding import FlagModel
            import torch
            
            self.device = self._detect_device()
            
            if self.device == "cuda" and torch.cuda.is_available():
                os.environ['CUDA_VISIBLE_DEVICES'] = '0'
                logger.info("Configured CUDA_VISIBLE_DEVICES=0 for GPU isolation")
            
            logger.info(f"Loading BGE model '{EMBEDDING_MODEL_NAME}' on {self.device}")
            start_time = time.time()
            
            if self.device == "cuda":
                devices_param = ["cuda:0"]
                use_fp16 = True
            else:
                devices_param = "cpu"
                use_fp16 = False
            
            self.model = FlagModel(
                EMBEDDING_MODEL_NAME,
                devices=devices_param,
                use_fp16=use_fp16,
                query_instruction_for_retrieval="Represent this sentence for searching relevant passages:"
            )
            
            load_time = time.time() - start_time
            logger.info(f"BGE model loaded in {load_time:.2f}s on {self.device}")
            
            if self.device == "cuda" and torch.cuda.is_available():
                gpu_name = torch.cuda.get_device_name(0)
                gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
                logger.info(f"Using GPU: {gpu_name} ({gpu_memory:.1f}GB)")
                
        except ImportError as e:
            logger.warning(f"FlagEmbedding not available: {e}")
            logger.info("Direct embedding disabled - using HTTP fallback")
            self.model = None
        except Exception as e:
            logger.error(f"Failed to load BGE model: {e}")
            if 'OutOfMemoryError' in str(type(e)) or 'CUDA out of memory' in str(e):
                logger.info("GPU out of memory - consider using CPU mode")
            self.model = None
    
    def batch_embed(self, texts: List[str], batch_size: int = None) -> List[List[float]]:
        """Generate embeddings with memory management"""
        if not texts or self.model is None:
            return []
        
        # Input validation and preprocessing
        processed_texts = []
        for i, text in enumerate(texts):
            if not text or not text.strip():
                processed_texts.append("[empty text]")
            elif len(text) > 8192:
                processed_texts.append(text[:8192])
            else:
                processed_texts.append(text)
        
        batch_size = batch_size or EMBEDDING_BATCH_SIZE
        embeddings = []
        start_time = time.time()
        
        try:
            with self._lock:
                for i in range(0, len(processed_texts), batch_size):
                    batch = processed_texts[i:i + batch_size]
                    batch_embeddings = self.model.encode(
                        batch,
                        batch_size=len(batch),
                        convert_to_numpy=True
                    )
                    embeddings.extend(batch_embeddings.tolist())
            
            processing_time = time.time() - start_time
            logger.debug(f"Generated {len(embeddings)} embeddings in {processing_time:.3f}s")
            return embeddings
            
        except Exception as e:
            logger.error(f"Direct embedding failed: {e}")
            return []
    
    def is_available(self) -> bool:
        """Check if direct embedding is available"""
        return self.model is not None
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get embedding manager metrics"""
        metrics = {
            "direct_embedding_available": self.is_available(),
            "device": self.device,
            "model_name": EMBEDDING_MODEL_NAME if self.is_available() else None
        }
        
        if self.device == "cuda":
            try:
                import torch
                if torch.cuda.is_available():
                    allocated = torch.cuda.memory_allocated(0) / (1024**2)
                    total = torch.cuda.get_device_properties(0).total_memory / (1024**2)
                    metrics.update({
                        "gpu_memory_allocated_mb": allocated,
                        "gpu_memory_total_mb": total,
                        "gpu_memory_usage_percent": (allocated / total) * 100
                    })
            except Exception as e:
                metrics["gpu_metrics_error"] = str(e)
        
        return metrics

# Global instances
_direct_manager: Optional[DirectEmbeddingManager] = None
_embedding_executor: Optional[ThreadPoolExecutor] = None

def get_direct_manager() -> DirectEmbeddingManager:
    """Get or create direct embedding manager"""
    global _direct_manager
    if _direct_manager is None:
        _direct_manager = DirectEmbeddingManager()
    return _direct_manager

def get_embedding_executor() -> ThreadPoolExecutor:
    """Get or create embedding thread pool"""
    global _embedding_executor
    if _embedding_executor is None:
        _embedding_executor = ThreadPoolExecutor(
            max_workers=4,
            thread_name_prefix="embedding_pool"
        )
    return _embedding_executor

class BGEEmbeddingClient:
    """
    Enhanced BGE client with direct GPU support and HTTP fallback.
    
    CRITICAL: Maintains CLS pooling compatibility with existing BGE server.
    """
    
    def __init__(self, base_url: Optional[str] = None):
        """
        Initialize enhanced BGE embedding client.
        
        Args:
            base_url: BGE server URL, defaults to environment variable
        """
        self.base_url = base_url or BGE_SERVER_URL
        self.client = httpx.AsyncClient(timeout=float(BGE_TIMEOUT))
        
        # BGE-specific query instruction
        self.query_instruction = "Represent this sentence for searching relevant passages:"
        
        # Initialize direct embedding if enabled
        self.use_direct = USE_DIRECT_EMBEDDING
        if self.use_direct:
            self.direct_manager = get_direct_manager()
            logger.info(f"Direct embedding: {self.direct_manager.is_available()}")
        else:
            self.direct_manager = None
        
        logger.info(f"Enhanced BGE client initialized (direct={self.use_direct}, server={self.base_url})")
    
    async def embed_texts(self, texts: List[str], add_instruction: bool = True) -> List[List[float]]:
        """
        Generate embeddings for multiple texts with hybrid approach.
        
        Args:
            texts: List of text strings to embed
            add_instruction: Whether to add query instruction prefix
            
        Returns:
            List of embeddings as float lists (768 dimensions for BGE base)
        """
        if not texts:
            return []
        
        # Try direct embedding first if available
        if self.use_direct and self.direct_manager and self.direct_manager.is_available():
            try:
                # Direct embedding handles instruction internally
                processed_texts = texts
                if add_instruction:
                    processed_texts = [f"{self.query_instruction} {text}" for text in texts]
                
                # Use async wrapper for direct embedding
                executor = get_embedding_executor()
                loop = asyncio.get_event_loop()
                embeddings = await loop.run_in_executor(
                    executor,
                    self.direct_manager.batch_embed,
                    processed_texts
                )
                
                if embeddings:
                    logger.debug(f"Generated {len(embeddings)} embeddings via direct GPU")
                    return embeddings
                else:
                    logger.warning("Direct embedding returned empty results, falling back to HTTP")
                    
            except Exception as e:
                logger.warning(f"Direct embedding failed: {e}, falling back to HTTP")
        
        # Fallback to HTTP service
        return await self._embed_via_http(texts, add_instruction)
    
    async def _embed_via_http(self, texts: List[str], add_instruction: bool = True) -> List[List[float]]:
        """Generate embeddings via HTTP service"""
        try:
            # Add query instruction if specified
            processed_texts = texts
            if add_instruction:
                processed_texts = [f"{self.query_instruction} {text}" for text in texts]
            
            response = await self.client.post(
                f"{self.base_url}/embed",
                json={"texts": processed_texts}
            )
            response.raise_for_status()
            
            result = response.json()
            embeddings = result.get("embeddings", [])
            
            logger.debug(f"Generated {len(embeddings)} embeddings via HTTP")
            return embeddings
            
        except Exception as e:
            logger.error(f"HTTP embedding failed: {e}")
            raise
    
    async def embed_single(self, text: str, add_instruction: bool = True) -> List[float]:
        """
        Generate embedding for single text with hybrid approach.
        
        Args:
            text: Text string to embed
            add_instruction: Whether to add query instruction prefix
            
        Returns:
            Single embedding as float list (768 dimensions)
        """
        # Use batch method for consistency and efficiency
        embeddings = await self.embed_texts([text], add_instruction)
        return embeddings[0] if embeddings else []
    
    async def embed_documents(self, documents: List[str]) -> List[List[float]]:
        """
        Generate embeddings for documents (without query instruction).
        
        Args:
            documents: List of document texts to embed
            
        Returns:
            List of embeddings for documents
        """
        return await self.embed_texts(documents, add_instruction=False)
    
    async def embed_queries(self, queries: List[str]) -> List[List[float]]:
        """
        Generate embeddings for queries (with query instruction).
        
        Args:
            queries: List of query texts to embed
            
        Returns:
            List of embeddings for queries
        """
        return await self.embed_texts(queries, add_instruction=True)
    
    async def embed_batch_concurrent(
        self, 
        text_batches: List[List[str]], 
        max_concurrent: int = 3,
        add_instruction: bool = True
    ) -> List[List[List[float]]]:
        """
        Generate embeddings for multiple batches concurrently.
        
        Args:
            text_batches: List of text batches to embed
            max_concurrent: Maximum concurrent operations
            add_instruction: Whether to add query instruction
            
        Returns:
            List of embedding batches
        """
        if not text_batches:
            return []
        
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def embed_batch_with_limit(batch: List[str]) -> List[List[float]]:
            async with semaphore:
                return await self.embed_texts(batch, add_instruction)
        
        tasks = [embed_batch_with_limit(batch) for batch in text_batches]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions in results
        valid_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Batch {i} failed: {result}")
                valid_results.append([])  # Empty result for failed batch
            else:
                valid_results.append(result)
        
        total_embeddings = sum(len(batch_result) for batch_result in valid_results)
        logger.info(f"Generated {total_embeddings} embeddings across {len(text_batches)} batches")
        
        return valid_results
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Comprehensive health check for both direct and HTTP embedding.
        
        Returns:
            Health status with detailed metrics
        """
        health_status = {
            "timestamp": time.time(),
            "direct_embedding": {
                "enabled": self.use_direct,
                "available": False,
                "metrics": {}
            },
            "http_service": {
                "available": False,
                "url": self.base_url,
                "response_time_ms": None
            }
        }
        
        # Check direct embedding
        if self.use_direct and self.direct_manager:
            health_status["direct_embedding"]["available"] = self.direct_manager.is_available()
            if self.direct_manager.is_available():
                health_status["direct_embedding"]["metrics"] = self.direct_manager.get_metrics()
        
        # Check HTTP service
        try:
            start_time = time.time()
            response = await self.client.get(f"{self.base_url}/health", timeout=5.0)
            response_time = (time.time() - start_time) * 1000
            
            health_status["http_service"]["available"] = response.status_code == 200
            health_status["http_service"]["response_time_ms"] = response_time
            
        except Exception as e:
            logger.warning(f"HTTP health check failed: {e}")
        
        return health_status
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get comprehensive performance metrics"""
        metrics = {
            "configuration": {
                "use_direct_embedding": self.use_direct,
                "bge_server_url": self.base_url,
                "model_name": EMBEDDING_MODEL_NAME,
                "batch_size": EMBEDDING_BATCH_SIZE,
                "device": EMBEDDING_DEVICE
            }
        }
        
        if self.direct_manager and self.direct_manager.is_available():
            metrics["direct_embedding"] = self.direct_manager.get_metrics()
        
        return metrics
    
    async def close(self):
        """Close the HTTP client and cleanup resources."""
        await self.client.aclose()
        
        # Cleanup thread pool executor if we created it
        global _embedding_executor
        if _embedding_executor is not None:
            _embedding_executor.shutdown(wait=False)
            _embedding_executor = None
    
    def __del__(self):
        """Cleanup on deletion."""
        try:
            import asyncio
            asyncio.create_task(self.close())
        except:
            pass