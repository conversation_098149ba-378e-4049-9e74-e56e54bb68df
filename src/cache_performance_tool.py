"""
Cache Performance MCP Tool

Provides detailed cache performance analysis and optimization insights
as an MCP tool for the Spark Memory server.
"""

from mcp.server.fastmcp import Context
import json
import logging
from cache_performance_monitor import get_cache_performance_monitor

logger = logging.getLogger(__name__)

async def get_cache_performance_report(ctx: Context) -> str:
    """
    Get comprehensive cache performance report with optimization insights.
    
    Args:
        ctx: The MCP server provided context
        
    Returns:
        JSON string with detailed cache performance analysis
    """
    try:
        cache_monitor = get_cache_performance_monitor()
        report = await cache_monitor.generate_performance_report()
        
        return json.dumps(report, indent=2)
        
    except Exception as e:
        logger.error(f"Error generating cache performance report: {e}")
        return json.dumps({
            "error": str(e),
            "report_type": "cache_performance_error"
        }, indent=2)

async def invalidate_llm_cache(ctx: Context, cache_type: str = None) -> str:
    """
    Invalidate LLM cache entries for testing and optimization.
    
    Args:
        ctx: The MCP server provided context
        cache_type: Optional cache type to invalidate (extraction, decision, summary)
        
    Returns:
        JSON string with invalidation results
    """
    try:
        from llm_cache_service import get_llm_cache_service, CacheType
        
        cache_service = get_llm_cache_service()
        
        # Convert string to CacheType enum if provided
        cache_type_enum = None
        if cache_type:
            try:
                cache_type_enum = CacheType(cache_type.lower())
            except ValueError:
                return json.dumps({
                    "success": False,
                    "error": f"Invalid cache type: {cache_type}",
                    "valid_types": [ct.value for ct in CacheType]
                })
        
        # Invalidate cache
        await cache_service.invalidate_cache(cache_type_enum)
        
        return json.dumps({
            "success": True,
            "invalidated_cache_type": cache_type or "all",
            "message": f"Successfully invalidated {cache_type or 'all'} cache entries"
        })
        
    except Exception as e:
        logger.error(f"Error invalidating cache: {e}")
        return json.dumps({
            "success": False,
            "error": str(e)
        })

async def cache_warm_common_patterns(ctx: Context) -> str:
    """
    Warm cache with common memory extraction and decision patterns.
    
    Args:
        ctx: The MCP server provided context
        
    Returns:
        JSON string with cache warming results
    """
    try:
        from llm_cache_service import get_llm_cache_service, CacheType
        
        cache_service = get_llm_cache_service()
        
        # Common extraction patterns
        extraction_patterns = [
            (
                "Extract factual memories from this conversation that should be stored for future reference.\n\nINPUT:\nPrevious Message: \nCurrent Message: I prefer React over Vue for frontend development\nConversation Summary: None\nRecent Context: None\n\nEXTRACT RULES:\n- Only factual, actionable information valuable for future conversations\n- Skip temporary states, generic responses, or redundant information\n- Each memory must be a complete, standalone sentence\n- Maximum 5 memories per extraction\n\nCRITICAL: You MUST respond with ONLY a valid JSON array of strings. No other text, explanations, or formatting.\n\nResponse (JSON array only):",
                '["User prefers React over Vue for frontend development"]',
                {"user_id": "test", "summary_length": 0, "recent_count": 0}
            ),
            (
                "Extract factual memories from this conversation that should be stored for future reference.\n\nINPUT:\nPrevious Message: \nCurrent Message: I work as a software engineer at TechCorp\nConversation Summary: None\nRecent Context: None\n\nEXTRACT RULES:\n- Only factual, actionable information valuable for future conversations\n- Skip temporary states, generic responses, or redundant information\n- Each memory must be a complete, standalone sentence\n- Maximum 5 memories per extraction\n\nCRITICAL: You MUST respond with ONLY a valid JSON array of strings. No other text, explanations, or formatting.\n\nResponse (JSON array only):",
                '["User works as a software engineer at TechCorp"]',
                {"user_id": "test", "summary_length": 0, "recent_count": 0}
            )
        ]
        
        # Common decision patterns
        decision_patterns = [
            (
                "Analyze this candidate memory and decide what operation to perform based on existing similar memories.\n\nCandidate Memory: User prefers React over Vue for frontend development\n\nSimilar Existing Memories:\nNo similar memories found\n\nOperations:\n- ADD: Create new memory (no conflicts, new information, or complementary to existing)\n- UPDATE: Enhance existing memory (similar memory exists that can be improved/expanded)\n- DELETE: Remove contradicted memory (candidate contradicts or makes existing memory obsolete)\n- NOOP: No action needed (duplicate, irrelevant, or already well-covered)\n\nDecision Criteria:\n- If distance < 0.3: Very similar, likely NOOP or UPDATE\n- If distance 0.3-0.5: Moderately similar, consider UPDATE or ADD\n- If distance 0.5-0.7: Somewhat similar, likely ADD unless contradictory\n- If no similar memories: ADD\n\nRespond with JSON format:\n{\"operation\": \"ADD|UPDATE|DELETE|NOOP\", \"reasoning\": \"Brief explanation of decision\"}",
                '{"operation": "ADD", "reasoning": "New information about user preferences with no similar existing memories"}',
                {"candidate_length": 45, "similar_count": 0, "similarity_scores": []}
            )
        ]
        
        # Warm extraction cache
        extraction_count = await cache_service.warm_cache(
            CacheType.EXTRACTION, extraction_patterns
        )
        
        # Warm decision cache
        decision_count = await cache_service.warm_cache(
            CacheType.DECISION, decision_patterns
        )
        
        return json.dumps({
            "success": True,
            "cache_warming_results": {
                "extraction_patterns_cached": extraction_count,
                "decision_patterns_cached": decision_count,
                "total_patterns_cached": extraction_count + decision_count
            },
            "message": "Cache warming completed successfully"
        })
        
    except Exception as e:
        logger.error(f"Error warming cache: {e}")
        return json.dumps({
            "success": False,
            "error": str(e)
        })