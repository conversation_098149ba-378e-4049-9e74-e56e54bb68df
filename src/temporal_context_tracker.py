"""
Temporal Context Tracker for Enhanced Rolling Summaries

Tracks evolution of information over time, identifying changes, updates,
and temporal patterns in conversation context.

Phase 2 Feature 3/3: Enhanced Rolling Summary Features
"""

import json
import logging
import re
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class TemporalMarker:
    """Represents a temporal marker in conversation context."""
    marker_type: str  # 'change', 'update', 'evolution', 'new', 'deprecated'
    content: str
    timestamp: datetime
    confidence: float = 0.5
    related_entities: List[str] = None
    previous_state: Optional[str] = None
    current_state: Optional[str] = None

class TemporalContextTracker:
    """
    Tracks temporal evolution of conversation context.
    
    Identifies when information changes, updates, or evolves over time,
    providing rich temporal context for enhanced summaries.
    """
    
    def __init__(self, llm_client):
        """
        Initialize temporal context tracker.
        
        Args:
            llm_client: LLM client for temporal analysis
        """
        self.llm_client = llm_client
        
        # Temporal pattern indicators
        self.change_patterns = {
            'preference_change': [
                r'\b(now prefer|changed to|switched to|instead of|rather than)\b',
                r'\b(no longer|used to|previously|before)\b',
                r'\b(updated|modified|revised|changed)\b'
            ],
            'status_update': [
                r'\b(now working on|currently|recently started|just finished)\b',
                r'\b(completed|done with|moved to|transitioned)\b',
                r'\b(status.*changed|progress.*made)\b'
            ],
            'learning_evolution': [
                r'\b(learned|discovered|realized|found out)\b',
                r'\b(now understand|figured out|got better at)\b',
                r'\b(improved|developed|gained experience)\b'
            ],
            'temporal_markers': [
                r'\b(yesterday|today|tomorrow|last week|next month)\b',
                r'\b(recently|lately|currently|now|soon)\b',
                r'\b(since|until|after|before|during)\b'
            ]
        }
        
        self.evolution_indicators = [
            'now', 'currently', 'recently', 'lately', 'changed', 'updated',
            'switched', 'moved', 'transitioned', 'evolved', 'developed',
            'learned', 'discovered', 'improved', 'modified', 'revised'
        ]
        
        logger.info("Initialized TemporalContextTracker")
    
    async def extract_temporal_markers(
        self, 
        current_content: str, 
        previous_summary: str = "",
        context: Dict[str, Any] = None
    ) -> List[TemporalMarker]:
        """
        Extract temporal markers from content.
        
        Args:
            current_content: Current conversation content
            previous_summary: Previous conversation summary
            context: Additional context information
            
        Returns:
            List of temporal markers found
        """
        try:
            markers = []
            
            # 1. Pattern-based extraction
            pattern_markers = self._extract_pattern_markers(current_content)
            markers.extend(pattern_markers)
            
            # 2. LLM-based temporal analysis
            if previous_summary:
                llm_markers = await self._extract_llm_temporal_markers(
                    current_content, previous_summary
                )
                markers.extend(llm_markers)
            
            # 3. Entity evolution detection
            entity_markers = self._detect_entity_evolution(current_content, previous_summary)
            markers.extend(entity_markers)
            
            # Remove duplicates and sort by confidence
            unique_markers = self._deduplicate_markers(markers)
            unique_markers.sort(key=lambda m: m.confidence, reverse=True)
            
            logger.debug(f"Extracted {len(unique_markers)} temporal markers")
            return unique_markers[:10]  # Limit to top 10
            
        except Exception as e:
            logger.error(f"Error extracting temporal markers: {e}")
            return []
    
    def _extract_pattern_markers(self, content: str) -> List[TemporalMarker]:
        """Extract temporal markers using pattern matching."""
        markers = []
        content_lower = content.lower()
        
        for marker_type, patterns in self.change_patterns.items():
            for pattern in patterns:
                matches = re.finditer(pattern, content_lower)
                for match in matches:
                    # Extract surrounding context
                    start = max(0, match.start() - 50)
                    end = min(len(content), match.end() + 50)
                    context_text = content[start:end].strip()
                    
                    marker = TemporalMarker(
                        marker_type=marker_type,
                        content=context_text,
                        timestamp=datetime.now(),
                        confidence=0.6,  # Pattern-based confidence
                        related_entities=self._extract_entities_from_text(context_text)
                    )
                    markers.append(marker)
        
        return markers
    
    async def _extract_llm_temporal_markers(
        self, 
        current_content: str, 
        previous_summary: str
    ) -> List[TemporalMarker]:
        """Use LLM to identify temporal changes and evolution."""
        try:
            prompt = f"""Analyze the conversation for temporal changes and evolution patterns.

Previous Summary:
{previous_summary}

Current Content:
{current_content}

Identify temporal markers that show:
1. Changes in preferences or opinions
2. Status updates on projects or activities  
3. Learning or skill evolution
4. New information that updates previous knowledge
5. Temporal context (when things happened)

For each temporal marker found, respond with JSON:
{{
  "markers": [
    {{
      "type": "change|update|evolution|new|deprecated",
      "content": "specific text showing the change",
      "confidence": 0.0-1.0,
      "previous_state": "what it was before (if applicable)",
      "current_state": "what it is now",
      "entities": ["related", "entities"]
    }}
  ]
}}

Focus on significant changes that affect conversation context."""

            response = await self.llm_client.generate(prompt)
            
            try:
                data = json.loads(response)
                markers = []
                
                for marker_data in data.get('markers', []):
                    marker = TemporalMarker(
                        marker_type=marker_data.get('type', 'update'),
                        content=marker_data.get('content', ''),
                        timestamp=datetime.now(),
                        confidence=marker_data.get('confidence', 0.5),
                        related_entities=marker_data.get('entities', []),
                        previous_state=marker_data.get('previous_state'),
                        current_state=marker_data.get('current_state')
                    )
                    markers.append(marker)
                
                return markers
                
            except json.JSONDecodeError:
                logger.warning("Failed to parse LLM temporal analysis response")
                return []
                
        except Exception as e:
            logger.warning(f"LLM temporal analysis failed: {e}")
            return []
    
    def _detect_entity_evolution(self, current_content: str, previous_summary: str) -> List[TemporalMarker]:
        """Detect evolution in entity relationships and properties."""
        markers = []
        
        if not previous_summary:
            return markers
        
        # Simple entity evolution detection
        current_entities = self._extract_entities_from_text(current_content)
        previous_entities = self._extract_entities_from_text(previous_summary)
        
        # New entities
        new_entities = set(current_entities) - set(previous_entities)
        for entity in new_entities:
            marker = TemporalMarker(
                marker_type='new',
                content=f"New entity mentioned: {entity}",
                timestamp=datetime.now(),
                confidence=0.7,
                related_entities=[entity],
                current_state=f"newly mentioned: {entity}"
            )
            markers.append(marker)
        
        return markers
    
    def _extract_entities_from_text(self, text: str) -> List[str]:
        """Simple entity extraction from text."""
        # Basic entity patterns (can be enhanced with NER)
        import re
        
        entities = []
        
        # Proper nouns (capitalized words)
        proper_nouns = re.findall(r'\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b', text)
        entities.extend(proper_nouns)
        
        # Technologies/tools (common patterns)
        tech_patterns = [
            r'\b(Python|JavaScript|React|Node\.js|Docker|AWS|Git)\b',
            r'\b(VS Code|PyCharm|Slack|Zoom|Teams)\b'
        ]
        
        for pattern in tech_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            entities.extend(matches)
        
        # Remove duplicates and limit
        return list(set(entities))[:10]
    
    def _deduplicate_markers(self, markers: List[TemporalMarker]) -> List[TemporalMarker]:
        """Remove duplicate temporal markers."""
        seen_content = set()
        unique_markers = []
        
        for marker in markers:
            # Create a simple hash of the marker content
            content_hash = hash(marker.content.lower().strip())
            
            if content_hash not in seen_content:
                seen_content.add(content_hash)
                unique_markers.append(marker)
        
        return unique_markers
    
    def analyze_temporal_evolution(self, markers: List[TemporalMarker]) -> Dict[str, Any]:
        """Analyze temporal evolution patterns from markers."""
        analysis = {
            'total_markers': len(markers),
            'change_frequency': 0,
            'evolution_types': {},
            'confidence_distribution': {},
            'temporal_patterns': []
        }
        
        if not markers:
            return analysis
        
        # Count evolution types
        for marker in markers:
            marker_type = marker.marker_type
            analysis['evolution_types'][marker_type] = analysis['evolution_types'].get(marker_type, 0) + 1
        
        # Confidence distribution
        high_conf = sum(1 for m in markers if m.confidence >= 0.7)
        medium_conf = sum(1 for m in markers if 0.4 <= m.confidence < 0.7)
        low_conf = sum(1 for m in markers if m.confidence < 0.4)
        
        analysis['confidence_distribution'] = {
            'high': high_conf,
            'medium': medium_conf,
            'low': low_conf
        }
        
        # Change frequency (markers per time period)
        analysis['change_frequency'] = len(markers) / max(1, len(set(m.timestamp.date() for m in markers)))
        
        return analysis
    
    def to_dict(self, marker: TemporalMarker) -> Dict[str, Any]:
        """Convert temporal marker to dictionary for storage."""
        return {
            'marker_type': marker.marker_type,
            'content': marker.content,
            'timestamp': marker.timestamp.isoformat(),
            'confidence': marker.confidence,
            'related_entities': marker.related_entities or [],
            'previous_state': marker.previous_state,
            'current_state': marker.current_state
        }
