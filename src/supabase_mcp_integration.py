"""
Real Supabase MCP Integration for Memory Operations

This module provides actual database operations using Supabase MCP tools
instead of placeholder implementations.
"""

import json
import logging
import asyncio
import asyncpg
import os
from typing import List, Dict, Any, Optional

logger = logging.getLogger(__name__)

class RealSupabaseMCP:
    """
    Real implementation of Supabase MCP integration for memory operations.
    
    This uses the actual Supabase MCP tools available in the environment.
    """
    
    def __init__(self):
        """Initialize the database integration."""
        self.database_url = os.getenv('DATABASE_URL')
        if not self.database_url:
            raise ValueError("DATABASE_URL environment variable is required")
        logger.info("Initialized RealSupabaseMCP with direct database connection")
    
    async def execute_sql(self, sql: str, params: List = None) -> List[Dict[str, Any]]:
        """
        Execute SQL using direct asyncpg connection.
        
        Args:
            sql: SQL query to execute
            params: Optional parameters for the query
            
        Returns:
            Query results as list of dictionaries
        """
        try:
            # Connect to database
            conn = await asyncpg.connect(self.database_url)
            
            try:
                # Convert parameters to proper asyncpg format
                asyncpg_params = []
                if params:
                    for param in params:
                        if isinstance(param, list):  # Handle embedding vectors
                            # Convert to PostgreSQL vector literal format
                            # asyncpg requires vector type as string '[x,y,z]' format
                            vector_str = '[' + ','.join(str(float(x)) for x in param) + ']'
                            asyncpg_params.append(vector_str)
                        elif isinstance(param, dict):
                            # Convert dict to JSON string for jsonb
                            asyncpg_params.append(json.dumps(param))
                        else:
                            asyncpg_params.append(param)
                
                logger.debug(f"Executing SQL: {sql[:100]}... with {len(asyncpg_params)} params")
                
                # Execute query
                if "SELECT" in sql.upper():
                    # For SELECT queries, fetch all results
                    rows = await conn.fetch(sql, *asyncpg_params)
                    # Convert asyncpg.Record to dict
                    result = [dict(row) for row in rows]
                else:
                    # For INSERT/UPDATE/DELETE queries
                    if "RETURNING" in sql.upper():
                        # Query returns data
                        rows = await conn.fetch(sql, *asyncpg_params)
                        result = [dict(row) for row in rows]
                    else:
                        # Query doesn't return data
                        await conn.execute(sql, *asyncpg_params)
                        result = []
                
                logger.debug(f"SQL executed successfully, returned {len(result)} rows")
                return result
                
            finally:
                await conn.close()
                
        except Exception as e:
            logger.error(f"Error executing SQL: {e}")
            logger.error(f"SQL: {sql}")
            logger.error(f"Params: {params}")
            raise
    
    async def store_memory(
        self, 
        user_id: str, 
        content: str, 
        embedding: List[float], 
        metadata: Optional[Dict[str, Any]] = None
    ) -> int:
        """Store a memory with its embedding."""
        # Validate embedding format
        if not isinstance(embedding, list) or not embedding:
            raise ValueError("Embedding must be a non-empty list of floats")
        
        if not all(isinstance(x, (int, float)) for x in embedding):
            raise ValueError("All embedding values must be numeric")
        
        sql = """
            INSERT INTO memories (user_id, content, embedding, metadata, created_at, updated_at) 
            VALUES ($1, $2, $3::vector, $4, NOW(), NOW())
            RETURNING id
        """
        
        results = await self.execute_sql(sql, [user_id, content, embedding, metadata or {}])
        
        if results and len(results) > 0:
            memory_id = results[0].get('id')
            logger.info(f"Stored memory {memory_id} for user {user_id} with {len(embedding)}-dim embedding")
            return memory_id
        else:
            logger.error("No ID returned from memory insert")
            raise Exception("Failed to store memory")
    
    async def similarity_search(
        self, 
        embedding: List[float], 
        user_id: str, 
        threshold: float = 1.5, 
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """Perform vector similarity search using L2 distance."""
        # Validate embedding format
        if not isinstance(embedding, list) or not embedding:
            raise ValueError("Search embedding must be a non-empty list of floats")
        
        if not all(isinstance(x, (int, float)) for x in embedding):
            raise ValueError("All embedding values must be numeric")
        
        sql = """
            SELECT id, content, metadata, created_at, updated_at,
                   (embedding <-> $1::vector) as distance
            FROM memories 
            WHERE user_id = $2 AND (embedding <-> $1::vector) < $3
            ORDER BY distance
            LIMIT $4
        """
        
        results = await self.execute_sql(sql, [embedding, user_id, threshold, limit])
        logger.info(f"Found {len(results)} similar memories for user {user_id} (threshold={threshold})")
        return results
    
    async def get_all_memories(
        self, 
        user_id: str, 
        limit: int = 50, 
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """Get all memories for a user."""
        sql = """
            SELECT id, content, metadata, created_at, updated_at
            FROM memories 
            WHERE user_id = $1
            ORDER BY created_at DESC
            LIMIT $2 OFFSET $3
        """
        
        results = await self.execute_sql(sql, [user_id, limit, offset])
        logger.debug(f"Retrieved {len(results)} memories for user {user_id}")
        return results
    
    async def delete_all_memories(self, user_id: str) -> int:
        """Delete all memories for a user."""
        # First get count
        count_sql = "SELECT COUNT(*) as count FROM memories WHERE user_id = $1"
        count_results = await self.execute_sql(count_sql, [user_id])
        deleted_count = count_results[0].get('count', 0) if count_results else 0
        
        # Then delete
        delete_sql = "DELETE FROM memories WHERE user_id = $1"
        await self.execute_sql(delete_sql, [user_id])
        
        logger.info(f"Deleted {deleted_count} memories for user {user_id}")
        return deleted_count
    
    async def update_memory(
        self, 
        memory_id: int, 
        content: str, 
        embedding: List[float], 
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Update an existing memory."""
        # Validate embedding format
        if not isinstance(embedding, list) or not embedding:
            raise ValueError("Embedding must be a non-empty list of floats")
        
        if not all(isinstance(x, (int, float)) for x in embedding):
            raise ValueError("All embedding values must be numeric")
        
        sql = """
            UPDATE memories 
            SET content = $2, embedding = $3::vector, metadata = $4, updated_at = NOW()
            WHERE id = $1
        """
        
        await self.execute_sql(sql, [memory_id, content, embedding, metadata or {}])
        logger.debug(f"Updated memory {memory_id} with {len(embedding)}-dim embedding")
    
    async def delete_memory(self, memory_id: int):
        """Delete a specific memory."""
        sql = "DELETE FROM memories WHERE id = $1"
        await self.execute_sql(sql, [memory_id])
        logger.debug(f"Deleted memory {memory_id}")
    
    async def store_conversation_summary(self, user_id: str, summary: str):
        """Store or update conversation summary."""
        sql = """
            INSERT INTO conversation_summaries (user_id, summary, updated_at)
            VALUES ($1, $2, NOW())
            ON CONFLICT (user_id) 
            DO UPDATE SET summary = $2, updated_at = NOW()
        """
        
        await self.execute_sql(sql, [user_id, summary])
        logger.debug(f"Stored conversation summary for user {user_id}")
    
    async def get_conversation_summary(self, user_id: str) -> Optional[str]:
        """Get conversation summary for a user."""
        sql = "SELECT summary FROM conversation_summaries WHERE user_id = $1"
        results = await self.execute_sql(sql, [user_id])
        
        if results and len(results) > 0:
            return results[0].get('summary')
        return None
    
    async def get_recent_memories(self, user_id: str, limit: int = 10) -> List[str]:
        """Get recent memory contents for context."""
        sql = """
            SELECT content FROM memories 
            WHERE user_id = $1
            ORDER BY created_at DESC
            LIMIT $2
        """
        
        results = await self.execute_sql(sql, [user_id, limit])
        return [row.get('content', '') for row in results]