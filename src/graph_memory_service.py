"""
Graph Memory Service for Neo4j Integration

Implements relationship-based memory evolution with entity extraction and
graph traversal for enhanced memory intelligence.
"""

import json
import logging
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from neo4j import GraphDatabase
from neo4j.exceptions import ServiceUnavailable, TransientError

from config import get_config, get_neo4j_config
from exceptions import GraphConnectionError, EntityExtractionError

logger = logging.getLogger(__name__)
config = get_config()

class GraphMemoryService:
    """
    Neo4j integration for relationship-based memory evolution.
    
    Handles entity extraction, relationship inference, and graph traversal
    for enhanced memory intelligence and context awareness.
    """
    
    def __init__(self, llm_client=None):
        """
        Initialize graph memory service.
        
        Args:
            llm_client: LLM client for entity extraction and relationship inference
        """
        self.llm_client = llm_client
        self.driver = None
        self._connection_pool = None
        
        # Neo4j connection configuration
        self.neo4j_config = get_neo4j_config()
        
        logger.info("Initialized GraphMemoryService")
    
    async def initialize(self):
        """Initialize Neo4j connection and create indexes."""
        try:
            # Create Neo4j driver with configuration
            self.driver = GraphDatabase.driver(
                self.neo4j_config["uri"],
                auth=(self.neo4j_config["username"], self.neo4j_config["password"]),
                connection_timeout=config.graph_store.CONNECTION_TIMEOUT,
                max_transaction_retry_time=config.graph_store.MAX_TRANSACTION_RETRY_TIME
            )
            
            # Verify connection
            await self._verify_connection()
            
            # Create indexes and constraints
            await self._create_indexes()
            
            logger.info("Graph memory service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize graph memory service: {e}")
            raise GraphConnectionError(f"Neo4j initialization failed: {e}")
    
    async def _verify_connection(self):
        """Verify Neo4j connection is working."""
        def _verify(tx):
            result = tx.run("RETURN 1 as test")
            return result.single()["test"]
        
        with self.driver.session() as session:
            result = session.execute_read(_verify)
            if result != 1:
                raise GraphConnectionError("Neo4j connection verification failed")
    
    async def _create_indexes(self):
        """Create necessary indexes and constraints for performance."""
        def _create_indexes_tx(tx):
            # Create constraints for unique entities
            tx.run("CREATE CONSTRAINT entity_name_unique IF NOT EXISTS FOR (e:Entity) REQUIRE e.name IS UNIQUE")
            tx.run("CREATE CONSTRAINT memory_id_unique IF NOT EXISTS FOR (m:Memory) REQUIRE m.id IS UNIQUE")
            
            # Create indexes for performance
            tx.run("CREATE INDEX entity_type_idx IF NOT EXISTS FOR (e:Entity) ON (e.type)")
            tx.run("CREATE INDEX entity_created_idx IF NOT EXISTS FOR (e:Entity) ON (e.created_at)")
            tx.run("CREATE INDEX memory_user_idx IF NOT EXISTS FOR (m:Memory) ON (m.user_id)")
            tx.run("CREATE INDEX memory_created_idx IF NOT EXISTS FOR (m:Memory) ON (m.created_at)")
        
        try:
            with self.driver.session() as session:
                session.execute_write(_create_indexes_tx)
                logger.info("Created Neo4j indexes and constraints")
        except Exception as e:
            logger.warning(f"Failed to create some indexes (may already exist): {e}")
    
    async def extract_entities(self, content: str, memory_id: str, user_id: str) -> List[Dict[str, Any]]:
        """
        Extract entities from memory content using LLM.
        
        Args:
            content: Memory content to extract entities from
            memory_id: Associated memory ID
            user_id: User identifier
            
        Returns:
            List of extracted entities with metadata
        """
        if not self.llm_client:
            logger.warning("No LLM client available for entity extraction")
            return []
        
        try:
            # Build entity extraction prompt
            prompt = self._build_entity_extraction_prompt(content)
            
            # Generate entity extraction
            response = await self.llm_client.generate(prompt)
            
            # Parse response
            entities = self._parse_entity_response(response)
            
            # Add metadata to entities
            for entity in entities:
                entity.update({
                    "memory_id": memory_id,
                    "user_id": user_id,
                    "extracted_at": datetime.utcnow().isoformat(),
                    "source_content": content[:100] + "..." if len(content) > 100 else content
                })
            
            logger.debug(f"Extracted {len(entities)} entities from memory {memory_id}")
            return entities
            
        except Exception as e:
            logger.error(f"Entity extraction failed: {e}")
            raise EntityExtractionError(f"Failed to extract entities: {e}")
    
    def _build_entity_extraction_prompt(self, content: str) -> str:
        """Build prompt for entity extraction."""
        return f"""
{config.graph_store.ENTITY_EXTRACTION_PROMPT}

Memory Content: {content}

Extract entities and relationships, focusing on:
- People (names, roles, relationships)
- Projects (names, status, technologies used)
- Technologies (tools, frameworks, languages)
- Concepts (skills, methodologies, domains)
- Organizations (companies, teams, groups)

Response format (JSON only):
{{
  "entities": [
    {{"name": "EntityName", "type": "Person|Project|Technology|Concept|Organization|Skill", "properties": {{"role": "description", "context": "additional_info"}}}}
  ],
  "relationships": [
    {{"from": "EntityA", "to": "EntityB", "type": "COLLABORATES_WITH|WORKS_ON|USES|DEPENDS_ON|LEADS|MEMBER_OF|SKILLED_IN|PREFERS|LEARNED", "properties": {{"context": "relationship_context"}}}}
  ]
}}
"""
    
    def _parse_entity_response(self, response: str) -> List[Dict[str, Any]]:
        """Parse LLM response for entities and relationships."""
        try:
            data = json.loads(response.strip())
            entities = data.get("entities", [])
            relationships = data.get("relationships", [])
            
            # Validate entities
            valid_entities = []
            for entity in entities:
                if all(key in entity for key in ["name", "type"]) and entity["type"] in config.graph_store.SUPPORTED_ENTITY_TYPES:
                    valid_entities.append(entity)
            
            # Store relationships for later processing
            if hasattr(self, '_pending_relationships'):
                self._pending_relationships.extend(relationships)
            else:
                self._pending_relationships = relationships
            
            return valid_entities
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse entity response: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error parsing entities: {e}")
            return []
    
    async def create_entities_and_relationships(self, entities: List[Dict], memory_id: str, user_id: str):
        """
        Create entities and relationships in Neo4j graph.
        
        Args:
            entities: List of entity dictionaries
            memory_id: Associated memory ID
            user_id: User identifier
        """
        if not entities:
            return
        
        try:
            def _create_entities_tx(tx, entities_batch, memory_id, user_id):
                # Create memory node
                tx.run("""
                    MERGE (m:Memory {id: $memory_id})
                    SET m.user_id = $user_id, m.created_at = datetime(), m.updated_at = datetime()
                """, memory_id=memory_id, user_id=user_id)
                
                # Create entities and link to memory
                for entity in entities_batch:
                    tx.run("""
                        MERGE (e:Entity {name: $name})
                        SET e.type = $type, 
                            e.properties = $properties,
                            e.user_id = $user_id,
                            e.updated_at = datetime()
                        ON CREATE SET e.created_at = datetime()
                        WITH e
                        MATCH (m:Memory {id: $memory_id})
                        MERGE (e)-[:MENTIONED_IN]->(m)
                    """, 
                    name=entity["name"], 
                    type=entity["type"], 
                    properties=entity.get("properties", {}),
                    user_id=user_id,
                    memory_id=memory_id)
            
            # Process entities in batches
            batch_size = config.graph_store.ENTITY_EXTRACTION_BATCH_SIZE
            with self.driver.session() as session:
                for i in range(0, len(entities), batch_size):
                    batch = entities[i:i + batch_size]
                    session.execute_write(_create_entities_tx, batch, memory_id, user_id)
            
            # Create relationships if any pending
            if hasattr(self, '_pending_relationships') and self._pending_relationships:
                await self._create_relationships(self._pending_relationships, user_id)
                self._pending_relationships = []
            
            logger.info(f"Created {len(entities)} entities for memory {memory_id}")
            
        except Exception as e:
            logger.error(f"Failed to create entities in graph: {e}")
            raise
    
    async def _create_relationships(self, relationships: List[Dict], user_id: str):
        """Create relationships between entities."""
        def _create_relationships_tx(tx, relationships_batch, user_id):
            for rel in relationships_batch:
                if rel["type"] not in config.graph_store.SUPPORTED_RELATIONSHIP_TYPES:
                    continue
                
                tx.run(f"""
                    MATCH (a:Entity {{name: $from_name, user_id: $user_id}})
                    MATCH (b:Entity {{name: $to_name, user_id: $user_id}})
                    MERGE (a)-[r:{rel["type"]}]->(b)
                    SET r.properties = $properties, r.created_at = datetime()
                """, 
                from_name=rel["from"], 
                to_name=rel["to"], 
                properties=rel.get("properties", {}),
                user_id=user_id)
        
        batch_size = config.graph_store.ENTITY_EXTRACTION_BATCH_SIZE
        with self.driver.session() as session:
            for i in range(0, len(relationships), batch_size):
                batch = relationships[i:i + batch_size]
                session.execute_write(_create_relationships_tx, batch, user_id)
    
    async def find_related_memories(
        self, 
        memory_id: str, 
        user_id: str, 
        depth: int = 2
    ) -> List[str]:
        """
        Find memories connected through graph relationships.
        
        Args:
            memory_id: Starting memory ID
            user_id: User identifier
            depth: Maximum traversal depth
            
        Returns:
            List of related memory IDs
        """
        def _find_related_tx(tx, memory_id, user_id, depth):
            query = f"""
            MATCH (m:Memory {{id: $memory_id, user_id: $user_id}})<-[:MENTIONED_IN]-(e:Entity)
            MATCH (e)-[*1..{depth}]-(other_entity:Entity)-[:MENTIONED_IN]->(related:Memory {{user_id: $user_id}})
            WHERE related.id <> $memory_id
            RETURN DISTINCT related.id as related_id, 
                   COUNT(*) as connection_strength
            ORDER BY connection_strength DESC
            LIMIT 10
            """
            
            result = tx.run(query, memory_id=memory_id, user_id=user_id)
            return [(record["related_id"], record["connection_strength"]) for record in result]
        
        try:
            with self.driver.session() as session:
                results = session.execute_read(_find_related_tx, memory_id, user_id, min(depth, config.graph_store.MAX_GRAPH_TRAVERSAL_DEPTH))
                return [result[0] for result in results]
                
        except Exception as e:
            logger.error(f"Failed to find related memories: {e}")
            return []
    
    async def get_entity_relationships(
        self, 
        entity_name: str, 
        user_id: str
    ) -> Dict[str, Any]:
        """
        Get all relationships for a specific entity.
        
        Args:
            entity_name: Name of the entity
            user_id: User identifier
            
        Returns:
            Dictionary with entity relationships and metadata
        """
        def _get_relationships_tx(tx, entity_name, user_id):
            query = """
            MATCH (e:Entity {name: $entity_name, user_id: $user_id})
            OPTIONAL MATCH (e)-[r]->(other:Entity)
            OPTIONAL MATCH (incoming:Entity)-[r_in]->(e)
            RETURN e,
                   COLLECT(DISTINCT {target: other.name, type: type(r), properties: r.properties}) as outgoing,
                   COLLECT(DISTINCT {source: incoming.name, type: type(r_in), properties: r_in.properties}) as incoming
            """
            
            result = tx.run(query, entity_name=entity_name, user_id=user_id)
            record = result.single()
            
            if not record:
                return None
            
            return {
                "entity": dict(record["e"]),
                "outgoing_relationships": [rel for rel in record["outgoing"] if rel["target"]],
                "incoming_relationships": [rel for rel in record["incoming"] if rel["source"]]
            }
        
        try:
            with self.driver.session() as session:
                return session.execute_read(_get_relationships_tx, entity_name, user_id)
                
        except Exception as e:
            logger.error(f"Failed to get entity relationships: {e}")
            return None
    
    async def process_memory_for_graph(
        self, 
        memory_id: str, 
        content: str, 
        user_id: str
    ) -> Dict[str, Any]:
        """
        Complete processing of memory for graph integration.
        
        Args:
            memory_id: Memory identifier
            content: Memory content
            user_id: User identifier
            
        Returns:
            Processing results with entities and relationships
        """
        try:
            # Extract entities from content
            entities = await self.extract_entities(content, memory_id, user_id)
            
            # Create entities and relationships in graph
            await self.create_entities_and_relationships(entities, memory_id, user_id)
            
            # Find related memories
            related_memories = await self.find_related_memories(memory_id, user_id)
            
            return {
                "memory_id": memory_id,
                "entities_created": len(entities),
                "related_memories": related_memories,
                "processing_status": "success"
            }
            
        except Exception as e:
            logger.error(f"Failed to process memory for graph: {e}")
            return {
                "memory_id": memory_id,
                "entities_created": 0,
                "related_memories": [],
                "processing_status": "error",
                "error": str(e)
            }
    
    async def close(self):
        """Close Neo4j driver connection."""
        if self.driver:
            self.driver.close()
            logger.info("Closed Neo4j driver connection")