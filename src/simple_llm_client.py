"""
Simple LLM Client for Memory Operations

Provides a basic LLM interface for memory extraction and update operations.
"""

import os
import json
import logging
from typing import Optional
import httpx
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

class SimpleLLMClient:
    """
    Simple LLM client for memory operations.
    
    Currently supports basic text generation with configurable providers.
    """
    
    def __init__(self, provider: str = "mock", model: str = "gpt-3.5-turbo"):
        """
        Initialize LLM client.
        
        Args:
            provider: LLM provider (mock, openai, openrouter, etc.)
            model: Model name to use
        """
        self.provider = provider
        self.model = model
        self.api_key = os.getenv('LLM_API_KEY', '')
        self.base_url = os.getenv('LLM_BASE_URL', '')
        
        logger.info(f"Initialized SimpleLLMClient with provider: {provider}, model: {model}")
    
    async def generate(self, prompt: str) -> str:
        """
        Generate text response from prompt.
        
        Args:
            prompt: Input prompt
            
        Returns:
            Generated text response
        """
        try:
            if self.provider == "mock":
                return await self._mock_generate(prompt)
            elif self.provider == "openai":
                return await self._openai_generate(prompt)
            elif self.provider == "openrouter":
                return await self._openrouter_generate(prompt)
            else:
                logger.warning(f"Unsupported provider {self.provider}, using mock")
                return await self._mock_generate(prompt)
                
        except Exception as e:
            logger.error(f"Error in LLM generation: {e}")
            return ""
    
    async def _mock_generate(self, prompt: str) -> str:
        """Mock generation for testing purposes."""
        if "extract" in prompt.lower() and "memories" in prompt.lower():
            # Mock memory extraction
            return '["User prefers React over Vue for frontend development", "User is working on a memory system project"]'
        elif "operation" in prompt.lower() and "decision" in prompt.lower():
            # Mock operation decision
            return '{"operation": "ADD", "reasoning": "New information about user preferences"}'
        elif "summary" in prompt.lower():
            # Mock summary generation
            return "User is interested in frontend development, particularly React framework. Prefers React over Vue for projects."
        else:
            return "Mock LLM response for testing purposes."
    
    async def _openai_generate(self, prompt: str) -> str:
        """Generate using OpenAI API."""
        if not self.api_key:
            logger.warning("No OpenAI API key provided, using mock response")
            return await self._mock_generate(prompt)
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    "https://api.openai.com/v1/chat/completions",
                    headers={
                        "Authorization": f"Bearer {self.api_key}",
                        "Content-Type": "application/json"
                    },
                    json={
                        "model": self.model,
                        "messages": [{"role": "user", "content": prompt}],
                        "temperature": 0.1,  # Lower temperature for more consistent JSON
                        "max_tokens": 1000   # Reduced for faster response
                    },
                    timeout=15.0  # Reduced timeout for faster failure
                )
                
                if response.status_code == 200:
                    data = response.json()
                    return data["choices"][0]["message"]["content"]
                else:
                    logger.error(f"OpenAI API error: {response.status_code}")
                    return await self._mock_generate(prompt)
                    
        except Exception as e:
            logger.error(f"Error calling OpenAI API: {e}")
            return await self._mock_generate(prompt)
    
    async def _openrouter_generate(self, prompt: str) -> str:
        """Generate using OpenRouter API."""
        if not self.api_key:
            logger.warning("No OpenRouter API key provided, using mock response")
            return await self._mock_generate(prompt)
        
        if not self.base_url:
            logger.warning("No OpenRouter base URL provided, using mock response")
            return await self._mock_generate(prompt)
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    headers={
                        "Authorization": f"Bearer {self.api_key}",
                        "Content-Type": "application/json",
                        "HTTP-Referer": "https://mcp-mem0.local",  # OpenRouter requires this
                        "X-Title": "Spark Memory MCP Server"  # OpenRouter optional but recommended
                    },
                    json={
                        "model": self.model,
                        "messages": [{"role": "user", "content": prompt}],
                        "temperature": 0.1,  # Lower temperature for more consistent JSON
                        "max_tokens": 1000   # Reduced for faster response
                    },
                    timeout=15.0  # Reduced timeout for faster failure
                )
                
                if response.status_code == 200:
                    data = response.json()
                    return data["choices"][0]["message"]["content"]
                else:
                    logger.error(f"OpenRouter API error: {response.status_code} - {response.text}")
                    return await self._mock_generate(prompt)
                    
        except Exception as e:
            logger.error(f"Error calling OpenRouter API: {e}")
            return await self._mock_generate(prompt)

def get_spark_memory_client():
    """
    Get a simple LLM client for the Spark memory system.
    
    Returns:
        SimpleLLMClient instance
        
    Raises:
        ValueError: If required environment variables are not set
    """
    provider = os.getenv('LLM_PROVIDER')
    model = os.getenv('LLM_CHOICE')
    api_key = os.getenv('LLM_API_KEY')
    
    # Production safety: Fail fast if configuration is missing
    if not provider:
        raise ValueError("LLM_PROVIDER environment variable is required. Set to 'openai', 'openrouter', or 'ollama'")
    
    if provider != 'ollama' and not api_key:
        raise ValueError(f"LLM_API_KEY environment variable is required for provider: {provider}")
    
    if not model:
        raise ValueError("LLM_CHOICE environment variable is required. Specify the model name")
    
    # Only allow mock in explicit test environments
    if provider == 'mock':
        test_env = os.getenv('ENVIRONMENT', '').lower()
        if test_env not in ['test', 'development', 'dev']:
            raise ValueError("Mock LLM provider is only allowed in test/development environments")
        logger.warning("Using mock LLM provider - only suitable for testing")
    
    return SimpleLLMClient(provider=provider, model=model)