"""
Server Readiness Manager for Spark Memory MCP Server

Manages initialization phases and readiness state with MCP protocol awareness.
"""

import threading
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, Any

from config import get_config

logger = logging.getLogger(__name__)
config = get_config()


class ServerReadinessManager:
    """
    Enhanced server readiness manager with MCP protocol awareness.
    
    Manages initialization phases:
    1. Component initialization
    2. MCP protocol readiness 
    3. Service availability
    """

    def __init__(self):
        """Initialize the server readiness manager with configuration."""
        self._lock = threading.Lock()
        self._readiness_file = Path(config.server.READINESS_FILE_PATH)
        self._pending_connections = []
        
        # Initialization phases
        self._components_ready = False
        self._mcp_protocol_ready = False
        self._service_available = False
        
        # Initialization tracking
        self._startup_time = None
        self._initialization_stages = {
            "components": False,
            "mcp_protocol": False,
            "service": False
        }
        
        # Retry configuration from config
        self._max_retries = config.performance.MAX_RETRIES
        self._retry_delay = config.performance.RETRY_BASE_DELAY
        
        logger.info("Initialized enhanced ServerReadinessManager with MCP protocol awareness")

    def set_components_ready(self):
        """Mark components as initialized (BGE, database, etc.)."""
        with self._lock:
            self._components_ready = True
            self._initialization_stages["components"] = True
            logger.info("Components marked as ready")

    def set_mcp_protocol_ready(self):
        """Mark MCP protocol as fully initialized."""
        with self._lock:
            self._mcp_protocol_ready = True
            self._initialization_stages["mcp_protocol"] = True
            logger.info("MCP protocol marked as ready")

    def set_service_available(self):
        """Mark service as fully available for requests."""
        with self._lock:
            if self._components_ready and self._mcp_protocol_ready:
                self._service_available = True
                self._initialization_stages["service"] = True
                self._startup_time = datetime.now()
                
                # Write readiness to file for cross-process communication
                self._readiness_file.write_text(self._startup_time.isoformat())
                logger.info(f"Service marked as fully available at {self._startup_time}")
            else:
                logger.warning("Cannot mark service available - components or MCP protocol not ready")

    def is_ready(self) -> bool:
        """Check if service is fully ready for requests."""
        with self._lock:
            return self._service_available

    def is_components_ready(self) -> bool:
        """Check if components are initialized."""
        with self._lock:
            return self._components_ready

    def is_mcp_protocol_ready(self) -> bool:
        """Check if MCP protocol is ready."""
        with self._lock:
            return self._mcp_protocol_ready

    def get_status(self) -> Dict[str, Any]:
        """Get detailed readiness status."""
        with self._lock:
            return {
                "ready": self._service_available,
                "startup_time": self._startup_time.isoformat() if self._startup_time else None,
                "initialization_stages": self._initialization_stages.copy(),
                "pending_connections": len(self._pending_connections),
                "components_ready": self._components_ready,
                "mcp_protocol_ready": self._mcp_protocol_ready,
                "service_available": self._service_available
            }

    def should_retry_request(self, attempt: int) -> bool:
        """Check if request should be retried during initialization."""
        return attempt < self._max_retries and not self._service_available

    def get_retry_delay(self, attempt: int) -> float:
        """Get exponential backoff delay for retry."""
        return self._retry_delay * (config.performance.EXPONENTIAL_BACKOFF_MULTIPLIER ** attempt)

    def clear_readiness(self):
        """Clear readiness state (for testing/restart scenarios)."""
        with self._lock:
            self._components_ready = False
            self._mcp_protocol_ready = False
            self._service_available = False
            self._startup_time = None
            self._initialization_stages = {
                "components": False,
                "mcp_protocol": False,
                "service": False
            }
            
            if self._readiness_file.exists():
                self._readiness_file.unlink()
            
            logger.info("Cleared readiness state")

    def get_component_timeout(self) -> float:
        """Get the component initialization timeout."""
        return config.server.COMPONENT_INITIALIZATION_TIMEOUT
    
    def get_health_check_timeout(self) -> float:
        """Get the health check timeout."""
        return config.server.COMPONENT_HEALTH_TIMEOUT


# Global readiness manager instance
readiness_manager = ServerReadinessManager()


def get_readiness_manager() -> ServerReadinessManager:
    """Get the global readiness manager instance."""
    return readiness_manager


def create_readiness_manager() -> ServerReadinessManager:
    """Create a new readiness manager instance (useful for testing)."""
    return ServerReadinessManager()