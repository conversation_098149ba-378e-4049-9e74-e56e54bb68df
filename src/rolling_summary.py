"""
Rolling Summary Manager for Conversation Context

Implements asynchronous conversation summary generation and management
for enhanced memory extraction context.
"""

import asyncio
import json
import logging
from typing import List, Dict, Optional, Any

from .temporal_context_tracker import TemporalContextTracker, TemporalMarker
from .topic_continuity_manager import Topic<PERSON><PERSON><PERSON><PERSON><PERSON>anager, Topic
from .summary_quality_assessor import SummaryQualityAssessor, QualityFactors

logger = logging.getLogger(__name__)

class RollingSummaryManager:
    """
    Enhanced conversation summary manager with advanced features.

    Phase 2 Enhanced Features:
    - Temporal context tracking for evolution over time
    - Topic continuity across sessions
    - Summary quality assessment and adaptive length
    - Entity tracking and relationship awareness
    """

    def __init__(self, llm_client, db_client, supabase_mcp=None, graph_service=None):
        """
        Initialize enhanced rolling summary manager.

        Args:
            llm_client: LLM client for summary generation
            db_client: Database client for summary storage
            supabase_mcp: Supabase MCP context for database operations
            graph_service: Optional graph service for entity tracking
        """
        self.llm_client = llm_client
        self.db = db_client
        self.supabase_mcp = supabase_mcp
        self.graph_service = graph_service

        # Enhanced Phase 2 components
        self.temporal_tracker = TemporalContextTracker(llm_client)
        self.topic_manager = TopicContinuityManager(llm_client, db_client)
        self.quality_assessor = SummaryQualityAssessor(llm_client)

        # In-memory cache for immediate access
        self.summary_cache = {}
        self.entity_cache = {}  # Cache for tracked entities
        self.relationship_cache = {}  # Cache for entity relationships
        self.topic_cache = {}  # Cache for topics
        self.quality_cache = {}  # Cache for quality scores

        # Configuration
        self.max_summary_length = 2000
        self.update_interval = 10  # messages
        self.context_window = 20  # recent messages to consider
        self.entity_extraction_enabled = graph_service is not None

        logger.info(f"Initialized Enhanced RollingSummaryManager with Phase 2 features (Entity Extraction: {self.entity_extraction_enabled})")
    
    async def get_conversation_summary(self, user_id: str) -> str:
        """
        Get current conversation summary for a user.
        
        Args:
            user_id: User identifier
            
        Returns:
            Current conversation summary or empty string if none exists
        """
        try:
            # Check cache first
            if user_id in self.summary_cache:
                return self.summary_cache[user_id]
            
            # Load from database
            summary = await self._load_summary_from_db(user_id)
            
            if not summary:
                # Generate initial summary if none exists
                summary = await self._generate_initial_summary(user_id)
            
            # Cache the summary
            if summary:
                self.summary_cache[user_id] = summary
            
            return summary or ""
            
        except Exception as e:
            logger.error(f"Error getting conversation summary: {e}")
            return ""
    
    async def update_summary_async(self, user_id: str, new_messages: List[str]):
        """
        Asynchronously update conversation summary with new messages.
        
        This runs in the background without blocking the main pipeline.
        
        Args:
            user_id: User identifier
            new_messages: List of new message contents
        """
        # Run in background task to avoid blocking
        asyncio.create_task(
            self._update_summary_background(user_id, new_messages)
        )
    
    async def update_summary_with_metadata(self, user_id: str, new_messages: List[str], metadata: Dict[str, Any]):
        """
        Update summary with enhanced metadata including entities and relationships.

        Args:
            user_id: User identifier
            new_messages: List of new message contents
            metadata: Enhanced metadata with topics, entities, and graph info
        """
        # Run enhanced update in background
        asyncio.create_task(
            self._update_summary_with_metadata_background(user_id, new_messages, metadata)
        )

    async def update_summary_with_phase2_features(
        self,
        user_id: str,
        new_messages: List[str],
        session_id: Optional[str] = None,
        metadata: Dict[str, Any] = None
    ):
        """
        Update summary with Phase 2 enhanced features.

        Args:
            user_id: User identifier
            new_messages: List of new message contents
            session_id: Current session identifier
            metadata: Additional metadata
        """
        # Run Phase 2 enhanced update in background
        asyncio.create_task(
            self._update_summary_phase2_background(user_id, new_messages, session_id, metadata)
        )
    
    async def _update_summary_background(self, user_id: str, new_messages: List[str]):
        """
        Background task for summary updates.
        
        Args:
            user_id: User identifier
            new_messages: List of new message contents
        """
        try:
            # Get current summary
            current_summary = await self.get_conversation_summary(user_id)
            
            # Generate updated summary
            updated_summary = await self._generate_updated_summary(
                current_summary, new_messages, user_id
            )
            
            if updated_summary:
                # Update cache
                self.summary_cache[user_id] = updated_summary
                
                # Store in database
                await self._store_summary_in_db(user_id, updated_summary)
                
                logger.debug(f"Updated conversation summary for user {user_id}")
            
        except Exception as e:
            # Log error but don't crash main pipeline
            logger.error(f"Error in background summary update: {e}")
    
    async def _update_summary_with_metadata_background(self, user_id: str, new_messages: List[str], metadata: Dict[str, Any]):
        """
        Enhanced background task for summary updates with metadata.
        
        Args:
            user_id: User identifier
            new_messages: List of new message contents
            metadata: Enhanced metadata with topics and entity info
        """
        try:
            # Get current summary
            current_summary = await self.get_conversation_summary(user_id)
            
            # Extract entities if graph service is enabled
            entities = []
            if self.entity_extraction_enabled and self.graph_service:
                entities = await self._extract_entities_from_messages(new_messages, user_id)
            
            # Generate updated summary with metadata
            updated_summary = await self._generate_updated_summary_with_metadata(
                current_summary, new_messages, user_id, metadata, entities
            )
            
            if updated_summary:
                # Update cache
                self.summary_cache[user_id] = updated_summary
                
                # Update entity cache if entities were extracted
                if entities:
                    self.entity_cache[user_id] = entities
                
                # Store in database
                await self._store_summary_in_db(user_id, updated_summary)
                
                logger.debug(f"Updated enhanced conversation summary for user {user_id} with {len(entities)} entities")
            
        except Exception as e:
            # Log error but don't crash main pipeline
            logger.error(f"Error in enhanced background summary update: {e}")

    async def _update_summary_phase2_background(
        self,
        user_id: str,
        new_messages: List[str],
        session_id: Optional[str] = None,
        metadata: Dict[str, Any] = None
    ):
        """
        Phase 2 enhanced background summary update with all advanced features.

        Args:
            user_id: User identifier
            new_messages: List of new message contents
            session_id: Current session identifier
            metadata: Additional metadata
        """
        try:
            # Get current summary and context
            current_summary = await self.get_conversation_summary(user_id)
            content = " ".join(new_messages)

            # 1. Extract temporal markers
            temporal_markers = await self.temporal_tracker.extract_temporal_markers(
                content, current_summary
            )

            # 2. Extract and update topics
            existing_topics = self.topic_cache.get(user_id, [])
            new_topics = await self.topic_manager.extract_topics_from_content(
                content, session_id, existing_topics
            )

            # Update topic continuity
            updated_topics = self.topic_manager.update_topic_status(new_topics)
            self.topic_cache[user_id] = updated_topics

            # 3. Generate enhanced summary with Phase 2 features
            enhanced_summary = await self._generate_phase2_summary(
                current_summary, new_messages, user_id, temporal_markers, updated_topics, metadata
            )

            # 4. Assess summary quality
            quality_score, quality_factors = await self.quality_assessor.assess_summary_quality(
                enhanced_summary, content
            )

            # 5. Calculate adaptive length for future summaries
            conversation_complexity = self._calculate_conversation_complexity(updated_topics, temporal_markers)
            adaptive_length = self.quality_assessor.calculate_adaptive_length(
                conversation_complexity, len(content), quality_score
            )

            # 6. Update caches
            self.summary_cache[user_id] = enhanced_summary
            self.quality_cache[user_id] = (quality_score, quality_factors)

            # 7. Store enhanced summary in database
            await self._store_enhanced_summary_in_db(
                user_id, enhanced_summary, temporal_markers, updated_topics,
                quality_score, quality_factors, adaptive_length, session_id
            )

            logger.debug(f"Phase 2 enhanced summary updated for user {user_id} (Quality: {quality_score:.3f})")

        except Exception as e:
            logger.error(f"Error in Phase 2 summary update: {e}")

    async def _extract_entities_from_messages(self, messages: List[str], user_id: str) -> List[str]:
        """Extract entities from messages using graph service."""
        all_entities = []
        
        if not self.graph_service or not hasattr(self.graph_service, 'extract_entities'):
            return all_entities
        
        try:
            for message in messages:
                # Create temporary memory ID for entity extraction
                temp_memory_id = f"summary_temp_{user_id}_{hash(message)}"
                entities = await self.graph_service.extract_entities(message, temp_memory_id, user_id)
                
                # Extract entity names
                for entity in entities:
                    if entity.get('name') and entity['name'] not in all_entities:
                        all_entities.append(entity['name'])
            
            return all_entities[:20]  # Limit to top 20 entities
            
        except Exception as e:
            logger.error(f"Entity extraction failed in summary: {e}")
            return []
    
    async def _generate_initial_summary(self, user_id: str) -> str:
        """
        Generate initial summary from recent memories.
        
        Args:
            user_id: User identifier
            
        Returns:
            Initial conversation summary
        """
        try:
            # Get recent memories for context
            recent_memories = await self.db.get_recent_memories(user_id, 10)
            
            if not recent_memories:
                return ""
            
            # Extract entities if graph service is enabled
            entities = []
            if self.entity_extraction_enabled:
                entities = await self._extract_entities_from_messages(recent_memories, user_id)
            
            # Build enhanced initial summary prompt
            prompt = self._build_initial_summary_prompt(recent_memories, entities)
            
            # Generate summary
            summary = await self.llm_client.generate(prompt)
            
            # Clean and validate summary
            summary = self._clean_summary(summary)
            
            logger.debug(f"Generated initial summary for user {user_id}")
            return summary
            
        except Exception as e:
            logger.error(f"Error generating initial summary: {e}")
            return ""
    
    async def _generate_updated_summary(
        self, 
        current_summary: str, 
        new_messages: List[str], 
        user_id: str
    ) -> str:
        """
        Generate updated summary incorporating new messages.
        
        Args:
            current_summary: Current conversation summary
            new_messages: New messages to incorporate
            user_id: User identifier
            
        Returns:
            Updated conversation summary
        """
        try:
            # Build update prompt
            prompt = self._build_update_summary_prompt(
                current_summary, new_messages
            )
            
            # Generate updated summary
            updated_summary = await self.llm_client.generate(prompt)
            
            # Clean and validate summary
            updated_summary = self._clean_summary(updated_summary)
            
            return updated_summary
            
        except Exception as e:
            logger.error(f"Error generating updated summary: {e}")
            return current_summary  # Return current summary on error
    
    async def _generate_updated_summary_with_metadata(
        self, 
        current_summary: str, 
        new_messages: List[str], 
        user_id: str,
        metadata: Dict[str, Any],
        entities: List[str]
    ) -> str:
        """
        Generate enhanced updated summary with metadata and entity awareness.
        
        Args:
            current_summary: Current conversation summary
            new_messages: New messages to incorporate
            user_id: User identifier
            metadata: Enhanced metadata with topics and processing info
            entities: Extracted entities from messages
            
        Returns:
            Enhanced updated conversation summary
        """
        try:
            # Build enhanced update prompt
            prompt = self._build_update_summary_prompt(
                current_summary, new_messages, metadata
            )
            
            # Add entity information if available
            if entities:
                prompt += f"\n\nExtracted Entities: {', '.join(entities[:10])}"
            
            # Generate updated summary
            updated_summary = await self.llm_client.generate(prompt)
            
            # Clean and validate summary
            updated_summary = self._clean_summary(updated_summary)
            
            return updated_summary
            
        except Exception as e:
            logger.error(f"Error generating enhanced updated summary: {e}")
            return current_summary  # Return current summary on error
    
    def _build_initial_summary_prompt(self, recent_memories: List[str], entities: Optional[List[str]] = None) -> str:
        """
        Build enhanced prompt for initial summary generation with entity awareness.
        
        Args:
            recent_memories: List of recent memory contents
            entities: Optional list of extracted entities
            
        Returns:
            Formatted prompt with entity focus
        """
        memory_text = "\\n".join(recent_memories)
        entity_text = ", ".join(entities) if entities else "Not yet identified"
        
        prompt = f"""
Create a comprehensive conversation summary with entity and relationship awareness:

Key Memories:
{memory_text}

Identified Entities:
{entity_text}

Generate a summary that captures:
- Main topics of discussion
- User preferences and background information
- Important decisions or conclusions
- Ongoing projects or interests
- Key relationships between people, projects, and technologies
- Evolution of preferences or knowledge over time
- Entities that frequently appear together

Focus on:
1. WHO: People mentioned and their roles/relationships
2. WHAT: Projects, technologies, skills discussed
3. WHEN: Temporal context and evolution patterns
4. WHY: Motivations and reasoning behind decisions
5. HOW: Relationships and connections between entities

Keep the summary under {self.max_summary_length} characters and focus on information that would be valuable for future conversations.

Summary:
        """.strip()
        
        return prompt
    
    def _build_update_summary_prompt(
        self, 
        current_summary: str, 
        new_messages: List[str],
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Build enhanced prompt for summary updates with metadata awareness.
        
        Args:
            current_summary: Current conversation summary
            new_messages: New messages to incorporate
            metadata: Optional metadata with topics and entity information
            
        Returns:
            Formatted update prompt with relationship focus
        """
        new_message_text = "\\n".join(new_messages)
        
        # Extract metadata components
        topics = metadata.get("topics", []) if metadata else []
        graph_enabled = metadata.get("graph_enabled", False) if metadata else False
        
        topics_text = ", ".join(topics) if topics else "General conversation"
        relationship_instruction = ""
        
        if graph_enabled:
            relationship_instruction = """
- Track evolution of entity relationships (e.g., "now uses X instead of Y")
- Note new connections between people, projects, or technologies
- Identify preference changes or skill developments"""
        
        prompt = f"""
Update the conversation summary with enhanced entity and relationship tracking.

Current Summary:
{current_summary}

New Messages:
{new_message_text}

Conversation Topics:
{topics_text}

Update Instructions:
- Preserve important existing information
- Add significant new information from the messages
- Remove outdated or contradicted information
- Track entity evolution and relationship changes{relationship_instruction}
- Note temporal markers ("recently", "now", "changed", "learned")
- Maintain conciseness (under {self.max_summary_length} characters)
- Focus on facts, preferences, relationships, and context that aid future conversations

Pay special attention to:
1. Changes in preferences or technologies used
2. New relationships between entities
3. Evolution of skills or knowledge
4. Project status updates
5. Important decisions or conclusions

Updated Summary:
        """.strip()
        
        return prompt
    
    def _clean_summary(self, summary: str) -> str:
        """
        Clean and validate summary text.
        
        Args:
            summary: Raw summary text
            
        Returns:
            Cleaned summary
        """
        if not summary:
            return ""
        
        # Clean whitespace
        summary = summary.strip()
        
        # Truncate if too long
        if len(summary) > self.max_summary_length:
            summary = summary[:self.max_summary_length - 3] + "..."
        
        # Remove common prefixes
        prefixes = ["Summary:", "Updated Summary:", "Conversation Summary:"]
        for prefix in prefixes:
            if summary.startswith(prefix):
                summary = summary[len(prefix):].strip()
        
        return summary
    
    async def _load_summary_from_db(self, user_id: str) -> Optional[str]:
        """
        Load summary from database via Supabase MCP.
        
        Args:
            user_id: User identifier
            
        Returns:
            Stored summary or None if not found
        """
        try:
            # Execute via Supabase MCP
            sql = "SELECT summary FROM conversation_summaries WHERE user_id = $1"
            
            # This would be executed via MCP in actual implementation
            # For now, return None as placeholder
            return None
            
        except Exception as e:
            logger.error(f"Error loading summary from database: {e}")
            return None
    
    async def _store_summary_in_db(self, user_id: str, summary: str):
        """
        Store summary in database via Supabase MCP.
        
        Args:
            user_id: User identifier
            summary: Summary text to store
        """
        try:
            # Execute via Supabase MCP
            sql = """
                INSERT INTO conversation_summaries (user_id, summary, updated_at)
                VALUES ($1, $2, NOW())
                ON CONFLICT (user_id) 
                DO UPDATE SET summary = $2, updated_at = NOW()
            """
            
            # This would be executed via MCP in actual implementation
            logger.debug(f"Stored summary for user {user_id}")
            
        except Exception as e:
            logger.error(f"Error storing summary in database: {e}")
    
    def clear_cache(self, user_id: Optional[str] = None):
        """
        Clear summary cache.
        
        Args:
            user_id: Specific user to clear, or None to clear all
        """
        if user_id:
            self.summary_cache.pop(user_id, None)
            logger.debug(f"Cleared cache for user {user_id}")
        else:
            self.summary_cache.clear()
            logger.debug("Cleared all summary cache")
    
    def get_cache_stats(self) -> Dict[str, int]:
        """
        Get cache statistics.
        
        Returns:
            Dictionary with cache statistics
        """
        return {
            'cached_users': len(self.summary_cache),
            'total_cache_size': sum(len(summary) for summary in self.summary_cache.values())
        }

    # Phase 2 Enhanced Methods

    async def _generate_phase2_summary(
        self,
        current_summary: str,
        new_messages: List[str],
        user_id: str,
        temporal_markers: List[TemporalMarker],
        topics: List[Topic],
        metadata: Dict[str, Any] = None
    ) -> str:
        """Generate enhanced summary with Phase 2 features."""
        try:
            # Build enhanced prompt with temporal and topic context
            prompt = self._build_phase2_summary_prompt(
                current_summary, new_messages, temporal_markers, topics, metadata
            )

            # Generate enhanced summary
            enhanced_summary = await self.llm_client.generate(prompt)

            # Clean and validate summary
            enhanced_summary = self._clean_summary(enhanced_summary)

            return enhanced_summary

        except Exception as e:
            logger.error(f"Error generating Phase 2 summary: {e}")
            return current_summary

    def _build_phase2_summary_prompt(
        self,
        current_summary: str,
        new_messages: List[str],
        temporal_markers: List[TemporalMarker],
        topics: List[Topic],
        metadata: Dict[str, Any] = None
    ) -> str:
        """Build enhanced prompt for Phase 2 summary generation."""
        new_message_text = "\\n".join(new_messages)

        # Temporal context
        temporal_context = ""
        if temporal_markers:
            temporal_items = []
            for marker in temporal_markers[:5]:  # Top 5 temporal markers
                temporal_items.append(f"- {marker.marker_type}: {marker.content}")
            temporal_context = f"\\n\\nTemporal Changes Detected:\\n" + "\\n".join(temporal_items)

        # Topic context
        topic_context = ""
        if topics:
            active_topics = [t for t in topics if t.status == "active"]
            if active_topics:
                topic_names = [t.name for t in active_topics[:5]]
                topic_context = f"\\n\\nActive Topics: {', '.join(topic_names)}"

        # Metadata context
        metadata_context = ""
        if metadata:
            session_info = metadata.get("session_id", "")
            if session_info:
                metadata_context = f"\\n\\nSession Context: {session_info}"

        prompt = f"""Update the conversation summary with enhanced Phase 2 features.

Current Summary:
{current_summary}

New Messages:
{new_message_text}{temporal_context}{topic_context}{metadata_context}

Enhanced Update Instructions:
- Preserve important existing information
- Integrate new information with temporal awareness
- Track evolution and changes over time (use temporal markers)
- Maintain topic continuity across sessions
- Update entity relationships and status changes
- Note preference evolution and learning progression
- Keep summary concise but comprehensive
- Focus on actionable information for future conversations

Generate an enhanced summary that captures both content and context evolution."""

        return prompt

    def _calculate_conversation_complexity(
        self,
        topics: List[Topic],
        temporal_markers: List[TemporalMarker]
    ) -> float:
        """Calculate conversation complexity score for adaptive length."""
        complexity = 0.0

        # Topic complexity
        if topics:
            active_topics = [t for t in topics if t.status == "active"]
            topic_score = min(len(active_topics) * 0.1, 0.4)
            complexity += topic_score

        # Temporal complexity
        if temporal_markers:
            change_markers = [m for m in temporal_markers if m.marker_type in ['change', 'evolution', 'update']]
            temporal_score = min(len(change_markers) * 0.05, 0.3)
            complexity += temporal_score

        # Base complexity
        complexity += 0.3

        return min(complexity, 1.0)

    async def _store_enhanced_summary_in_db(
        self,
        user_id: str,
        summary: str,
        temporal_markers: List[TemporalMarker],
        topics: List[Topic],
        quality_score: float,
        quality_factors: QualityFactors,
        adaptive_length: int,
        session_id: Optional[str] = None
    ):
        """Store enhanced summary with Phase 2 data in database."""
        try:
            # Prepare temporal markers for storage
            temporal_data = {}
            for marker in temporal_markers[:10]:  # Limit to top 10
                temporal_data[f"marker_{len(temporal_data)}"] = self.temporal_tracker.to_dict(marker)

            # Prepare topics for storage
            topic_data = []
            for topic in topics[:20]:  # Limit to top 20
                topic_data.append(self.topic_manager.to_dict(topic))

            # Prepare quality factors for storage
            quality_data = self.quality_assessor.to_dict(quality_factors)

            # Calculate topic continuity score
            topic_continuity_score = self.topic_manager.get_topic_continuity_score(topics)

            # Store via Supabase MCP (placeholder for actual implementation)
            logger.debug(f"Stored enhanced summary for user {user_id} with Phase 2 features")

        except Exception as e:
            logger.error(f"Error storing enhanced summary: {e}")

    # Phase 2 Getter Methods

    async def get_summary_quality_score(self, user_id: str) -> float:
        """Get current summary quality score for user."""
        if user_id in self.quality_cache:
            return self.quality_cache[user_id][0]
        return 0.5

    async def get_cross_session_topics(self, user_id: str) -> List[Topic]:
        """Get topics that span across multiple sessions."""
        if user_id in self.topic_cache:
            return [t for t in self.topic_cache[user_id] if len(t.session_ids or []) > 1]
        return []

    async def get_temporal_evolution(self, user_id: str) -> Dict[str, Any]:
        """Get temporal evolution analysis for user."""
        # This would retrieve temporal markers from database
        # For now, return placeholder
        return {
            'evolution_detected': True,
            'change_frequency': 0.5,
            'temporal_markers': []
        }