"""
Confidence Calculator for Memory Quality Assessment

This module implements confidence scoring for memories based on:
- Content certainty and specificity
- Source reliability 
- Temporal factors
- Validation history

Phase 2 Feature 2/3: Confidence Scores
"""

import json
import logging
import re
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class ConfidenceFactors:
    """Factors that contribute to confidence score calculation."""
    certainty_score: float = 0.5
    specificity_score: float = 0.5
    source_reliability: float = 0.5
    temporal_factor: float = 0.5
    content_quality: float = 0.5
    validation_boost: float = 0.0
    reasoning: str = ""

class ConfidenceCalculator:
    """
    Calculates and manages confidence scores for memories.
    
    Uses LLM-based analysis combined with heuristic rules to assess
    memory quality and reliability.
    """
    
    def __init__(self, llm_client):
        """
        Initialize the confidence calculator.
        
        Args:
            llm_client: LLM client for content analysis
        """
        self.llm_client = llm_client
        self.certainty_patterns = {
            'high': [
                r'\b(definitely|certainly|absolutely|clearly|obviously|undoubtedly)\b',
                r'\b(I am sure|I know|I remember|I always|I never)\b',
                r'\b(fact|truth|confirmed|verified)\b'
            ],
            'medium': [
                r'\b(probably|likely|usually|generally|typically)\b',
                r'\b(I think|I believe|I feel|seems like)\b',
                r'\b(often|sometimes|frequently)\b'
            ],
            'low': [
                r'\b(maybe|perhaps|possibly|might|could be|uncertain)\b',
                r'\b(I guess|I suppose|not sure|unclear)\b',
                r'\b(rarely|seldom|occasionally)\b'
            ]
        }
        
        self.specificity_indicators = {
            'high': [
                r'\b\d{4}-\d{2}-\d{2}\b',  # Dates
                r'\b\d{1,2}:\d{2}\b',      # Times
                r'\b\$\d+\b',              # Money amounts
                r'\b\d+%\b',               # Percentages
                r'\b[A-Z][a-z]+ [A-Z][a-z]+\b'  # Proper names
            ],
            'medium': [
                r'\b(yesterday|today|tomorrow|last week|next month)\b',
                r'\b(morning|afternoon|evening|night)\b',
                r'\b(expensive|cheap|large|small|fast|slow)\b'
            ]
        }
        
    async def calculate_initial_confidence(
        self, 
        memory_content: str, 
        extraction_context: str = "",
        source_type: str = "user_input"
    ) -> Tuple[float, ConfidenceFactors]:
        """
        Calculate initial confidence score for a new memory.
        
        Args:
            memory_content: The memory text content
            extraction_context: Context from which memory was extracted
            source_type: Type of source (user_input, inferred, system_generated)
            
        Returns:
            Tuple of (confidence_score, confidence_factors)
        """
        try:
            # Initialize factors
            factors = ConfidenceFactors()
            
            # 1. Analyze certainty using patterns
            factors.certainty_score = self._analyze_certainty(memory_content)
            
            # 2. Analyze specificity
            factors.specificity_score = self._analyze_specificity(memory_content)
            
            # 3. Source reliability
            factors.source_reliability = self._get_source_reliability(source_type)
            
            # 4. Content quality (length, coherence)
            factors.content_quality = self._analyze_content_quality(memory_content)
            
            # 5. LLM-based analysis for nuanced assessment
            llm_analysis = await self._llm_confidence_analysis(memory_content, extraction_context)
            
            # Combine all factors
            base_score = (
                factors.certainty_score * 0.25 +
                factors.specificity_score * 0.20 +
                factors.source_reliability * 0.25 +
                factors.content_quality * 0.15 +
                llm_analysis.get('confidence_score', 0.5) * 0.15
            )
            
            # Apply temporal factor (new memories get slight boost)
            factors.temporal_factor = 1.0
            final_score = min(base_score * factors.temporal_factor, 1.0)
            
            # Build reasoning
            factors.reasoning = self._build_reasoning(factors, llm_analysis)
            
            logger.info(f"Calculated confidence {final_score:.3f} for memory: {memory_content[:50]}...")
            
            return final_score, factors
            
        except Exception as e:
            logger.error(f"Error calculating confidence: {e}")
            # Return default confidence on error
            default_factors = ConfidenceFactors(reasoning=f"Error in calculation: {str(e)}")
            return 0.5, default_factors
    
    def _analyze_certainty(self, content: str) -> float:
        """Analyze certainty indicators in the content."""
        content_lower = content.lower()
        
        # Count pattern matches
        high_count = sum(len(re.findall(pattern, content_lower)) for pattern in self.certainty_patterns['high'])
        medium_count = sum(len(re.findall(pattern, content_lower)) for pattern in self.certainty_patterns['medium'])
        low_count = sum(len(re.findall(pattern, content_lower)) for pattern in self.certainty_patterns['low'])
        
        total_indicators = high_count + medium_count + low_count
        
        if total_indicators == 0:
            return 0.5  # Neutral if no indicators
        
        # Weight the indicators
        weighted_score = (high_count * 0.9 + medium_count * 0.6 + low_count * 0.2) / total_indicators
        return min(weighted_score, 1.0)
    
    def _analyze_specificity(self, content: str) -> float:
        """Analyze specificity of the content."""
        high_count = sum(len(re.findall(pattern, content)) for pattern in self.specificity_indicators['high'])
        medium_count = sum(len(re.findall(pattern, content.lower())) for pattern in self.specificity_indicators['medium'])
        
        # Base score on content length and specific details
        length_score = min(len(content) / 200, 1.0)  # Longer content tends to be more specific
        detail_score = min((high_count * 0.3 + medium_count * 0.1), 0.5)
        
        return min(length_score * 0.7 + detail_score * 0.3, 1.0)
    
    def _get_source_reliability(self, source_type: str) -> float:
        """Get reliability score based on source type."""
        reliability_map = {
            'user_input': 0.8,
            'direct_statement': 0.9,
            'inferred': 0.5,
            'system_generated': 0.3,
            'extracted': 0.6
        }
        return reliability_map.get(source_type, 0.5)
    
    def _analyze_content_quality(self, content: str) -> float:
        """Analyze overall content quality."""
        # Basic quality indicators
        word_count = len(content.split())
        
        # Quality factors
        length_quality = min(word_count / 20, 1.0)  # Optimal around 20 words
        if word_count < 3:
            length_quality = 0.2  # Very short content is low quality
        
        # Check for coherence (basic heuristics)
        coherence_score = 0.8  # Default assumption of coherence
        if content.count('?') > 2:  # Too many questions might indicate uncertainty
            coherence_score -= 0.2
        
        return min(length_quality * 0.6 + coherence_score * 0.4, 1.0)
    
    async def _llm_confidence_analysis(self, content: str, context: str) -> Dict[str, Any]:
        """Use LLM to analyze confidence factors."""
        try:
            prompt = f"""Analyze this memory content and assess its confidence level from 0.0 to 1.0:

Memory: "{content}"
Context: "{context}"

Consider:
1. Certainty of the statement (definitive vs uncertain language)
2. Factual content vs opinions/preferences  
3. Specificity and detail level
4. Source reliability indicators

Respond with JSON only:
{{"confidence_score": 0.X, "reasoning": "brief explanation", "key_factors": ["factor1", "factor2"]}}"""

            response = await self.llm_client.generate(prompt)
            
            # Try to parse JSON response
            try:
                return json.loads(response)
            except json.JSONDecodeError:
                # Extract confidence score if JSON parsing fails
                import re
                score_match = re.search(r'"confidence_score":\s*([0-9.]+)', response)
                if score_match:
                    return {"confidence_score": float(score_match.group(1)), "reasoning": "LLM analysis"}
                
        except Exception as e:
            logger.warning(f"LLM confidence analysis failed: {e}")
        
        return {"confidence_score": 0.5, "reasoning": "LLM analysis unavailable"}
    
    def _build_reasoning(self, factors: ConfidenceFactors, llm_analysis: Dict[str, Any]) -> str:
        """Build human-readable reasoning for the confidence score."""
        reasoning_parts = []
        
        if factors.certainty_score > 0.7:
            reasoning_parts.append("high certainty language")
        elif factors.certainty_score < 0.4:
            reasoning_parts.append("uncertain language")
        
        if factors.specificity_score > 0.7:
            reasoning_parts.append("specific details")
        elif factors.specificity_score < 0.4:
            reasoning_parts.append("vague content")
        
        if factors.source_reliability > 0.7:
            reasoning_parts.append("reliable source")
        elif factors.source_reliability < 0.5:
            reasoning_parts.append("uncertain source")
        
        llm_reasoning = llm_analysis.get('reasoning', '')
        if llm_reasoning:
            reasoning_parts.append(f"LLM: {llm_reasoning}")
        
        return "; ".join(reasoning_parts) if reasoning_parts else "standard assessment"
    
    async def update_confidence_for_access(self, memory_id: str, access_type: str = "search") -> Optional[float]:
        """
        Update confidence score when memory is accessed.
        
        Args:
            memory_id: ID of the accessed memory
            access_type: Type of access (search, retrieval, validation)
            
        Returns:
            New confidence score or None if update failed
        """
        boost_amounts = {
            'search': 0.02,
            'retrieval': 0.05,
            'validation': 0.10,
            'confirmation': 0.15
        }
        
        boost = boost_amounts.get(access_type, 0.02)
        
        try:
            # This would typically call the database function
            # For now, return the boost amount as a placeholder
            logger.info(f"Boosting confidence for memory {memory_id} by {boost} due to {access_type}")
            return boost
        except Exception as e:
            logger.error(f"Failed to update confidence for memory {memory_id}: {e}")
            return None
    
    def to_dict(self, factors: ConfidenceFactors) -> Dict[str, Any]:
        """Convert confidence factors to dictionary for storage."""
        return {
            'certainty_score': factors.certainty_score,
            'specificity_score': factors.specificity_score,
            'source_reliability': factors.source_reliability,
            'temporal_factor': factors.temporal_factor,
            'content_quality': factors.content_quality,
            'validation_boost': factors.validation_boost,
            'reasoning': factors.reasoning,
            'calculated_at': datetime.now().isoformat()
        }
