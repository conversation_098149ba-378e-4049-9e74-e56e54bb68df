"""
Uvicorn wrapper for FastMCP to enable hot reload in development.

This wrapper allows the MCP server to work with uvicorn's --reload flag.
"""

import os
import asyncio
from contextlib import asynccontextmanager
from fastapi import FastAPI, Request, Response
from fastapi.responses import StreamingResponse
import logging

# Import the MCP server
from main_new import mcp, logger

# Create FastAPI app wrapper
app = FastAPI(title="Spark Memory MCP Server")

@app.get("/")
async def root():
    """Root endpoint for health checks."""
    return {"service": "spark-mcp-server", "status": "ok"}

@app.get("/health")
async def health():
    """Health check endpoint."""
    # Use the existing health check from MCP
    health_tool = next((t for t in mcp._tools if t.name == "health_check"), None)
    if health_tool:
        result = await health_tool.handler()
        return Response(content=result, media_type="application/json")
    return {"status": "ok"}

@app.post("/mcp")
async def mcp_endpoint(request: Request):
    """Main MCP endpoint for SSE transport."""
    body = await request.body()
    
    async def event_generator():
        """Generate SSE events from MCP."""
        # This is a simplified implementation
        # In production, you'd properly handle the MCP protocol
        yield f"data: {{'type': 'message', 'content': 'MCP endpoint'}}\n\n"
    
    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",
        }
    )

# For development, expose the MCP tools as REST endpoints
@app.post("/tools/{tool_name}")
async def tool_endpoint(tool_name: str, request: Request):
    """Execute MCP tool via REST for development/testing."""
    try:
        # Find the tool
        tool = next((t for t in mcp._tools if t.name == tool_name), None)
        if not tool:
            return {"error": f"Tool '{tool_name}' not found"}, 404
        
        # Get request data
        data = await request.json()
        
        # Execute tool
        result = await tool.handler(**data)
        
        # Return result
        if isinstance(result, str):
            try:
                import json
                return json.loads(result)
            except:
                return {"result": result}
        return result
        
    except Exception as e:
        logger.error(f"Tool execution error: {e}")
        return {"error": str(e)}, 500

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifecycle."""
    # Initialize MCP server
    logger.info("Starting MCP server in development mode")
    
    # The MCP server should already be initialized via its decorators
    # Just need to ensure it's ready
    
    yield
    
    # Cleanup
    logger.info("Shutting down MCP server")

# Set lifespan
app.router.lifespan_context = lifespan

if __name__ == "__main__":
    import uvicorn
    
    port = int(os.getenv("PORT", "8050"))
    uvicorn.run(
        "main_dev:app",
        host="0.0.0.0",
        port=port,
        reload=True,
        reload_dirs=["src"],
        log_level="debug" if os.getenv("ENVIRONMENT") == "development" else "info"
    )
