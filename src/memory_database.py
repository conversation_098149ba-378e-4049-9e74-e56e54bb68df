"""
Memory Database with PostgreSQL + pgvector integration

Implements vector similarity search with L2 distance for BGE embeddings.
Uses enhanced database service with connection pooling for production performance.
"""

import os
import json
from typing import List, Dict, Any, Optional
import logging
from datetime import datetime
from enhanced_database_service import EnhancedDatabaseService

logger = logging.getLogger(__name__)

class MemoryDatabase:
    """
    Production-ready database with pgvector for semantic memory storage.
    
    Optimized for BGE embeddings (768 dimensions) with L2 distance similarity search.
    Uses enhanced database service with connection pooling and vector indexes.
    """
    
    def __init__(self, supabase_mcp=None, database_url: Optional[str] = None,
                 min_connections: int = 5, max_connections: int = 20, 
                 graph_service=None, llm_client=None):
        """
        Initialize database with enhanced service and optional graph integration.
        
        Args:
            supabase_mcp: Legacy MCP context (deprecated, use database_url)
            database_url: PostgreSQL connection URL for enhanced service
            min_connections: Minimum connection pool size
            max_connections: Maximum connection pool size
            graph_service: Optional graph memory service for relationship tracking
            llm_client: Optional LLM client for graph operations
        """
        # Initialize enhanced database service with reduced defaults for development
        self.db_service = EnhancedDatabaseService(
            database_url=database_url,
            min_connections=min_connections,
            max_connections=max_connections
        )
        
        # Keep legacy MCP for backward compatibility
        self.supabase_mcp = supabase_mcp
        
        # Graph memory integration
        self.graph_service = graph_service
        self.llm_client = llm_client
        self._graph_enabled = graph_service is not None
        
        logger.info(f"Initialized MemoryDatabase with EnhancedDatabaseService (Graph: {'enabled' if self._graph_enabled else 'disabled'})")
    
    async def initialize(self):
        """
        Initialize database with enhanced service, schema, indexes, and graph service.
        
        Initializes both PostgreSQL database and Neo4j graph service if enabled.
        """
        try:
            # Initialize PostgreSQL database service
            await self.db_service.initialize()
            
            # Initialize graph service if enabled
            if self._graph_enabled and self.graph_service:
                try:
                    await self.graph_service.initialize()
                    logger.info("Graph memory service initialized successfully")
                except Exception as e:
                    logger.warning(f"Graph service initialization failed, disabling graph features: {e}")
                    self._graph_enabled = False
                    self.graph_service = None
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise
    
    async def _execute_sql(self, sql: str, params: List = None):
        """
        Execute SQL using enhanced database service.
        
        Args:
            sql: SQL query to execute
            params: Optional parameters for the query
            
        Returns:
            Query results
        """
        return await self.db_service.execute_sql(sql, params)
    
    async def store_memory(
        self,
        user_id: str,
        content: str,
        embedding: List[float],
        metadata: Optional[Dict[str, Any]] = None,
        session_id: Optional[str] = None,
        confidence_score: Optional[float] = None,
        confidence_factors: Optional[Dict[str, Any]] = None
    ) -> int:
        """
        Store a memory with its embedding using enhanced database service.

        Args:
            user_id: User identifier
            content: Memory content text
            embedding: BGE embedding vector (768 dimensions)
            metadata: Optional metadata dictionary
            session_id: Optional session identifier for session-based tracking
            confidence_score: Optional confidence score (0.0-1.0)
            confidence_factors: Optional confidence calculation factors

        Returns:
            ID of the stored memory
        """
        return await self.db_service.store_memory(
            user_id, content, embedding, metadata, session_id,
            confidence_score, confidence_factors
        )
    
    async def similarity_search(
        self,
        embedding: List[float],
        user_id: str,
        threshold: float = 0.7,
        limit: int = 5,
        session_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Perform optimized vector similarity search using HNSW index.

        Args:
            embedding: Query embedding vector
            user_id: User identifier for filtering
            threshold: Distance threshold for similarity
            limit: Maximum number of results
            session_id: Optional session filter

        Returns:
            List of similar memories with distance scores
        """
        return await self.db_service.similarity_search(embedding, user_id, threshold, limit, session_id)
    
    async def get_all_memories(self, user_id: str, limit: int = 50, offset: int = 0, session_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get all memories for a user with optimized pagination.

        Args:
            user_id: User identifier
            limit: Maximum number of memories to return
            offset: Number of memories to skip
            session_id: Optional session filter

        Returns:
            List of all user memories
        """
        return await self.db_service.get_all_memories(user_id, limit, offset, session_id)

    async def get_session_memories(self, session_id: str, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get all memories for a specific session.

        Args:
            session_id: Session identifier
            limit: Maximum number of memories to return
            offset: Number of memories to skip

        Returns:
            List of session memories
        """
        sql = """
            SELECT id, user_id, content, metadata, created_at, updated_at, session_id
            FROM memories
            WHERE session_id = $1
            ORDER BY created_at DESC
            LIMIT $2 OFFSET $3
        """
        return await self._execute_sql(sql, [session_id, limit, offset])
    
    async def delete_all_memories(self, user_id: str) -> int:
        """
        Delete all memories for a user.
        
        Args:
            user_id: User identifier
            
        Returns:
            Number of deleted memories
        """
        return await self.db_service.delete_all_memories(user_id)
    
    async def update_memory(
        self, 
        memory_id: int, 
        content: str, 
        embedding: List[float], 
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        Update an existing memory.
        
        Args:
            memory_id: Memory ID to update
            content: New content
            embedding: New embedding
            metadata: New metadata
        """
        await self.db_service.update_memory(memory_id, content, embedding, metadata)
    
    async def delete_memory(self, memory_id: int):
        """
        Delete a specific memory.
        
        Args:
            memory_id: Memory ID to delete
        """
        await self.db_service.delete_memory(memory_id)
    
    async def store_conversation_summary(self, user_id: str, summary: str):
        """
        Store or update conversation summary for a user.
        
        Args:
            user_id: User identifier
            summary: Conversation summary text
        """
        await self.db_service.store_conversation_summary(user_id, summary)
    
    async def get_conversation_summary(self, user_id: str) -> Optional[str]:
        """
        Get conversation summary for a user.
        
        Args:
            user_id: User identifier
            
        Returns:
            Conversation summary or None if not found
        """
        return await self.db_service.get_conversation_summary(user_id)
    
    async def get_recent_memories(self, user_id: str, limit: int = 10) -> List[str]:
        """
        Get recent memory contents for context.
        
        Args:
            user_id: User identifier
            limit: Number of recent memories to retrieve
            
        Returns:
            List of recent memory content strings
        """
        return await self.db_service.get_recent_memories(user_id, limit)
    
    async def close(self):
        """Close database connections and cleanup resources."""
        await self.db_service.close()
        logger.info("Database connections closed")
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get database performance metrics."""
        return await self.db_service.get_metrics()
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform database health check."""
        return await self.db_service.health_check()
    
    # Graph-Enhanced Methods
    
    async def store_memory_with_graph(
        self, 
        user_id: str, 
        content: str, 
        embedding: List[float], 
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Store memory with graph processing for relationship tracking.
        
        Args:
            user_id: User identifier
            content: Memory content text
            embedding: BGE embedding vector (768 dimensions)
            metadata: Optional metadata dictionary
            
        Returns:
            Dictionary with memory_id and graph processing results
        """
        # Store memory in PostgreSQL first
        memory_id = await self.store_memory(user_id, content, embedding, metadata)
        
        # Process for graph if enabled
        graph_result = None
        if self._graph_enabled and self.graph_service:
            try:
                graph_result = await self.graph_service.process_memory_for_graph(
                    str(memory_id), content, user_id
                )
                logger.debug(f"Graph processing completed for memory {memory_id}")
            except Exception as e:
                logger.warning(f"Graph processing failed for memory {memory_id}: {e}")
                graph_result = {"processing_status": "failed", "error": str(e)}
        
        return {
            "memory_id": memory_id,
            "content": content,
            "user_id": user_id,
            "graph_processing": graph_result,
            "graph_enabled": self._graph_enabled
        }
    
    async def similarity_search_with_graph(
        self, 
        embedding: List[float], 
        user_id: str, 
        threshold: float = 0.7, 
        limit: int = 5,
        include_graph_relations: bool = True
    ) -> List[Dict[str, Any]]:
        """
        Enhanced search combining vector similarity and graph relationships.
        
        Args:
            embedding: Query embedding vector
            user_id: User identifier for filtering
            threshold: Distance threshold for similarity
            limit: Maximum number of results
            include_graph_relations: Whether to include graph relationship data
            
        Returns:
            List of similar memories enhanced with graph relationships
        """
        # Get vector similarity results
        vector_results = await self.similarity_search(embedding, user_id, threshold, limit)
        
        if include_graph_relations and self._graph_enabled and self.graph_service:
            try:
                # Enhance with graph relationships
                for result in vector_results:
                    memory_id = str(result['id'])
                    
                    # Find related memories through graph
                    related_ids = await self.graph_service.find_related_memories(
                        memory_id, user_id, depth=2
                    )
                    
                    result['related_memories'] = related_ids
                    result['relationship_score'] = len(related_ids) * 0.1  # Boost score based on connections
                    result['graph_enhanced'] = True
                
                # Re-rank based on combined score (vector similarity + relationship strength)
                vector_results.sort(
                    key=lambda x: x['distance'] - x.get('relationship_score', 0)
                )
                
                logger.debug(f"Enhanced {len(vector_results)} results with graph relationships")
                
            except Exception as e:
                logger.warning(f"Graph enhancement failed, returning vector results only: {e}")
                for result in vector_results:
                    result['graph_enhanced'] = False
        else:
            for result in vector_results:
                result['graph_enhanced'] = False
        
        return vector_results
    
    async def get_entity_context(
        self, 
        entity_name: str, 
        user_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get complete context for an entity including relationships.
        
        Args:
            entity_name: Name of the entity
            user_id: User identifier
            
        Returns:
            Entity context with relationships or None if not found
        """
        if not self._graph_enabled or not self.graph_service:
            return None
        
        try:
            return await self.graph_service.get_entity_relationships(entity_name, user_id)
        except Exception as e:
            logger.error(f"Failed to get entity context for {entity_name}: {e}")
            return None
    
    async def find_memory_relationships(
        self, 
        memory_id: str, 
        user_id: str, 
        depth: int = 2
    ) -> List[str]:
        """
        Find memories related through graph relationships.
        
        Args:
            memory_id: Starting memory ID
            user_id: User identifier
            depth: Maximum traversal depth
            
        Returns:
            List of related memory IDs
        """
        if not self._graph_enabled or not self.graph_service:
            return []
        
        try:
            return await self.graph_service.find_related_memories(memory_id, user_id, depth)
        except Exception as e:
            logger.error(f"Failed to find memory relationships: {e}")
            return []
    
    async def close_with_graph(self):
        """Close database and graph connections."""
        # Close PostgreSQL connections
        await self.close()
        
        # Close graph service connections
        if self._graph_enabled and self.graph_service:
            try:
                await self.graph_service.close()
                logger.info("Graph service connections closed")
            except Exception as e:
                logger.warning(f"Error closing graph service: {e}")
    
    def is_graph_enabled(self) -> bool:
        """Check if graph memory features are enabled."""
        return self._graph_enabled