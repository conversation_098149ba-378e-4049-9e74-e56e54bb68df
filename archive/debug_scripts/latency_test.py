#!/usr/bin/env python3
"""
Latency Performance Test for Memory Extraction Pipeline
Measures P95 latency and identifies bottlenecks.
"""

import asyncio
import time
import statistics
import json
import httpx
from typing import List, Dict, Any

# Configuration
MCP_SERVER_URL = "http://localhost:8050"
TEST_USER_ID = "latency_test"
NUM_TESTS = 20

async def measure_add_memory_latency(client: httpx.AsyncClient, text: str) -> Dict[str, Any]:
    """Measure latency for adding a memory"""
    start_time = time.time()
    
    try:
        response = await client.post(
            f"{MCP_SERVER_URL}/tools/add_memories",
            json={
                "text": text,
                "user_id": TEST_USER_ID
            },
            timeout=30.0
        )
        
        end_time = time.time()
        latency_ms = (end_time - start_time) * 1000
        
        success = response.status_code == 200
        result = response.json() if success else {"error": response.text}
        
        return {
            "latency_ms": latency_ms,
            "success": success,
            "response": result,
            "status_code": response.status_code
        }
        
    except Exception as e:
        end_time = time.time()
        latency_ms = (end_time - start_time) * 1000
        return {
            "latency_ms": latency_ms,
            "success": False,
            "error": str(e),
            "status_code": None
        }

async def measure_search_memory_latency(client: httpx.AsyncClient, query: str) -> Dict[str, Any]:
    """Measure latency for searching memories"""
    start_time = time.time()
    
    try:
        response = await client.post(
            f"{MCP_SERVER_URL}/tools/search_memory",
            json={
                "query": query,
                "user_id": TEST_USER_ID,
                "limit": 5
            },
            timeout=30.0
        )
        
        end_time = time.time()
        latency_ms = (end_time - start_time) * 1000
        
        success = response.status_code == 200
        result = response.json() if success else {"error": response.text}
        
        return {
            "latency_ms": latency_ms,
            "success": success,
            "response": result,
            "status_code": response.status_code
        }
        
    except Exception as e:
        end_time = time.time()
        latency_ms = (end_time - start_time) * 1000
        return {
            "latency_ms": latency_ms,
            "success": False,
            "error": str(e),
            "status_code": None
        }

async def run_latency_test():
    """Run comprehensive latency testing"""
    print("🚀 Memory Extraction Pipeline Latency Test")
    print("=" * 60)
    
    # Test memories for variety
    test_memories = [
        "I prefer Python programming for backend development",
        "My favorite database is PostgreSQL with vector extensions",
        "I work remotely from home office setup",
        "I enjoy hiking and outdoor photography",
        "I'm learning about machine learning and AI systems",
        "I have experience with Docker and containerization",
        "My programming setup includes VS Code and terminal",
        "I prefer async programming patterns",
        "I'm interested in distributed systems architecture",
        "I like coffee and usually work in the morning"
    ]
    
    search_queries = [
        "programming languages",
        "database preferences", 
        "work environment",
        "hobbies and interests",
        "technical skills"
    ]
    
    async with httpx.AsyncClient() as client:
        # Clean up previous test data
        print("🧹 Cleaning up previous test data...")
        try:
            await client.post(
                f"{MCP_SERVER_URL}/tools/delete_all_memories",
                json={"user_id": TEST_USER_ID}
            )
        except:
            pass
        
        # Test 1: Add Memory Latency
        print(f"\n📝 Testing Add Memory Latency ({NUM_TESTS} operations)")
        add_results = []
        
        for i in range(NUM_TESTS):
            memory_text = test_memories[i % len(test_memories)] + f" (test {i+1})"
            result = await measure_add_memory_latency(client, memory_text)
            add_results.append(result)
            
            status = "✅" if result["success"] else "❌"
            print(f"  {status} Test {i+1}: {result['latency_ms']:.0f}ms")
        
        # Calculate add memory statistics
        successful_adds = [r for r in add_results if r["success"]]
        if successful_adds:
            add_latencies = [r["latency_ms"] for r in successful_adds]
            add_stats = {
                "count": len(successful_adds),
                "success_rate": len(successful_adds) / len(add_results) * 100,
                "mean_ms": statistics.mean(add_latencies),
                "median_ms": statistics.median(add_latencies),
                "p95_ms": sorted(add_latencies)[int(0.95 * len(add_latencies))],
                "p99_ms": sorted(add_latencies)[int(0.99 * len(add_latencies))],
                "min_ms": min(add_latencies),
                "max_ms": max(add_latencies)
            }
            
            print(f"\n📊 Add Memory Statistics:")
            print(f"  Success Rate: {add_stats['success_rate']:.1f}%")
            print(f"  Mean Latency: {add_stats['mean_ms']:.0f}ms")
            print(f"  P95 Latency: {add_stats['p95_ms']:.0f}ms")
            print(f"  P99 Latency: {add_stats['p99_ms']:.0f}ms")
            print(f"  Range: {add_stats['min_ms']:.0f}ms - {add_stats['max_ms']:.0f}ms")
        
        # Wait a moment for indexing
        print("\n⏳ Waiting for memory indexing...")
        await asyncio.sleep(2)
        
        # Test 2: Search Memory Latency
        print(f"\n🔍 Testing Search Memory Latency ({len(search_queries)} queries)")
        search_results = []
        
        for i, query in enumerate(search_queries):
            result = await measure_search_memory_latency(client, query)
            search_results.append(result)
            
            status = "✅" if result["success"] else "❌"
            result_count = len(result.get("response", {}).get("memories", [])) if result["success"] else 0
            print(f"  {status} Query {i+1}: {result['latency_ms']:.0f}ms ({result_count} results)")
        
        # Calculate search statistics
        successful_searches = [r for r in search_results if r["success"]]
        if successful_searches:
            search_latencies = [r["latency_ms"] for r in successful_searches]
            search_stats = {
                "count": len(successful_searches),
                "success_rate": len(successful_searches) / len(search_results) * 100,
                "mean_ms": statistics.mean(search_latencies),
                "median_ms": statistics.median(search_latencies),
                "p95_ms": sorted(search_latencies)[int(0.95 * len(search_latencies))] if len(search_latencies) > 1 else search_latencies[0],
                "p99_ms": sorted(search_latencies)[int(0.99 * len(search_latencies))] if len(search_latencies) > 1 else search_latencies[0],
                "min_ms": min(search_latencies),
                "max_ms": max(search_latencies)
            }
            
            print(f"\n📊 Search Memory Statistics:")
            print(f"  Success Rate: {search_stats['success_rate']:.1f}%")
            print(f"  Mean Latency: {search_stats['mean_ms']:.0f}ms")
            print(f"  P95 Latency: {search_stats['p95_ms']:.0f}ms")
            print(f"  P99 Latency: {search_stats['p99_ms']:.0f}ms")
            print(f"  Range: {search_stats['min_ms']:.0f}ms - {search_stats['max_ms']:.0f}ms")
        
        # Performance Analysis
        print(f"\n📈 Performance Analysis:")
        
        if successful_adds:
            if add_stats['p95_ms'] > 2000:
                print(f"  🚨 CRITICAL: Add Memory P95 ({add_stats['p95_ms']:.0f}ms) exceeds 2000ms threshold")
            elif add_stats['p95_ms'] > 1000:
                print(f"  ⚠️  WARNING: Add Memory P95 ({add_stats['p95_ms']:.0f}ms) exceeds 1000ms target")
            else:
                print(f"  ✅ Add Memory P95 ({add_stats['p95_ms']:.0f}ms) within acceptable range")
        
        if successful_searches:
            if search_stats['p95_ms'] > 1000:
                print(f"  🚨 CRITICAL: Search Memory P95 ({search_stats['p95_ms']:.0f}ms) exceeds 1000ms threshold")
            elif search_stats['p95_ms'] > 500:
                print(f"  ⚠️  WARNING: Search Memory P95 ({search_stats['p95_ms']:.0f}ms) exceeds 500ms target")
            else:
                print(f"  ✅ Search Memory P95 ({search_stats['p95_ms']:.0f}ms) within acceptable range")
        
        # Error Analysis
        failed_adds = [r for r in add_results if not r["success"]]
        failed_searches = [r for r in search_results if not r["success"]]
        
        if failed_adds or failed_searches:
            print(f"\n🚨 Error Analysis:")
            
            if failed_adds:
                print(f"  Add Memory Failures: {len(failed_adds)}")
                for i, failure in enumerate(failed_adds[:3]):  # Show first 3 failures
                    error = failure.get("error", "Unknown error")
                    print(f"    {i+1}. {error}")
            
            if failed_searches:
                print(f"  Search Memory Failures: {len(failed_searches)}")
                for i, failure in enumerate(failed_searches[:3]):  # Show first 3 failures
                    error = failure.get("error", "Unknown error")
                    print(f"    {i+1}. {error}")
        
        print(f"\n✅ Latency test completed")

if __name__ == "__main__":
    asyncio.run(run_latency_test())