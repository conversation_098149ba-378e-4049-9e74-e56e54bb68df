#!/usr/bin/env python3
"""
Debug script to investigate MCP search returning 0 results
"""

import asyncio
import sys
import os
sys.path.append('src')

from supabase_mcp_integration import RealSupabaseMCP
from bge_embedding_client import BGEEmbeddingClient

async def debug_mcp_search():
    """Debug why MCP search returns 0 results"""
    print("🔍 Debugging MCP Search Issues")
    print("=" * 40)
    
    try:
        supabase = RealSupabaseMCP()
        bge_client = BGEEmbeddingClient()
        
        test_user = "mcp_latency_test"
        
        # Check what memories exist for this user
        print(f"\n📋 Checking existing memories for user '{test_user}'...")
        all_memories = await supabase.get_all_memories(test_user, limit=20)
        print(f"✅ Found {len(all_memories)} memories")
        
        if all_memories:
            for i, memory in enumerate(all_memories[:5]):
                content_preview = memory.get('content', '')[:60] + "..."
                created_at = memory.get('created_at', 'N/A')
                print(f"  {i+1}. {content_preview} (Created: {created_at})")
        
        # Test search with different thresholds
        test_query = "programming languages and development"
        print(f"\n🔍 Testing search query: '{test_query}'")
        
        query_embedding = await bge_client.embed_single(test_query, add_instruction=True)
        print(f"✅ Generated query embedding: {len(query_embedding)} dimensions")
        
        thresholds = [0.5, 1.0, 1.5, 2.0, 3.0, 5.0]
        
        for threshold in thresholds:
            results = await supabase.similarity_search(
                embedding=query_embedding,
                user_id=test_user,
                threshold=threshold,
                limit=5
            )
            print(f"  Threshold {threshold}: {len(results)} results")
            
            if results:
                for j, result in enumerate(results[:2]):
                    distance = result.get('distance', 'N/A')
                    content_preview = result.get('content', '')[:40] + "..."
                    print(f"    {j+1}. Distance {distance:.4f}: {content_preview}")
        
        # Test exact match search (if memories exist)
        if all_memories:
            print(f"\n🎯 Testing exact content match...")
            exact_content = all_memories[0].get('content', '')
            exact_embedding = await bge_client.embed_single(exact_content, add_instruction=True)
            
            exact_results = await supabase.similarity_search(
                embedding=exact_embedding,
                user_id=test_user,
                threshold=5.0,  # Very liberal threshold
                limit=5
            )
            
            print(f"✅ Exact match search: {len(exact_results)} results")
            for j, result in enumerate(exact_results):
                distance = result.get('distance', 'N/A')
                content_preview = result.get('content', '')[:50] + "..."
                print(f"  {j+1}. Distance {distance:.4f}: {content_preview}")
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_mcp_search())