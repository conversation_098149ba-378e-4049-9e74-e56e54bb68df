#!/usr/bin/env python3
"""
Debug script to reproduce the memory extraction JSON parsing issue.
"""

import asyncio
import sys
import os
sys.path.append('/home/<USER>/dev/tools/mcp-mem0/src')

from simple_llm_client import get_spark_memory_client
from memory_extraction import MemoryExtractionModule
from bge_embedding_client import BGEEmbeddingClient
import json
import logging

# Setup logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

async def test_extraction_issue():
    """Test the memory extraction issue to understand JSON parsing failures."""
    
    print("🔍 Testing Memory Extraction JSON Parsing Issue")
    print("=" * 60)
    
    try:
        # Initialize clients
        print("📋 Initializing LLM client...")
        llm_client = get_spark_memory_client()
        print(f"✅ LLM Client: {llm_client.provider} - {llm_client.model}")
        
        print("📋 Initializing BGE client...")
        bge_client = BGEEmbeddingClient()
        print(f"✅ BGE Client: {bge_client.base_url}")
        
        # Test BGE connectivity
        print("🔌 Testing BGE server connectivity...")
        bge_healthy = await bge_client.health_check()
        print(f"{'✅' if bge_healthy else '❌'} BGE Server: {'Healthy' if bge_healthy else 'Unhealthy'}")
        
        # Initialize extraction module
        print("📋 Initializing extraction module...")
        extraction = MemoryExtractionModule(llm_client, bge_client)
        
        # Test cases that might cause JSON parsing issues
        test_cases = [
            "I prefer React over Vue for frontend development. I'm currently working on a memory system project.",
            "My name is John and I work as a software engineer at TechCorp. I specialize in Python and machine learning.",
            "I have a dog named Max. He's a golden retriever and loves to play fetch in the park.",
            "I'm learning about vector databases and embedding models like BGE. The performance targets are challenging.",
            "The memory extraction pipeline is returning 0 candidates with P95 latency at 2156ms. This needs optimization."
        ]
        
        print("\n🧪 Testing Memory Extraction with Various Inputs")
        print("=" * 60)
        
        for i, test_input in enumerate(test_cases, 1):
            print(f"\n📝 Test Case {i}:")
            print(f"Input: {test_input[:60]}{'...' if len(test_input) > 60 else ''}")
            
            # Create context
            context = {
                "conversation_summary": "Testing memory extraction",
                "recent_messages": ["Previous message for context"],
                "user_id": "test_user"
            }
            
            # Test extraction
            try:
                message_pair = ("", test_input)
                candidates = await extraction.extract_memories(message_pair, context)
                
                print(f"✅ Extracted {len(candidates)} candidates:")
                for j, candidate in enumerate(candidates, 1):
                    print(f"   {j}. {candidate}")
                    
            except Exception as e:
                print(f"❌ Extraction failed: {e}")
        
        # Test the raw LLM response to see what's actually being returned
        print("\n🔍 Testing Raw LLM Response")
        print("=" * 60)
        
        test_prompt = extraction._build_extraction_prompt(
            "", 
            "I prefer React over Vue and I'm working on a memory system project.", 
            {
                "conversation_summary": "Testing",
                "recent_messages": ["Previous message"],
                "user_id": "test_user"
            }
        )
        
        print("📤 Sending prompt to LLM...")
        raw_response = await llm_client.generate(test_prompt)
        
        print(f"📥 Raw LLM Response:")
        print(f"Type: {type(raw_response)}")
        print(f"Length: {len(raw_response)}")
        print(f"Content: {repr(raw_response)}")
        
        # Test JSON parsing
        print("\n🔧 Testing JSON Parsing:")
        try:
            parsed = json.loads(raw_response)
            print(f"✅ Valid JSON, type: {type(parsed)}")
            print(f"Content: {parsed}")
            
            if isinstance(parsed, list):
                print(f"✅ Correctly formatted as list with {len(parsed)} items")
            else:
                print(f"⚠️  Expected list, got {type(parsed)}")
                
        except json.JSONDecodeError as e:
            print(f"❌ JSON parsing failed: {e}")
            print("📋 Attempting fallback parsing...")
            
            # Try to extract meaningful content
            if raw_response.strip():
                print(f"🔄 Fallback: Treating as single memory")
                fallback_memories = [raw_response.strip()]
                print(f"Fallback result: {fallback_memories}")
            else:
                print(f"❌ Empty response, cannot extract memories")
    
    except Exception as e:
        print(f"❌ Fatal error in test: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        try:
            await bge_client.close()
        except:
            pass

if __name__ == "__main__":
    asyncio.run(test_extraction_issue())