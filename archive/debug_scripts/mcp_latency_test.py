#!/usr/bin/env python3
"""
MCP Protocol Latency Test for Memory Extraction Pipeline
Properly tests via MCP protocol and measures P95 latency.
"""

import asyncio
import json
import time
import statistics
from typing import List, Dict, Any
from mcp import ClientSession
from mcp.client.sse import sse_client

# Configuration
MCP_SERVER_URL = "http://localhost:8050/sse"
TEST_USER_ID = "mcp_latency_test"
NUM_TESTS = 10

async def measure_mcp_add_memory_latency(session: ClientSession, text: str) -> Dict[str, Any]:
    """Measure latency for adding a memory via MCP protocol"""
    start_time = time.time()
    
    try:
        result = await session.call_tool(
            "add_memories",
            arguments={
                "text": text,
                "user_id": TEST_USER_ID
            }
        )
        
        end_time = time.time()
        latency_ms = (end_time - start_time) * 1000
        
        # Parse response
        success = False
        candidates_processed = 0
        response_data = {}
        
        if result.content and len(result.content) > 0:
            response_text = result.content[0].text
            try:
                response_data = json.loads(response_text)
                success = response_data.get('success', False)
                candidates_processed = response_data.get('candidates_processed', 0)
            except json.JSONDecodeError:
                success = False
        
        return {
            "latency_ms": latency_ms,
            "success": success,
            "candidates_processed": candidates_processed,
            "response": response_data
        }
        
    except Exception as e:
        end_time = time.time()
        latency_ms = (end_time - start_time) * 1000
        return {
            "latency_ms": latency_ms,
            "success": False,
            "error": str(e),
            "candidates_processed": 0
        }

async def measure_mcp_search_memory_latency(session: ClientSession, query: str) -> Dict[str, Any]:
    """Measure latency for searching memories via MCP protocol"""
    start_time = time.time()
    
    try:
        result = await session.call_tool(
            "search_memory",
            arguments={
                "query": query,
                "user_id": TEST_USER_ID,
                "limit": 5
            }
        )
        
        end_time = time.time()
        latency_ms = (end_time - start_time) * 1000
        
        # Parse response
        success = False
        memories_found = 0
        response_data = {}
        
        if result.content and len(result.content) > 0:
            response_text = result.content[0].text
            try:
                response_data = json.loads(response_text)
                success = response_data.get('success', False)
                memories = response_data.get('memories', [])
                memories_found = len(memories)
            except json.JSONDecodeError:
                success = False
        
        return {
            "latency_ms": latency_ms,
            "success": success,
            "memories_found": memories_found,
            "response": response_data
        }
        
    except Exception as e:
        end_time = time.time()
        latency_ms = (end_time - start_time) * 1000
        return {
            "latency_ms": latency_ms,
            "success": False,
            "error": str(e),
            "memories_found": 0
        }

async def run_mcp_latency_test():
    """Run comprehensive MCP latency testing"""
    print("🚀 MCP Memory Extraction Pipeline Latency Test")
    print("=" * 60)
    
    # Test memories for variety
    test_memories = [
        "I prefer Python programming for backend development and API design",
        "My favorite database is PostgreSQL with pgvector extensions for semantic search",
        "I work remotely from a home office setup with dual monitors",
        "I enjoy hiking and outdoor photography during weekends",
        "I'm learning about machine learning and vector embedding systems",
        "I have experience with Docker containerization and microservices",
        "My programming setup includes VS Code, terminal, and Git workflows",
        "I prefer async programming patterns for high-performance applications",
        "I'm interested in distributed systems architecture and scalability",
        "I like coffee and usually work most productively in the morning hours"
    ]
    
    search_queries = [
        "programming languages and development",
        "database and data storage preferences", 
        "work environment and setup",
        "hobbies and personal interests",
        "technical skills and experience"
    ]
    
    try:
        print(f"🔌 Connecting to MCP server at {MCP_SERVER_URL}...")
        
        async with sse_client(MCP_SERVER_URL) as (read, write):
            async with ClientSession(read, write) as session:
                print("✅ Connected to MCP server")
                
                # Initialize the session
                await session.initialize()
                print("✅ Session initialized")
                
                # List available tools
                tools_result = await session.list_tools()
                available_tools = [tool.name for tool in tools_result.tools]
                print(f"📋 Available tools: {available_tools}")
                
                # Check required tools
                required_tools = ["add_memories", "search_memory", "delete_all_memories"]
                missing_tools = [tool for tool in required_tools if tool not in available_tools]
                
                if missing_tools:
                    print(f"❌ Missing required tools: {missing_tools}")
                    return
                
                print("✅ All required tools available")
                
                # Clean up previous test data
                print(f"\n🧹 Cleaning up previous test data...")
                try:
                    await session.call_tool(
                        "delete_all_memories",
                        arguments={"user_id": TEST_USER_ID}
                    )
                    print("✅ Previous data cleaned")
                except Exception as e:
                    print(f"⚠️  Cleanup warning: {e}")
                
                # Test 1: Add Memory Latency
                print(f"\n📝 Testing Add Memory Latency ({NUM_TESTS} operations)")
                add_results = []
                
                for i in range(NUM_TESTS):
                    memory_text = test_memories[i % len(test_memories)]
                    result = await measure_mcp_add_memory_latency(session, memory_text)
                    add_results.append(result)
                    
                    status = "✅" if result["success"] else "❌"
                    candidates = result.get("candidates_processed", 0)
                    print(f"  {status} Test {i+1}: {result['latency_ms']:.0f}ms ({candidates} candidates)")
                
                # Calculate add memory statistics
                successful_adds = [r for r in add_results if r["success"]]
                if successful_adds:
                    add_latencies = [r["latency_ms"] for r in successful_adds]
                    add_stats = {
                        "count": len(successful_adds),
                        "success_rate": len(successful_adds) / len(add_results) * 100,
                        "mean_ms": statistics.mean(add_latencies),
                        "median_ms": statistics.median(add_latencies),
                        "p95_ms": sorted(add_latencies)[int(0.95 * len(add_latencies))] if len(add_latencies) > 1 else add_latencies[0],
                        "p99_ms": sorted(add_latencies)[int(0.99 * len(add_latencies))] if len(add_latencies) > 1 else add_latencies[0],
                        "min_ms": min(add_latencies),
                        "max_ms": max(add_latencies),
                        "total_candidates": sum(r.get("candidates_processed", 0) for r in successful_adds)
                    }
                    
                    print(f"\n📊 Add Memory Statistics:")
                    print(f"  Success Rate: {add_stats['success_rate']:.1f}%")
                    print(f"  Total Candidates: {add_stats['total_candidates']}")
                    print(f"  Mean Latency: {add_stats['mean_ms']:.0f}ms")
                    print(f"  P95 Latency: {add_stats['p95_ms']:.0f}ms")
                    print(f"  P99 Latency: {add_stats['p99_ms']:.0f}ms")
                    print(f"  Range: {add_stats['min_ms']:.0f}ms - {add_stats['max_ms']:.0f}ms")
                    
                    # Performance assessment
                    if add_stats['p95_ms'] > 2000:
                        print(f"  🚨 CRITICAL: P95 ({add_stats['p95_ms']:.0f}ms) exceeds 2000ms threshold!")
                    elif add_stats['p95_ms'] > 1000:
                        print(f"  ⚠️  WARNING: P95 ({add_stats['p95_ms']:.0f}ms) exceeds 1000ms target")
                    else:
                        print(f"  ✅ P95 latency within acceptable range")
                
                # Wait for indexing
                print("\n⏳ Waiting for memory indexing...")
                await asyncio.sleep(3)
                
                # Test 2: Search Memory Latency
                print(f"\n🔍 Testing Search Memory Latency ({len(search_queries)} queries)")
                search_results = []
                
                for i, query in enumerate(search_queries):
                    result = await measure_mcp_search_memory_latency(session, query)
                    search_results.append(result)
                    
                    status = "✅" if result["success"] else "❌"
                    memories_found = result.get("memories_found", 0)
                    print(f"  {status} Query {i+1}: {result['latency_ms']:.0f}ms ({memories_found} results)")
                
                # Calculate search statistics
                successful_searches = [r for r in search_results if r["success"]]
                if successful_searches:
                    search_latencies = [r["latency_ms"] for r in successful_searches]
                    search_stats = {
                        "count": len(successful_searches),
                        "success_rate": len(successful_searches) / len(search_results) * 100,
                        "mean_ms": statistics.mean(search_latencies),
                        "median_ms": statistics.median(search_latencies),
                        "p95_ms": sorted(search_latencies)[int(0.95 * len(search_latencies))] if len(search_latencies) > 1 else search_latencies[0],
                        "p99_ms": sorted(search_latencies)[int(0.99 * len(search_latencies))] if len(search_latencies) > 1 else search_latencies[0],
                        "min_ms": min(search_latencies),
                        "max_ms": max(search_latencies),
                        "total_memories_found": sum(r.get("memories_found", 0) for r in successful_searches)
                    }
                    
                    print(f"\n📊 Search Memory Statistics:")
                    print(f"  Success Rate: {search_stats['success_rate']:.1f}%")
                    print(f"  Total Memories Found: {search_stats['total_memories_found']}")
                    print(f"  Mean Latency: {search_stats['mean_ms']:.0f}ms")
                    print(f"  P95 Latency: {search_stats['p95_ms']:.0f}ms")
                    print(f"  P99 Latency: {search_stats['p99_ms']:.0f}ms")
                    print(f"  Range: {search_stats['min_ms']:.0f}ms - {search_stats['max_ms']:.0f}ms")
                    
                    # Performance assessment
                    if search_stats['p95_ms'] > 1000:
                        print(f"  🚨 CRITICAL: Search P95 ({search_stats['p95_ms']:.0f}ms) exceeds 1000ms threshold!")
                    elif search_stats['p95_ms'] > 500:
                        print(f"  ⚠️  WARNING: Search P95 ({search_stats['p95_ms']:.0f}ms) exceeds 500ms target")
                    else:
                        print(f"  ✅ Search P95 latency within acceptable range")
                
                # Error Analysis
                failed_adds = [r for r in add_results if not r["success"]]
                failed_searches = [r for r in search_results if not r["success"]]
                
                if failed_adds or failed_searches:
                    print(f"\n🚨 Error Analysis:")
                    
                    if failed_adds:
                        print(f"  Add Memory Failures: {len(failed_adds)}")
                        for i, failure in enumerate(failed_adds[:3]):  # Show first 3 failures
                            error = failure.get("error", "Unknown error")
                            print(f"    {i+1}. {error}")
                    
                    if failed_searches:
                        print(f"  Search Memory Failures: {len(failed_searches)}")
                        for i, failure in enumerate(failed_searches[:3]):  # Show first 3 failures
                            error = failure.get("error", "Unknown error")
                            print(f"    {i+1}. {error}")
                
                # Overall Assessment
                print(f"\n🎯 Overall Performance Assessment:")
                
                if successful_adds and add_stats['total_candidates'] == 0:
                    print(f"  🚨 CRITICAL: Memory extraction returning 0 candidates")
                    print(f"  📋 This indicates LLM parsing or extraction pipeline failure")
                
                if successful_adds and add_stats['p95_ms'] > 2000:
                    print(f"  🚨 CRITICAL: Add Memory P95 latency exceeds 2000ms threshold")
                    print(f"  📋 This matches the reported P95 latency issue")
                
                if successful_searches and search_stats['total_memories_found'] == 0 and add_stats.get('total_candidates', 0) > 0:
                    print(f"  🚨 CRITICAL: Semantic search not finding stored memories")
                    print(f"  📋 This indicates BGE embedding or vector search issues")
                
                print(f"\n✅ MCP latency test completed")
                
    except Exception as e:
        print(f"❌ MCP connection failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(run_mcp_latency_test())