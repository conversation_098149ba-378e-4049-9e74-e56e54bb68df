#!/usr/bin/env python3
"""
Debug script to check what's actually stored in the database
and what the similarity distances look like.
"""

import asyncio
import asyncpg
import os
import json
from typing import List

async def debug_database():
    """Debug what's in the memory database"""
    database_url = os.getenv('DATABASE_URL', '***********************************************************************************')
    
    print("🔍 Database Debug Analysis")
    print("=" * 50)
    
    try:
        conn = await asyncpg.connect(database_url)
        
        # Check if memories table exists and its structure
        print("📋 Checking table structure...")
        table_info = await conn.fetch("""
            SELECT column_name, data_type, is_nullable 
            FROM information_schema.columns 
            WHERE table_name = 'memories' 
            ORDER BY ordinal_position
        """)
        
        if not table_info:
            print("❌ No 'memories' table found!")
            return
        
        print("✅ Table structure:")
        for col in table_info:
            print(f"  - {col['column_name']}: {col['data_type']} ({'NULL' if col['is_nullable'] == 'YES' else 'NOT NULL'})")
        
        # Count total memories
        total_count = await conn.fetchval("SELECT COUNT(*) FROM memories")
        print(f"\n📊 Total memories in database: {total_count}")
        
        if total_count == 0:
            print("❌ No memories found in database!")
            await conn.close()
            return
        
        # Count by user
        user_counts = await conn.fetch("""
            SELECT user_id, COUNT(*) as count 
            FROM memories 
            GROUP BY user_id 
            ORDER BY count DESC
        """)
        
        print("\n👥 Memories by user:")
        for row in user_counts:
            print(f"  - {row['user_id']}: {row['count']} memories")
        
        # Get sample memories with their embeddings info
        print("\n📝 Sample memories:")
        samples = await conn.fetch("""
            SELECT id, user_id, content, 
                   created_at,
                   CASE WHEN embedding IS NOT NULL THEN 'HAS_EMBEDDING' ELSE 'NO_EMBEDDING' END as embedding_status
            FROM memories 
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        
        for sample in samples:
            print(f"  ID {sample['id']}: {sample['content'][:100]}...")
            print(f"    User: {sample['user_id']}, Embedding: {sample['embedding_status']}, Created: {sample['created_at']}")
        
        # Test actual similarity search with sample data
        if samples:
            print("\n🔍 Testing similarity search...")
            sample_memory = samples[0]
            user_id = sample_memory['user_id']
            
            # Get the embedding of the first memory
            embedding_result = await conn.fetchrow("""
                SELECT embedding FROM memories WHERE id = $1
            """, sample_memory['id'])
            
            if embedding_result and embedding_result['embedding']:
                sample_embedding = list(embedding_result['embedding'])
                print(f"✅ Sample embedding has {len(sample_embedding)} dimensions")
                
                # Test similarity search with different thresholds
                thresholds = [0.1, 0.5, 1.0, 1.5, 2.0, 5.0]
                
                for threshold in thresholds:
                    results = await conn.fetch("""
                        SELECT id, content, 
                               (embedding <-> $1::vector) as distance
                        FROM memories 
                        WHERE user_id = $2 AND (embedding <-> $1::vector) < $3
                        ORDER BY distance
                        LIMIT 5
                    """, sample_embedding, user_id, threshold)
                    
                    print(f"  Threshold {threshold}: {len(results)} results")
                    for result in results[:2]:  # Show first 2 results
                        print(f"    - Distance {result['distance']:.4f}: {result['content'][:50]}...")
                
                # Also test without threshold to see actual distances
                print("\n📏 All distances for this user (no threshold):")
                all_results = await conn.fetch("""
                    SELECT id, content, 
                           (embedding <-> $1::vector) as distance
                    FROM memories 
                    WHERE user_id = $2
                    ORDER BY distance
                    LIMIT 10
                """, sample_embedding, user_id)
                
                for result in all_results:
                    print(f"    Distance {result['distance']:.4f}: {result['content'][:50]}...")
            
            else:
                print("❌ No embedding found for sample memory")
        
        await conn.close()
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_database())