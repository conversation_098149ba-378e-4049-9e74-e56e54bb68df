#!/usr/bin/env python3
"""
Debug script to test memory extraction with the JIRA ticket text
"""

import asyncio
import json
import sys
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from memory_extraction import MemoryExtractionModule
from simple_llm_client import get_spark_memory_client

async def debug_extraction():
    """Test extraction with the actual JIRA ticket text."""
    
    # The exact text from the user's request
    jira_text = "User analyzed JIRA ticket CS1000-7209 from MegaOne Store customer support. Customer rod<PERSON><PERSON><PERSON> complained about receiving polishing pad without water holes despite product description advertising holes for wet polishing."
    
    print("🔍 Debugging Memory Extraction")
    print("=" * 50)
    print(f"Input text: {jira_text}")
    print()
    
    try:
        # Get the actual LLM client (same as production)
        llm_client = get_spark_memory_client()
        print("✅ LLM client initialized")
        
        # Mock BGE client for this test
        class MockBGEClient:
            async def embed_single(self, text):
                return [0.1] * 768
        
        bge_client = MockBGEClient()
        
        # Initialize extraction module
        extraction = MemoryExtractionModule(llm_client, bge_client)
        print("✅ Extraction module initialized")
        
        # Create message pair (same as production)
        message_pair = ("", jira_text)  # Empty previous message
        
        # Create context (same as production)
        context = {
            "conversation_summary": "None",
            "recent_messages": [],
            "user_id": "user"
        }
        
        print("\n📝 Testing extraction...")
        print(f"Message pair: {message_pair}")
        print(f"Context: {context}")
        
        # Test the extraction
        memories = await extraction.extract_memories(message_pair, context)
        
        print(f"\n📊 Results:")
        print(f"Candidates extracted: {len(memories)}")
        
        if memories:
            for i, memory in enumerate(memories, 1):
                print(f"  {i}. {memory}")
        
        # Always debug the LLM response to see what's happening
        print("\n🔍 Debugging LLM Response...")
        
        # Build the exact prompt that extraction uses
        prompt = f"""
Extract key memories from this conversation that should be stored for future reference.

Focus on:
- Factual information about the user (preferences, background, context)
- Important decisions or conclusions reached
- Actionable information or commitments made
- Significant events or experiences mentioned
- Skills, knowledge, or expertise demonstrated

Previous Message: 
Current Message: {jira_text}

Conversation Summary: None
Recent Context: None

Extract only factual, actionable information that would be valuable to remember in future conversations.
Avoid storing:
- Temporary conversation state
- Generic responses or acknowledgments
- Redundant information already covered in summary

Return your response as a JSON list of memory strings. If no significant memories should be stored, return an empty list [].

Example response format:
["User prefers React over Vue for frontend development", "User is working on a machine learning project about sentiment analysis"]
        """.strip()
        
        print(f"Prompt sent to LLM:")
        print("-" * 30)
        print(prompt)
        print("-" * 30)
        
        try:
            raw_response = await llm_client.generate(prompt)
            print(f"\nRaw LLM Response:")
            print(f"'{raw_response}'")
            
            # Try to parse it
            try:
                parsed = json.loads(raw_response)
                print(f"Parsed as JSON: {parsed}")
                print(f"Type: {type(parsed)}")
            except json.JSONDecodeError as e:
                print(f"❌ JSON parsing failed: {e}")
                
        except Exception as e:
            print(f"❌ LLM generation failed: {e}")
            
        # Check LLM provider configuration
        print(f"\n🔧 LLM Configuration:")
        print(f"Provider: {os.getenv('LLM_PROVIDER', 'NOT SET')}")
        print(f"Model: {os.getenv('LLM_CHOICE', 'NOT SET')}")  
        print(f"API Key: {'SET' if os.getenv('LLM_API_KEY') else 'NOT SET'}")
        print(f"Base URL: {os.getenv('LLM_BASE_URL', 'NOT SET')}")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_extraction())