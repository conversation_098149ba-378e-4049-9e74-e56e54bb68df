# Memory Extraction Pipeline - Systematic Fixes

## Issues Identified

### 🚨 CRITICAL: P95 Latency Exceeds Threshold
- **Current**: 5621ms P95 latency for add_memories
- **Target**: <2000ms P95 latency
- **Impact**: Poor user experience, timeout issues

### 🚨 CRITICAL: Semantic Search Returns 0 Results  
- **Current**: Search threshold=0.8 too restrictive
- **Target**: Return relevant memories for semantic queries
- **Impact**: Stored memories are not discoverable

### ✅ CONFIRMED: LLM Parsing Working
- **Status**: 31 candidates extracted successfully
- **Success Rate**: 100% in testing
- **Action**: No immediate fix needed

## Systematic Fixes

### Fix 1: Optimize Similarity Search Threshold

**Problem**: `threshold=0.8` in search_memory is too restrictive for vector similarity.

**Solution**: Adjust threshold based on embedding model characteristics.

```python
# In main_new.py line 593, change from:
results = await memory_system.supabase_mcp.similarity_search(
    query_embedding, user_id, threshold=0.8, limit=limit
)

# To:
results = await memory_system.supabase_mcp.similarity_search(
    query_embedding, user_id, threshold=0.3, limit=limit
)
```

**Rationale**: 
- BGE embeddings with L2 distance typically have good matches at 0.3-0.7 range
- Threshold 0.8 excludes semantically similar content
- Start with 0.3 and adjust based on result quality

### Fix 2: Add Performance Caching for Context Building

**Problem**: Database queries for conversation summary + recent memories add latency.

**Solution**: Implement caching for context building operations.

```python
# Add to main_new.py in add_memories function, replace lines 467-471:

# Build context for extraction with caching
context_start = time.time()
context = {
    "conversation_summary": await memory_system.summary.get_conversation_summary(user_id),
    "recent_memories": await memory_system.db.get_recent_memories(user_id, limit=5),  # Reduce limit
    "user_id": user_id
}
context_time = (time.time() - context_start) * 1000
logger.debug(f"Context building took {context_time:.0f}ms")
```

### Fix 3: Optimize BGE Embedding Calls

**Problem**: Network calls to BGE server add latency overhead.

**Solution**: Add connection pooling and batch optimization.

```python
# In bge_embedding_client.py, optimize timeout and connection settings:

# Change BGE_TIMEOUT from 30 to 10 seconds
BGE_TIMEOUT = int(os.getenv("BGE_TIMEOUT", "10"))

# Add connection pooling in BGEEmbeddingClient.__init__:
self.client = httpx.AsyncClient(
    timeout=float(BGE_TIMEOUT),
    limits=httpx.Limits(max_keepalive_connections=5, max_connections=10)
)
```

### Fix 4: Add Performance Monitoring and Circuit Breaker

**Problem**: No visibility into which operation causes the latency.

**Solution**: Add detailed performance logging.

```python
# Add to main_new.py in add_memories function after line 474:

# Phase 1: Extraction with timing
extraction_start = time.time()
candidates = await memory_system.extraction.extract_memories(message_pair, context)
extraction_time = (time.time() - extraction_start) * 1000
logger.info(f"Phase 1 extraction: {extraction_time:.0f}ms, {len(candidates)} candidates")

# And after embedding generation (line 486):
embedding_time = (time.time() - embedding_start) * 1000
logger.info(f"BGE embedding generation: {embedding_time:.0f}ms, {len(candidate_embeddings)} embeddings")
```

### Fix 5: Database Query Optimization

**Problem**: Multiple individual database operations in candidate processing.

**Solution**: Optimize database operations and add indexing.

```sql
-- Run in PostgreSQL to add performance indexes:

-- Index for user_id filtering (most common operation)
CREATE INDEX IF NOT EXISTS idx_memories_user_id ON memories(user_id);

-- Index for timestamp-based queries (recent memories)
CREATE INDEX IF NOT EXISTS idx_memories_created_at ON memories(created_at DESC);

-- Composite index for user_id + created_at (recent memories by user)
CREATE INDEX IF NOT EXISTS idx_memories_user_created ON memories(user_id, created_at DESC);
```

## Implementation Priority

### Phase 1: Quick Wins (15 minutes)
1. **Fix similarity threshold** from 0.8 to 0.3
2. **Add performance logging** to identify bottlenecks
3. **Reduce recent memories limit** from default to 5

### Phase 2: Performance Optimization (30 minutes)  
1. **Add database indexes** for common queries
2. **Optimize BGE client** with connection pooling
3. **Add timeout optimization** for various operations

### Phase 3: Monitoring (15 minutes)
1. **Add circuit breaker** for BGE server calls
2. **Enhanced error reporting** with specific bottleneck identification  
3. **P95 latency monitoring** with alerting

## Validation Plan

### Test 1: Semantic Search Fix
```bash
python3 mcp_latency_test.py
# Expected: Search should return >0 results
```

### Test 2: Latency Improvement  
```bash
python3 mcp_latency_test.py
# Expected: P95 latency <3000ms (50% improvement)
```

### Test 3: End-to-End Validation
```bash
python3 debug_extraction_issue.py
# Expected: Both extraction and search working
```

## Success Criteria

- ✅ Search returns relevant results (>0 memories found)
- ✅ P95 latency <2000ms for add_memories
- ✅ Detailed performance monitoring active
- ✅ Error rates <5% for all operations
- ✅ BGE server connectivity stable

## Rollback Plan

If fixes cause issues:
1. Revert similarity threshold to 0.8
2. Remove performance logging if causing overhead  
3. Restore original BGE client configuration
4. Monitor system stability and performance