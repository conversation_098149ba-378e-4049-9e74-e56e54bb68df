["tests/phase_1/test_critical_fixes.py::test_conflict_resolution", "tests/phase_1/test_critical_fixes.py::test_health_check", "tests/phase_1/test_critical_fixes.py::test_p95_latency", "tests/phase_1/test_critical_fixes.py::test_semantic_search_accuracy", "tests/test_cache_via_mcp.py::test_cache_performance", "tests/test_coroutine_serialization_fix.py::test_json_serialization_basic", "tests/test_coroutine_serialization_fix.py::test_performance_monitor_coroutine_handling", "tests/test_enhanced_database_integration.py::TestEnhancedDatabaseIntegration::test_bulk_operations_performance", "tests/test_enhanced_database_integration.py::TestEnhancedDatabaseIntegration::test_connection_pooling", "tests/test_enhanced_database_integration.py::TestEnhancedDatabaseIntegration::test_conversation_summary_operations", "tests/test_enhanced_database_integration.py::TestEnhancedDatabaseIntegration::test_database_service_initialization", "tests/test_enhanced_database_integration.py::TestEnhancedDatabaseIntegration::test_error_handling_and_recovery", "tests/test_enhanced_database_integration.py::TestEnhancedDatabaseIntegration::test_memory_storage_and_retrieval", "tests/test_enhanced_database_integration.py::TestEnhancedDatabaseIntegration::test_recent_memories_retrieval", "tests/test_enhanced_database_integration.py::TestEnhancedDatabaseIntegration::test_vector_index_creation", "tests/test_enhanced_database_integration.py::TestEnhancedDatabaseIntegration::test_vector_similarity_search", "tests/test_enhanced_database_integration.py::test_enhanced_database_integration", "tests/test_final_validation.py::test_final_validation", "tests/test_mcp_client.py::test_mcp_health_simulation", "tests/test_mcp_client.py::test_production_readiness", "tests/test_mcp_interface.py::test_mcp_add_memories", "tests/test_mcp_performance.py::test_http_alternative", "tests/test_mcp_performance.py::test_mcp_server_performance", "tests/test_mcp_simple.py::test_bge_integration", "tests/test_mcp_simple.py::test_container_mcp_server", "tests/test_mcp_simple.py::test_direct_memory_operations", "tests/test_performance_fix.py::test_performance_fix", "tests/test_production_fix.py::test_production_fix", "tests/test_production_mcp.py::test_bge_connectivity_from_container", "tests/test_production_mcp.py::test_container_imports", "tests/test_production_mcp.py::test_production_memory_pipeline", "tests/test_production_mcp.py::test_supabase_connectivity", "tests/test_real_mcp.py::test_spark_memory_mcp", "tests/test_two_phase_pipeline.py::test_bge_connectivity", "tests/test_two_phase_pipeline.py::test_database_schema", "tests/test_two_phase_pipeline.py::test_extraction_module", "tests/test_two_phase_pipeline.py::test_performance_monitor", "tests/test_two_phase_pipeline.py::test_rolling_summary", "tests/test_two_phase_pipeline.py::test_two_phase_integration", "tests/test_two_phase_pipeline.py::test_update_module", "tests/test_vector_fix.py::test_vector_fix"]