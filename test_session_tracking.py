#!/usr/bin/env python3
"""
Test script for session tracking functionality.

Tests the new session management features including:
- Creating sessions
- Adding memories to sessions
- Searching within sessions
- Session statistics
"""

import asyncio
import json
import requests
import time
from typing import Dict, Any

# MCP Server URL
MCP_SERVER_URL = "http://localhost:8050"

def call_mcp_tool(tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
    """Call an MCP tool via HTTP."""
    url = f"{MCP_SERVER_URL}/tools/{tool_name}"
    
    try:
        response = requests.post(url, json=arguments, timeout=30)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Error calling {tool_name}: {e}")
        return {"error": str(e)}

def test_session_creation():
    """Test creating a new session."""
    print("\n=== Testing Session Creation ===")
    
    result = call_mcp_tool("create_session", {
        "session_name": "Test Session for Development",
        "expires_in_hours": 2
    })
    
    print(f"Create session result: {json.dumps(result, indent=2)}")
    
    if "session" in result and result["session"]:
        session_id = result["session"]["id"]
        print(f"✅ Session created successfully: {session_id}")
        return session_id
    else:
        print("❌ Failed to create session")
        return None

def test_get_active_session():
    """Test getting the active session."""
    print("\n=== Testing Get Active Session ===")
    
    result = call_mcp_tool("get_active_session", {})
    print(f"Active session result: {json.dumps(result, indent=2)}")
    
    if result.get("session"):
        print("✅ Active session found")
        return result["session"]["id"]
    else:
        print("ℹ️ No active session found")
        return None

def test_add_memory_with_session(session_id: str):
    """Test adding memories to a specific session."""
    print(f"\n=== Testing Add Memory with Session {session_id} ===")
    
    test_memories = [
        "I love working with Python and AI systems",
        "Session tracking is an important feature for conversation management",
        "The Spark Memory MCP server uses PostgreSQL with pgvector for storage"
    ]
    
    for i, memory_text in enumerate(test_memories):
        result = call_mcp_tool("add_memories", {
            "text": memory_text,
            "session_id": session_id
        })
        
        print(f"Memory {i+1} result: {json.dumps(result, indent=2)}")
        
        if result.get("success"):
            print(f"✅ Memory {i+1} added successfully")
        else:
            print(f"❌ Failed to add memory {i+1}")
        
        time.sleep(1)  # Small delay between requests

def test_search_memory_with_session(session_id: str):
    """Test searching memories within a specific session."""
    print(f"\n=== Testing Search Memory with Session {session_id} ===")
    
    result = call_mcp_tool("search_memory", {
        "query": "Python AI systems",
        "limit": 5,
        "session_id": session_id
    })
    
    print(f"Search result: {json.dumps(result, indent=2)}")
    
    if result.get("success") and result.get("results"):
        print(f"✅ Found {len(result['results'])} memories in session")
    else:
        print("❌ No memories found in session search")

def test_list_memories_with_session(session_id: str):
    """Test listing memories for a specific session."""
    print(f"\n=== Testing List Memories with Session {session_id} ===")
    
    result = call_mcp_tool("list_memories", {
        "limit": 10,
        "session_id": session_id
    })
    
    print(f"List memories result: {json.dumps(result, indent=2)}")
    
    if result.get("success") and result.get("memories"):
        print(f"✅ Listed {len(result['memories'])} memories from session")
    else:
        print("❌ No memories found for session")

def test_session_stats(session_id: str):
    """Test getting session statistics."""
    print(f"\n=== Testing Session Stats for {session_id} ===")
    
    result = call_mcp_tool("get_session_stats", {
        "session_id": session_id
    })
    
    print(f"Session stats result: {json.dumps(result, indent=2)}")
    
    if result.get("success"):
        stats = result.get("stats", {})
        print(f"✅ Session stats: {stats.get('memory_count', 0)} memories")
    else:
        print("❌ Failed to get session stats")

def test_list_user_sessions():
    """Test listing all user sessions."""
    print("\n=== Testing List User Sessions ===")
    
    result = call_mcp_tool("list_user_sessions", {
        "limit": 10
    })
    
    print(f"User sessions result: {json.dumps(result, indent=2)}")
    
    if result.get("success") and result.get("sessions"):
        print(f"✅ Found {len(result['sessions'])} sessions for user")
    else:
        print("❌ No sessions found for user")

def test_end_session(session_id: str):
    """Test ending a session."""
    print(f"\n=== Testing End Session {session_id} ===")
    
    result = call_mcp_tool("end_session", {
        "session_id": session_id
    })
    
    print(f"End session result: {json.dumps(result, indent=2)}")
    
    if result.get("success"):
        print("✅ Session ended successfully")
    else:
        print("❌ Failed to end session")

def main():
    """Run all session tracking tests."""
    print("🚀 Starting Session Tracking Tests")
    print("=" * 50)
    
    # Test 1: Create a new session
    session_id = test_session_creation()
    if not session_id:
        print("❌ Cannot continue tests without a session")
        return
    
    # Test 2: Get active session
    active_session_id = test_get_active_session()
    
    # Test 3: Add memories to the session
    test_add_memory_with_session(session_id)
    
    # Test 4: Search memories within the session
    test_search_memory_with_session(session_id)
    
    # Test 5: List memories for the session
    test_list_memories_with_session(session_id)
    
    # Test 6: Get session statistics
    test_session_stats(session_id)
    
    # Test 7: List all user sessions
    test_list_user_sessions()
    
    # Test 8: End the session
    test_end_session(session_id)
    
    print("\n" + "=" * 50)
    print("🎉 Session Tracking Tests Completed!")

if __name__ == "__main__":
    main()
