#!/usr/bin/env python3
"""
Quick validation test for Spark MCP Server
Tests core functionality without full test suite
"""

import asyncio
import sys
import os
import httpx
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_bge_connectivity():
    """Test BGE server connectivity"""
    print("🔌 Testing BGE server connectivity...")
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://192.168.1.84:8080/health", timeout=10.0)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ BGE server healthy: {data.get('model', 'Unknown')} - {data.get('dimensions', 'Unknown')}D")
                return True
            else:
                print(f"❌ BGE server returned {response.status_code}")
                return False
    except Exception as e:
        print(f"❌ BGE server connection failed: {e}")
        return False

async def test_basic_imports():
    """Test that core modules can be imported"""
    print("📦 Testing module imports...")
    try:
        from bge_embedding_client import BGEEmbeddingClient
        from memory_database import MemoryDatabase
        from memory_extraction import MemoryExtractionModule
        from memory_update import MemoryUpdateModule
        from config import get_config
        from server_readiness import get_readiness_manager
        print("✅ All core modules imported successfully")
        return True
    except Exception as e:
        print(f"❌ Module import failed: {e}")
        return False

async def test_bge_client():
    """Test BGE client functionality"""
    print("🧠 Testing BGE embedding client...")
    try:
        from bge_embedding_client import BGEEmbeddingClient
        client = BGEEmbeddingClient()
        
        # Test health check
        health = await client.health_check()
        print(f"✅ BGE client health: {health}")
        
        # Test embedding generation
        embedding = await client.get_embedding("Test embedding text")
        if embedding and len(embedding) == 768:
            print(f"✅ Embedding generated: {len(embedding)} dimensions")
            return True
        else:
            print(f"❌ Invalid embedding: {len(embedding) if embedding else 0} dimensions")
            return False
    except Exception as e:
        print(f"❌ BGE client test failed: {e}")
        return False

async def test_configuration():
    """Test configuration loading"""
    print("⚙️ Testing configuration...")
    try:
        from config import get_config
        config = get_config()
        print(f"✅ Configuration loaded:")
        print(f"   - BGE Server: {config.bge_server_url}")
        print(f"   - LLM Provider: {config.llm_provider}")
        print(f"   - Transport: {config.transport}")
        print(f"   - Port: {config.port}")
        return True
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

async def main():
    """Run all validation tests"""
    print("🚀 Starting Spark MCP Server Validation")
    print("=" * 50)
    
    # Load environment
    from dotenv import load_dotenv
    load_dotenv()
    
    tests = [
        test_basic_imports,
        test_configuration,
        test_bge_connectivity,
        test_bge_client,
    ]
    
    results = []
    for test in tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            results.append(False)
        print()
    
    # Summary
    passed = sum(results)
    total = len(results)
    print("=" * 50)
    print(f"🏁 Validation Summary: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All validation tests passed! Server is ready.")
        return 0
    else:
        print("❌ Some validation tests failed. Check configuration.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)