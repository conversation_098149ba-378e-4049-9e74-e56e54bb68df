#!/usr/bin/env python3
"""
Test Enhanced Rolling Summary Features
Phase 2 Feature 3/3: Enhanced Rolling Summary Features

Tests the three main enhancements:
1. Temporal Context Tracking
2. Topic Continuity Management  
3. Summary Quality Assessment
"""

import asyncio
import json
import sys
import os
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from temporal_context_tracker import TemporalContextTracker, TemporalMarker
from topic_continuity_manager import TopicContinuityManager, Topic
from summary_quality_assessor import SummaryQualityAssessor, QualityFactors

# Mock LLM client for testing
class MockLLMClient:
    async def generate(self, prompt):
        if "temporal" in prompt.lower():
            return json.dumps({
                "markers": [
                    {
                        "type": "change",
                        "content": "User now prefers Python over JavaScript",
                        "confidence": 0.8,
                        "previous_state": "preferred JavaScript",
                        "current_state": "prefers Python",
                        "entities": ["Python", "JavaScript"]
                    }
                ]
            })
        elif "topics" in prompt.lower():
            return json.dumps({
                "topics": [
                    {
                        "name": "python_development",
                        "keywords": ["python", "development", "coding"],
                        "importance": 0.8
                    },
                    {
                        "name": "machine_learning",
                        "keywords": ["ml", "ai", "models"],
                        "importance": 0.7
                    }
                ]
            })
        elif "quality" in prompt.lower():
            return json.dumps({
                "completeness_score": 0.8,
                "coherence_score": 0.7,
                "relevance_score": 0.9,
                "conciseness_score": 0.6,
                "temporal_awareness_score": 0.8,
                "entity_coverage_score": 0.7,
                "actionability_score": 0.8,
                "reasoning": "Good coverage with clear temporal markers"
            })
        else:
            return "Enhanced summary with temporal and topic awareness."

# Mock database client
class MockDBClient:
    async def execute_query(self, sql, params):
        return []

async def test_temporal_context_tracker():
    """Test temporal context tracking functionality."""
    print("🔍 Testing Temporal Context Tracker...")
    
    llm_client = MockLLMClient()
    tracker = TemporalContextTracker(llm_client)
    
    # Test content with temporal changes
    current_content = """
    I used to prefer JavaScript for web development, but now I'm really into Python. 
    Recently I started learning machine learning and switched my focus to data science.
    My project status changed from planning to active development last week.
    """
    
    previous_summary = """
    User is a web developer who prefers JavaScript. Working on a web application project.
    """
    
    # Extract temporal markers
    markers = await tracker.extract_temporal_markers(current_content, previous_summary)
    
    print(f"   ✅ Extracted {len(markers)} temporal markers")
    
    # Verify marker types
    marker_types = [m.marker_type for m in markers]
    expected_types = ['change', 'preference_change', 'status_update']
    
    for expected_type in expected_types:
        if any(expected_type in mt for mt in marker_types):
            print(f"   ✅ Found {expected_type} marker")
        else:
            print(f"   ⚠️  Missing {expected_type} marker")
    
    # Test temporal evolution analysis
    analysis = tracker.analyze_temporal_evolution(markers)
    print(f"   ✅ Temporal analysis: {analysis['total_markers']} markers, confidence distribution: {analysis['confidence_distribution']}")
    
    return len(markers) > 0

async def test_topic_continuity_manager():
    """Test topic continuity management functionality."""
    print("🔍 Testing Topic Continuity Manager...")
    
    llm_client = MockLLMClient()
    db_client = MockDBClient()
    manager = TopicContinuityManager(llm_client, db_client)
    
    # Test content with multiple topics
    content = """
    I'm working on a Python machine learning project using scikit-learn.
    Also learning about neural networks and deep learning frameworks like TensorFlow.
    My team is using agile methodology for project management.
    """
    
    session_id = "test_session_123"
    
    # Extract topics
    topics = await manager.extract_topics_from_content(content, session_id)
    
    print(f"   ✅ Extracted {len(topics)} topics")
    
    # Verify topic categories
    topic_names = [t.name for t in topics]
    expected_categories = ['technology', 'work', 'learning']
    
    for category in expected_categories:
        if any(category in name for name in topic_names):
            print(f"   ✅ Found {category} related topic")
    
    # Test topic importance calculation
    for topic in topics:
        if topic.importance_score > 0:
            print(f"   ✅ Topic '{topic.name}' has importance score: {topic.importance_score:.3f}")
    
    # Test topic continuity score
    continuity_score = manager.get_topic_continuity_score(topics)
    print(f"   ✅ Topic continuity score: {continuity_score:.3f}")
    
    return len(topics) > 0

async def test_summary_quality_assessor():
    """Test summary quality assessment functionality."""
    print("🔍 Testing Summary Quality Assessor...")
    
    llm_client = MockLLMClient()
    assessor = SummaryQualityAssessor(llm_client)
    
    # Test summary
    summary = """
    User is a Python developer transitioning from web development to machine learning.
    Currently working on ML projects using scikit-learn and TensorFlow.
    Recently changed focus from JavaScript to Python development.
    Team uses agile methodology. Learning neural networks and deep learning.
    """
    
    original_content = """
    I used to do web development with JavaScript but now I'm really into Python.
    Started learning machine learning recently and working with scikit-learn.
    My team follows agile practices and I'm exploring TensorFlow for deep learning.
    """
    
    # Assess quality
    quality_score, quality_factors = await assessor.assess_summary_quality(
        summary, original_content
    )
    
    print(f"   ✅ Overall quality score: {quality_score:.3f}")
    print(f"   ✅ Completeness: {quality_factors.completeness_score:.3f}")
    print(f"   ✅ Coherence: {quality_factors.coherence_score:.3f}")
    print(f"   ✅ Relevance: {quality_factors.relevance_score:.3f}")
    print(f"   ✅ Temporal awareness: {quality_factors.temporal_awareness_score:.3f}")
    
    # Test adaptive length calculation
    conversation_complexity = 0.7
    content_length = len(original_content)
    adaptive_length = assessor.calculate_adaptive_length(
        conversation_complexity, content_length, quality_score
    )
    
    print(f"   ✅ Adaptive summary length: {adaptive_length} characters")
    
    return quality_score > 0.0

async def test_integration():
    """Test integration of all enhanced features."""
    print("🔍 Testing Enhanced Features Integration...")
    
    llm_client = MockLLMClient()
    db_client = MockDBClient()
    
    # Initialize all components
    temporal_tracker = TemporalContextTracker(llm_client)
    topic_manager = TopicContinuityManager(llm_client, db_client)
    quality_assessor = SummaryQualityAssessor(llm_client)
    
    # Test scenario: User conversation evolution
    conversation_content = """
    I've been working as a JavaScript developer for 3 years, but recently I've become 
    interested in machine learning. Last month I started learning Python and this week 
    I began working on my first ML project using scikit-learn. My goal is to transition 
    into a data science role within the next year. I'm also exploring TensorFlow and 
    considering taking an online course on deep learning.
    """
    
    previous_summary = """
    User is an experienced JavaScript developer with 3 years of experience.
    Primarily focused on web development projects.
    """
    
    # 1. Extract temporal markers
    temporal_markers = await temporal_tracker.extract_temporal_markers(
        conversation_content, previous_summary
    )
    print(f"   ✅ Temporal markers: {len(temporal_markers)}")
    
    # 2. Extract topics
    topics = await topic_manager.extract_topics_from_content(
        conversation_content, "session_integration_test"
    )
    print(f"   ✅ Topics identified: {len(topics)}")
    
    # 3. Generate enhanced summary (simulated)
    enhanced_summary = f"""
    User is transitioning from JavaScript web development (3 years experience) to machine learning.
    Recently started learning Python (last month) and began first ML project this week using scikit-learn.
    Goal: transition to data science role within next year.
    Currently exploring: TensorFlow, considering deep learning course.
    Career evolution: JavaScript developer → ML/Data Science transition in progress.
    """
    
    # 4. Assess summary quality
    quality_score, quality_factors = await quality_assessor.assess_summary_quality(
        enhanced_summary, conversation_content
    )
    print(f"   ✅ Enhanced summary quality: {quality_score:.3f}")
    
    # 5. Calculate conversation complexity
    complexity = 0.3  # Base
    if topics:
        complexity += min(len([t for t in topics if t.status == "active"]) * 0.1, 0.4)
    if temporal_markers:
        complexity += min(len(temporal_markers) * 0.05, 0.3)
    complexity = min(complexity, 1.0)
    
    print(f"   ✅ Conversation complexity: {complexity:.3f}")
    
    # 6. Calculate adaptive length
    adaptive_length = quality_assessor.calculate_adaptive_length(
        complexity, len(conversation_content), quality_score
    )
    print(f"   ✅ Recommended summary length: {adaptive_length} characters")
    
    return True

async def main():
    """Run all enhanced rolling summary tests."""
    print("🚀 Testing Enhanced Rolling Summary Features (Phase 2 Feature 3/3)")
    print("=" * 70)
    
    tests = [
        ("Temporal Context Tracker", test_temporal_context_tracker),
        ("Topic Continuity Manager", test_topic_continuity_manager),
        ("Summary Quality Assessor", test_summary_quality_assessor),
        ("Integration Test", test_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 50)
        
        try:
            result = await test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 TEST SUMMARY")
    print("=" * 70)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:.<40} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All Enhanced Rolling Summary Tests Passed!")
        print("✅ Phase 2 Feature 3/3: Enhanced Rolling Summary Features - IMPLEMENTED SUCCESSFULLY")
        print("✅ Ready to commit and complete Phase 2 development")
        return True
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please review implementation.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
