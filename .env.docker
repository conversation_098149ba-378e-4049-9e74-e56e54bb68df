# Docker Container Environment Configuration for Spark Memory MCP Server

# ==========================================
# MCP Server Configuration
# ==========================================

# The transport for the MCP server - 'sse' for web/HTTP or 'stdio' for direct communication
TRANSPORT=sse

# Host to bind to - use 0.0.0.0 for Docker containers to accept external connections
HOST=0.0.0.0

# Port to listen on - should match EXPOSE in Dockerfile
PORT=8050

# ==========================================
# LLM Configuration
# ==========================================

# The provider for your LLM - options: openai, openrouter, ollama, mock
LLM_PROVIDER=openrouter

# Base URL for the LLM API
# OpenAI: https://api.openai.com/v1
# Ollama (example): http://host.docker.internal:11434/v1 (for Docker)
# OpenRouter: https://openrouter.ai/api/v1
LLM_BASE_URL=https://openrouter.ai/api/v1

# API Key for LLM provider
# OpenAI: Get from https://platform.openai.com/api-keys
# OpenRouter: Get from https://openrouter.ai/keys
# Ollama: Usually not needed
LLM_API_KEY=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

# The LLM model to use
# OpenAI example: gpt-4o-mini, gpt-3.5-turbo
# OpenRouter example: anthropic/claude-3-5-sonnet
# Ollama example: qwen2.5:14b-instruct-8k
# Mock: any value (for testing)
LLM_CHOICE=google/gemini-2.5-flash-lite

# ==========================================
# BGE Embedding Configuration (NEW ARCHITECTURE)
# ==========================================

# BGE Embedding Server URL - this is the ACTUAL embedding system used
# For Docker: use host.docker.internal if BGE server is on host machine
# For local development: http://localhost:8080
# For production: http://your-bge-server:8080
BGE_SERVER_URL=http://************:8080

# ==========================================
# LEGACY EMBEDDING CONFIGURATION (UNUSED IN NEW ARCHITECTURE)
# ==========================================

# IMPORTANT: EMBEDDING_MODEL_CHOICE is NOT used in the new two-phase architecture
# The new system uses BGE (BAAI/bge-base-en-v1.5) exclusively via BGE_SERVER_URL
# This variable is kept for backward compatibility with utils.py but is ignored

# Legacy embedding model choice (IGNORED in main_new.py)
# OpenAI example: text-embedding-3-small
# Ollama example: nomic-embed-text
EMBEDDING_MODEL_CHOICE=text-embedding-3-small

# ==========================================
# Database Configuration
# ==========================================

# Supabase PostgreSQL connection
# For Docker: use host.docker.internal if Supabase is on host machine
# Format: postgresql://[user]:[password]@[host]:[port]/[database_name]
DATABASE_URL=**************************************************************************/postgres

# ==========================================
# Performance and Monitoring
# ==========================================

# Performance monitoring settings (optional)
LOCOMO_LATENCY_P50_TARGET=708
LOCOMO_LATENCY_P95_TARGET=1440
LOCOMO_TOKEN_TARGET=7000

# ==========================================
# Docker Network Configuration Notes
# ==========================================
# 
# When running in Docker:
# 1. Use host.docker.internal to access services on host machine
# 2. Use service names for Docker Compose services
# 3. Ensure exposed ports match internal ports
# 4. BGE server must be accessible from container
# 5. Supabase/PostgreSQL must be accessible from container
#
# Example Docker run command:
# docker run -p 8050:8050 --env-file .env.docker spark-mcp