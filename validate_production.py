#!/usr/bin/env python3
"""
Simple production validation script for the Spark Memory MCP server.
"""

import subprocess
import sys
import time


def check_container_status():
    """Check if the container is running and healthy."""
    print("🔍 Checking container status...")
    
    try:
        result = subprocess.run(
            ["docker", "ps", "--filter", "name=spark-mcp-server", "--format", "table {{.Names}}\t{{.Status}}"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        output = result.stdout.strip()
        print(f"Container status: {output}")
        
        if "spark-mcp-server" in output:
            if "healthy" in output:
                print("✅ Container is running and healthy")
                return True
            elif "unhealthy" in output:
                print("❌ Container is running but unhealthy")
                return False
            else:
                print("⚠️ Container is running (health status unknown)")
                return True
        else:
            print("❌ Container is not running")
            return False
            
    except Exception as e:
        print(f"❌ Failed to check container status: {e}")
        return False


def check_initialization_logs():
    """Check that all components initialized successfully."""
    print("\n🔍 Checking initialization logs...")
    
    try:
        result = subprocess.run(
            ["docker", "logs", "spark-mcp-server", "--tail", "50"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        logs = result.stdout + result.stderr
        
        required_components = [
            "Database service initialized successfully",
            "Initialized memory database",
            "Initialized LLM client",
            "Initialized rolling summary manager", 
            "Initialized performance monitor",
            "Initialized MemoryExtractionModule",
            "Initialized MemoryUpdateModule",
            "Initialized two-phase memory pipeline",
            "Spark Memory System initialized successfully",
            "service now fully available"
        ]
        
        print("Checking required components:")
        all_good = True
        for component in required_components:
            if component in logs:
                print(f"  ✅ {component}")
            else:
                print(f"  ❌ {component}")
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ Failed to check logs: {e}")
        return False


def check_external_dependencies():
    """Check external dependencies."""
    print("\n🔍 Checking external dependencies...")
    
    # Check BGE server
    try:
        import httpx
        import asyncio
        
        async def check_bge():
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get("http://************:8080/health")
                return response.status_code == 200
        
        bge_ok = asyncio.run(check_bge())
        if bge_ok:
            print("  ✅ BGE embedding server is accessible")
        else:
            print("  ❌ BGE embedding server is not accessible")
        
        return bge_ok
        
    except Exception as e:
        print(f"  ❌ BGE server check failed: {e}")
        return False


def check_coroutine_fix():
    """Check that coroutine serialization fix is working."""
    print("\n🔍 Checking coroutine serialization fix...")
    
    try:
        # Run the coroutine fix tests
        result = subprocess.run(
            [sys.executable, "tests/test_coroutine_serialization_fix.py"],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0 and "All coroutine serialization fix tests passed!" in result.stdout:
            print("  ✅ Coroutine serialization fix is working")
            return True
        else:
            print("  ❌ Coroutine serialization fix tests failed")
            print(f"  Output: {result.stdout}")
            print(f"  Error: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"  ❌ Coroutine fix test failed: {e}")
        return False


def main():
    """Main validation function."""
    print("🧪 Production Validation for Spark Memory MCP Server")
    print("=" * 60)
    
    tests = [
        ("Container Status", check_container_status),
        ("Component Initialization", check_initialization_logs),
        ("External Dependencies", check_external_dependencies),
        ("Coroutine Serialization Fix", check_coroutine_fix),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"Running {test_name}...")
        print(f"{'='*60}")
        
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("PRODUCTION VALIDATION SUMMARY")
    print("=" * 60)
    
    passed = 0
    failed = 0
    
    for test_name, result in results:
        status = "PASSED" if result else "FAILED"
        emoji = "✅" if result else "❌"
        print(f"{emoji} {test_name}: {status}")
        
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\nTotal Results: {passed} PASSED, {failed} FAILED")
    print(f"Success Rate: {passed}/{passed + failed} ({100 * passed / (passed + failed):.1f}%)")
    
    if failed == 0:
        print("\n🎉 ALL PRODUCTION VALIDATION TESTS PASSED!")
        print("✅ Production container is working correctly!")
        print("✅ All core components are operational!")
        print("✅ Coroutine serialization fix is validated!")
        print("✅ Service is ready for production use!")
        return 0
    else:
        print(f"\n❌ {failed} VALIDATION TEST(S) FAILED!")
        print("Please check the error details above and fix the issues.")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
